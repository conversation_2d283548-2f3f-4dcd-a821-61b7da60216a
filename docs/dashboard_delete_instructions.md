# How to Delete a User Using the Supabase Dashboard

If you're encountering the "Database error finding user" error when trying to register with an email that might already exist in the system, you can delete the user directly from the Supabase dashboard.

## Steps to Delete a User from the Dashboard:

1. Log in to your Supabase dashboard at https://app.supabase.com/
2. Select your project
3. Go to "Authentication" in the left sidebar
4. Click on "Users" tab
5. Search for the email address (<EMAIL>)
6. Click on the user to view their details
7. Click the "Delete User" button at the bottom of the user details panel
8. Confirm the deletion

## Alternative: Use the SQL Editor

If you prefer to use SQL, you can run the following script in the SQL Editor:

```sql
-- Check if the user exists
SELECT id, email, created_at FROM auth.users WHERE email = '<EMAIL>';

-- Delete the user directly
DELETE FROM auth.users WHERE email = '<EMAIL>';

-- Confirm the user is deleted
SELECT COUNT(*) FROM auth.users WHERE email = '<EMAIL>';
```

## After Deleting the User

After deleting the user, try registering again with the same email. The error should be resolved.

## If You Still Encounter Issues

If you still encounter issues after deleting the user, try running the `reset_auth_system.sql` script to fix permissions and ensure the auth system is properly configured.
