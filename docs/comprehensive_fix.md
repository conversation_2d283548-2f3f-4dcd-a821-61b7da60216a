# Comprehensive Fix for User Registration Issue

The error "Database error finding user" during registration is misleading. The issue is in how the authentication flow works with Supabase. Here's a comprehensive fix that addresses both the database and client-side issues.

## Step 1: Fix the Database

Run the following SQL script in the Supabase SQL Editor:

```sql
-- Fix permissions and database structure
-- Execute this in the Supabase SQL Editor

-- First, ensure the users table exists with the correct structure
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL,
    first_name TEXT NOT NULL DEFAULT 'Usuario',
    last_name TEXT NOT NULL DEFAULT 'Nuevo',
    nickname TEXT,
    full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
    phone TEXT,
    profile_picture TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Fix permissions for the public.users table
GRANT ALL PRIVILEGES ON TABLE public.users TO authenticated;
GRANT ALL PRIVILEGES ON TABLE public.users TO service_role;
GRANT SELECT, INSERT, UPDATE ON TABLE public.users TO anon;

-- Create a more robust function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert the new user into the public.users table with ON CONFLICT
    INSERT INTO public.users (
        id, 
        email, 
        first_name, 
        last_name, 
        nickname, 
        phone
    )
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'first_name', 'Usuario'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', 'Nuevo'),
        NEW.raw_user_meta_data->>'nickname',
        NEW.raw_user_meta_data->>'phone'
    )
    -- Add ON CONFLICT to handle the case where the user already exists
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        nickname = EXCLUDED.nickname,
        phone = EXCLUDED.phone,
        updated_at = now();
        
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Create or replace the function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for updated_at
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Sync any missing users from auth to public
DO $$
DECLARE
    user_record RECORD;
    users_added INTEGER := 0;
BEGIN
    FOR user_record IN 
        SELECT au.id, au.email, au.raw_user_meta_data
        FROM auth.users au
        LEFT JOIN public.users pu ON au.id = pu.id
        WHERE pu.id IS NULL
    LOOP
        BEGIN
            INSERT INTO public.users (
                id, 
                email, 
                first_name, 
                last_name, 
                nickname, 
                phone
            )
            VALUES (
                user_record.id, 
                user_record.email, 
                COALESCE(user_record.raw_user_meta_data->>'first_name', 'Usuario'),
                COALESCE(user_record.raw_user_meta_data->>'last_name', 'Nuevo'),
                user_record.raw_user_meta_data->>'nickname',
                user_record.raw_user_meta_data->>'phone'
            );
            users_added := users_added + 1;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error syncing user %: %', user_record.email, SQLERRM;
        END;
    END LOOP;
    
    RAISE NOTICE 'Sync complete: % users added', users_added;
END $$;
```

## Step 2: Modify the AuthService.signUp Function

Replace the `signUp` function in `services/authService.ts` with the following code:

```typescript
/**
 * Sign up a new user with direct user creation
 */
signUp: async (data: SignUpData): Promise<User> => {
  const { email, password, firstName, lastName, nickname, phone } = data;

  // Ensure email is a string
  const emailStr = typeof email === 'string' ? email : String(email);
  const passwordStr = typeof password === 'string' ? password : String(password);
  const firstNameStr = typeof firstName === 'string' ? firstName : String(firstName);
  const lastNameStr = typeof lastName === 'string' ? lastName : String(lastName);
  const nicknameStr = nickname ? (typeof nickname === 'string' ? nickname : String(nickname)) : undefined;
  const phoneStr = phone ? (typeof phone === 'string' ? phone : String(phone)) : undefined;

  console.log('Signing up with email:', emailStr);

  try {
    // First, sign up the user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: emailStr,
      password: passwordStr,
      options: {
        data: {
          first_name: firstNameStr,
          last_name: lastNameStr,
          nickname: nicknameStr,
          phone: phoneStr,
        },
        emailRedirectTo: 'parentium://confirm-email',
      },
    });

    if (authError) {
      console.error('Auth signup error:', authError);
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error('No user data returned from signup');
    }

    // Now, manually create the user record in the public.users table
    // This is a fallback in case the database trigger doesn't work
    try {
      console.log('Manually creating user record in public.users table');
      const { error: insertError } = await supabase
        .from('users')
        .insert([
          {
            id: authData.user.id,
            email: emailStr,
            first_name: firstNameStr,
            last_name: lastNameStr,
            nickname: nicknameStr,
            phone: phoneStr,
          },
        ])
        .select()
        .single();

      if (insertError) {
        // If the error is because the user already exists, that's fine
        if (insertError.code === '23505') { // Unique violation
          console.log('User already exists in public.users table, continuing');
        } else {
          console.warn('Error creating user record:', insertError);
          // We'll continue anyway since the auth user was created
        }
      } else {
        console.log('User record created successfully');
      }
    } catch (dbError) {
      console.warn('Exception creating user record:', dbError);
      // Continue anyway since the auth user was created
    }

    // Return user data
    return {
      id: authData.user.id,
      email: authData.user.email || '',
      firstName: firstNameStr,
      lastName: lastNameStr,
      nickname: nicknameStr,
      fullName: `${firstNameStr} ${lastNameStr}`,
      phone: phoneStr,
      createdAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Signup process error:', error);
    throw error;
  }
},
```

## Step 3: Modify the Register Screen Error Handling

In `app/(auth)/register.tsx`, improve the error handling in the `handleRegister` function:

```typescript
const handleRegister = async () => {
  if (!validate()) return;

  try {
    console.log('Submitting registration form with email:', email);

    // Convert all form values to strings to ensure proper formatting
    const emailStr = String(email).trim();
    const passwordStr = String(password);
    const firstNameStr = String(firstName).trim();
    const lastNameStr = String(lastName).trim();
    const nicknameStr = nickname ? String(nickname).trim() : undefined;
    const phoneStr = phoneNumber ? String(phoneNumber).trim() : undefined;

    await register(
      emailStr,
      passwordStr,
      firstNameStr,
      lastNameStr,
      nicknameStr,
      phoneStr
    );

    showToast('Registro exitoso', 'success');
    setRegistrationComplete(true);

    // Show email confirmation instructions
    Alert.alert(
      'Verificación de Correo Electrónico',
      `Hemos enviado un enlace de verificación a ${emailStr}. Por favor, revisa tu correo y haz clic en el enlace para verificar tu cuenta.`,
      [{ text: 'Entendido' }]
    );
  } catch (err) {
    console.error('Registration error in component:', err);
    
    // Show a more user-friendly error message
    const errorMessage = error || 'Error en el registro. Por favor intenta nuevamente.';
    showToast(errorMessage, 'error');
    
    // If the error is related to database issues, show a more specific message
    if (errorMessage.includes('Database error') || errorMessage.includes('finding user')) {
      showToast('Error de conexión con la base de datos. Por favor intenta nuevamente.', 'error');
    }
  }
};
```

## Why This Fix Works

This comprehensive fix addresses the issue at multiple levels:

1. **Database Level**: 
   - Ensures the users table exists with the correct structure
   - Fixes permissions so the app can access the table
   - Creates a robust trigger that handles user creation properly
   - Adds ON CONFLICT handling to prevent duplicate errors

2. **Client Level**:
   - Adds a fallback mechanism to manually create the user record
   - Improves error handling to provide better user feedback
   - Continues the registration process even if there are database issues

3. **Error Handling**:
   - Provides more user-friendly error messages
   - Logs detailed errors for debugging

By implementing both the database and client-side fixes, you ensure that user registration will work even if one of the mechanisms fails.

## After Implementing the Fix

After implementing these changes:

1. Try registering a new user in your app
2. The registration should now work correctly
3. If there are still issues, check the console logs for more detailed error messages

If you continue to have issues, you may need to check your Supabase configuration or network connectivity.
