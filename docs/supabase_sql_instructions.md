# Supabase SQL Instructions

Follow these steps to create the necessary tables in your Supabase project:

## 1. Enable UUID Extension

First, enable the UUID extension by running this SQL:

```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

## 2. Create Announcements Table

```sql
-- Create announcements table
CREATE TABLE IF NOT EXISTS public.announcements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL,
    date TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies
ALTER TABLE public.announcements ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select announcements for groups they are members of
CREATE POLICY "Users can view announcements for their groups" ON public.announcements
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = announcements.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert announcements for groups they are members of
CREATE POLICY "Users can create announcements for their groups" ON public.announcements
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = announcements.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own announcements
CREATE POLICY "Users can update their own announcements" ON public.announcements
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own announcements
CREATE POLICY "Users can delete their own announcements" ON public.announcements
    FOR DELETE
    USING (created_by = auth.uid());
```

## 3. Create Colectas Tables

```sql
-- Create colectas table
CREATE TABLE IF NOT EXISTS public.colectas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    motivo TEXT NOT NULL,
    descripcion TEXT NOT NULL,
    fecha_fin TIMESTAMP WITH TIME ZONE NOT NULL,
    base_amount DECIMAL(10, 2) NOT NULL,
    total_collected DECIMAL(10, 2) DEFAULT 0 NOT NULL,
    total_contributions INTEGER DEFAULT 0 NOT NULL,
    account_info TEXT NOT NULL,
    status TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create contributions table
CREATE TABLE IF NOT EXISTS public.contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    colecta_id UUID NOT NULL REFERENCES public.colectas(id) ON DELETE CASCADE,
    kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
    amount DECIMAL(10, 2) NOT NULL,
    payment_method TEXT NOT NULL,
    payment_reference TEXT,
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies for colectas
ALTER TABLE public.colectas ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select colectas for groups they are members of
CREATE POLICY "Users can view colectas for their groups" ON public.colectas
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = colectas.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert colectas for groups they are members of
CREATE POLICY "Users can create colectas for their groups" ON public.colectas
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = colectas.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own colectas
CREATE POLICY "Users can update their own colectas" ON public.colectas
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own colectas
CREATE POLICY "Users can delete their own colectas" ON public.colectas
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for contributions
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select contributions for colectas in their groups
CREATE POLICY "Users can view contributions for their groups" ON public.contributions
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.colectas
            JOIN public.group_memberships ON colectas.group_id = group_memberships.group_id
            WHERE colectas.id = contributions.colecta_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert contributions for their kids
CREATE POLICY "Users can create contributions for their kids" ON public.contributions
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to update their own contributions
CREATE POLICY "Users can update their own contributions" ON public.contributions
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to delete their own contributions
CREATE POLICY "Users can delete their own contributions" ON public.contributions
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );
```

## 4. Create Polls Tables

```sql
-- Create polls table
CREATE TABLE IF NOT EXISTS public.polls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create poll options table
CREATE TABLE IF NOT EXISTS public.poll_options (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poll_id UUID NOT NULL REFERENCES public.polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create votes table
CREATE TABLE IF NOT EXISTS public.votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poll_id UUID NOT NULL REFERENCES public.polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES public.poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(poll_id, user_id)
);

-- Add RLS policies for polls
ALTER TABLE public.polls ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select polls for groups they are members of
CREATE POLICY "Users can view polls for their groups" ON public.polls
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = polls.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert polls for groups they are members of
CREATE POLICY "Users can create polls for their groups" ON public.polls
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = polls.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own polls
CREATE POLICY "Users can update their own polls" ON public.polls
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own polls
CREATE POLICY "Users can delete their own polls" ON public.polls
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for poll options
ALTER TABLE public.poll_options ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select poll options for polls in their groups
CREATE POLICY "Users can view poll options for their groups" ON public.poll_options
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = poll_options.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert poll options for their own polls
CREATE POLICY "Users can create poll options for their own polls" ON public.poll_options
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Policy to allow users to update poll options for their own polls
CREATE POLICY "Users can update poll options for their own polls" ON public.poll_options
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Policy to allow users to delete poll options for their own polls
CREATE POLICY "Users can delete poll options for their own polls" ON public.poll_options
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Add RLS policies for votes
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select votes for polls in their groups
CREATE POLICY "Users can view votes for their groups" ON public.votes
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = votes.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert their own votes
CREATE POLICY "Users can create their own votes" ON public.votes
    FOR INSERT
    WITH CHECK (
        user_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = votes.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own votes
CREATE POLICY "Users can update their own votes" ON public.votes
    FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Policy to allow users to delete their own votes
CREATE POLICY "Users can delete their own votes" ON public.votes
    FOR DELETE
    USING (user_id = auth.uid());
```

## 5. Create Events Tables

```sql
-- Create events table
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create event attendees table
CREATE TABLE IF NOT EXISTS public.event_attendees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(event_id, kid_id)
);

-- Add RLS policies for events
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select events for groups they are members of
CREATE POLICY "Users can view events for their groups" ON public.events
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = events.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert events for groups they are members of
CREATE POLICY "Users can create events for their groups" ON public.events
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = events.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own events
CREATE POLICY "Users can update their own events" ON public.events
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own events
CREATE POLICY "Users can delete their own events" ON public.events
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for event attendees
ALTER TABLE public.event_attendees ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select event attendees for events in their groups
CREATE POLICY "Users can view event attendees for their groups" ON public.event_attendees
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.events
            JOIN public.group_memberships ON events.group_id = group_memberships.group_id
            WHERE events.id = event_attendees.event_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert event attendees for their kids
CREATE POLICY "Users can create event attendees for their kids" ON public.event_attendees
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to update event attendees for their kids
CREATE POLICY "Users can update event attendees for their kids" ON public.event_attendees
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to delete event attendees for their kids
CREATE POLICY "Users can delete event attendees for their kids" ON public.event_attendees
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );
```

## 6. Verify Tables

After executing all the SQL scripts, go to the "Table Editor" in the Supabase Dashboard to verify that all tables were created successfully.
