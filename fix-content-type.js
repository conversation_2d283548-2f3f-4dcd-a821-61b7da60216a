const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')

// Your Supabase URL and key
const supabaseUrl = 'https://jbvwbuzrqzprapjtdcsc.supabase.co'
// You'll need to replace this with your actual service key from Supabase dashboard
// Go to Project Settings > API > service_role key
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpidndidXpycXpwcmFwanRkY3NjIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDE2NjQzNSwiZXhwIjoyMDU5NzQyNDM1fQ.XtnojPSsiThGRKsQNlJQig_nGAHGAGB3EBVcFkGNS1U' // Replace with your actual service key

const supabase = createClient(supabaseUrl, supabaseKey)

async function fixContentType() {
  try {
    // Read the HTML file
    const fileContent = fs.readFileSync('public/auth-redirect.html')

    // Delete the existing file if it exists
    const { error: deleteError } = await supabase
      .storage
      .from('publicbucket')
      .remove(['auth-redirect.html'])

    if (deleteError) {
      console.error('Error deleting file:', deleteError)
    } else {
      console.log('Existing file deleted successfully')
    }

    // Upload the file with the correct Content-Type
    const { data, error } = await supabase
      .storage
      .from('publicbucket')
      .upload('auth-redirect.html', fileContent, {
        contentType: 'text/html',
        upsert: true,
        cacheControl: '3600'
      })

    if (error) {
      console.error('Error uploading file:', error)
    } else {
      console.log('File uploaded successfully with Content-Type: text/html')
      console.log('File URL:', supabaseUrl + '/storage/v1/object/public/publicbucket/auth-redirect.html')
    }
  } catch (error) {
    console.error('Error:', error)
  }
}

fixContentType()
