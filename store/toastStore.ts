import { create } from 'zustand';

type ToastType = 'success' | 'error' | 'info';

interface ToastState {
  visible: boolean;
  message: string;
  type: ToastType;
  
  // Actions
  showToast: (message: string, type?: ToastType, duration?: number) => void;
  hideToast: () => void;
}

export const useToastStore = create<ToastState>((set, get) => ({
  visible: false,
  message: '',
  type: 'info',
  
  showToast: (message: string, type: ToastType = 'info', duration: number = 3000) => {
    set({ visible: true, message, type });
    
    // Auto-hide after duration
    if (duration > 0) {
      setTimeout(() => {
        // Only hide if this is still the same toast
        if (get().message === message) {
          get().hideToast();
        }
      }, duration);
    }
  },
  
  hideToast: () => {
    set({ visible: false });
  },
}));