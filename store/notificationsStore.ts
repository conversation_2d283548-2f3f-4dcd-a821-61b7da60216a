import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-get-random-values'; // Import this polyfill before using UUID
// @ts-ignore - Import path issue
import { Notification, NotificationType } from '@/types';
import { useAuthStore } from './authStore';
import { NotificationsService } from '@/services/notificationsService';
// @ts-ignore - Import path issue
import { generateUuid } from '@/utils/uuid';

interface NotificationInput {
  type: NotificationType;
  groupId?: string;
  colectaId?: string;
  pollId?: string;
  eventId?: string;
  message: string;
}

interface NotificationsState {
  notifications: Notification[];
  isLoading: boolean;
  error: string | null;

  // Actions
  getNotifications: () => Promise<Notification[]>;
  addNotification: (notification: NotificationInput) => Promise<Notification>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (id: string) => Promise<void>;
  clearError: () => void;
}

export const useNotificationsStore = create<NotificationsState>()(
  persist(
    (set, get) => ({
      notifications: [],
      isLoading: false,
      error: null,

      getNotifications: async (forceRefresh = false) => {
        const { currentUser } = useAuthStore.getState();
        if (!currentUser) {
          set({ notifications: [], isLoading: false, error: null });
          return [];
        }

        set({ isLoading: true, error: null });

        try {
          // If we already have notifications and not forcing a refresh, just return them
          const { notifications } = get();
          if (!forceRefresh && notifications && notifications.length > 0) {
            set({ isLoading: false });
            return notifications;
          }

          console.log('Fetching notifications from service...');
          // Get notifications from the service
          const fetchedNotifications = await NotificationsService.getNotificationsByUserId(currentUser.id);

          console.log(`Fetched ${fetchedNotifications.length} notifications`);

          set({ notifications: fetchedNotifications, isLoading: false });
          return fetchedNotifications;
        } catch (error) {
          console.error('Error fetching notifications:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar las notificaciones. Intenta nuevamente.',
            isLoading: false,
            notifications: []
          });
          return [];
        }
      },

      addNotification: async (notification: NotificationInput) => {
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          const newNotification: Notification = {
            id: generateUuid(),
            userId: currentUser.id,
            groupId: notification.groupId,
            colectaId: notification.colectaId,
            pollId: notification.pollId,
            eventId: notification.eventId,
            type: notification.type,
            message: notification.message,
            status: 'UNREAD',
            createdAt: new Date().toISOString(),
          };

          set(state => ({
            notifications: [newNotification, ...(state.notifications || [])]
          }));

          return newNotification;
        } catch (error) {
          console.error('Failed to add notification:', error);
          throw error;
        }
      },

      markAsRead: async (id: string) => {
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Mark notification as read in the service
          await NotificationsService.markAsRead(id);

          // Update local state
          set(state => ({
            notifications: state.notifications.map(notification =>
              notification.id === id
                ? { ...notification, status: 'READ' }
                : notification
            )
          }));
        } catch (error) {
          console.error('Failed to mark notification as read:', error);
          throw error;
        }
      },

      markAllAsRead: async () => {
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Get unread notifications
          const { notifications } = get();
          const unreadNotifications = notifications.filter(n => n.status === 'UNREAD');

          if (unreadNotifications.length === 0) {
            return; // No unread notifications to mark
          }

          // Mark all notifications as read in the service
          await NotificationsService.markAllAsRead(currentUser.id);

          // Update local state
          set(state => ({
            notifications: state.notifications.map(notification =>
              ({ ...notification, status: 'READ' })
            )
          }));
        } catch (error) {
          console.error('Failed to mark all notifications as read:', error);
          throw error;
        }
      },

      deleteNotification: async (id: string) => {
        try {
          set(state => ({
            notifications: state.notifications.filter(notification => notification.id !== id)
          }));
        } catch (error) {
          console.error('Failed to delete notification:', error);
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'notifications-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);