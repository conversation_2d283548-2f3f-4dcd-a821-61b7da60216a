import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from './authStore';
import { GroupsService } from '@/services/groupsService';
import { supabase } from '@/lib/supabase';

// Flags to track state
let groupsDataCachedLogged = false;

export interface Group {
  id: string;
  institutionId: string;
  institutionName?: string; // For display purposes
  sala: string;
  año: number;
  nombre?: string;
  createdBy: string;
  createdAt: string;
  members: string[]; // User IDs
  kids: string[]; // Kid IDs
}

export interface GroupMembership {
  id: string;
  groupId: string;
  userId: string;
  kidId?: string;
  isReferente: boolean;
  createdAt: string;
}

interface GroupsState {
  allGroups: Group[];
  myGroups: Group[];
  memberships: GroupMembership[];
  schools: string[] | null;
  isLoading: boolean;
  isCreatingGroup: boolean;
  schoolsLoading: boolean;
  error: string | null;

  // Actions
  getGroups: () => Promise<Group[]>;
  getGroupById: (id: string) => Group | undefined;
  getMembershipsByGroupId: (groupId: string) => GroupMembership[];
  isReferente: (groupId: string) => boolean;
  createGroup: (institutionName: string, sala: string, año: number, kidId: string, nombre?: string, institutionAddress?: string) => Promise<Group>;
  joinGroup: (groupId: string, kidIds: string[]) => Promise<void>;
  leaveGroup: (groupId: string) => Promise<void>;
  searchGroups: (query: string) => Promise<Group[]>;
  searchSchools: (query: string) => Promise<string[]>;
  toggleReferenteStatus: (membershipId: string, isReferente: boolean) => Promise<void>;
  removeMember: (membershipId: string) => Promise<void>;
  clearError: () => void;
  clearUserData: () => void;
}

// Mock API functions
const mockGetGroups = async (userId: string): Promise<{ allGroups: Group[], myGroups: Group[], memberships: GroupMembership[] }> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // Return mock data
  const allGroups = [
    {
      id: '1',
      institutionId: 'inst-1',
      institutionName: 'Colegio San Martín',
      sala: 'Sala Roja',
      año: 2024,
      createdBy: '1',
      createdAt: '2023-12-15T10:30:00Z',
      members: ['1', '2'],
      kids: ['1'],
    },
    {
      id: '2',
      institutionId: 'inst-2',
      institutionName: 'Escuela Primaria 25',
      sala: '3er Grado',
      año: 2024,
      nombre: 'Turno Mañana',
      createdBy: '2',
      createdAt: '2023-12-10T14:20:00Z',
      members: ['2'],
      kids: [],
    },
    {
      id: '3',
      institutionId: 'inst-3',
      institutionName: 'Instituto Belgrano',
      sala: '5to Grado',
      año: 2024,
      createdBy: '3',
      createdAt: '2023-12-05T09:15:00Z',
      members: ['3'],
      kids: [],
    },
  ];

  // Mock memberships data
  const memberships: GroupMembership[] = [
    {
      id: '1',
      groupId: '1',
      userId: '1',
      kidId: '1',
      isReferente: true,
      createdAt: '2023-12-15T10:30:00Z',
    },
    {
      id: '2',
      groupId: '1',
      userId: '2',
      isReferente: false,
      createdAt: '2023-12-16T11:20:00Z',
    },
    {
      id: '3',
      groupId: '2',
      userId: '2',
      isReferente: true,
      createdAt: '2023-12-10T14:20:00Z',
    },
    {
      id: '4',
      groupId: '3',
      userId: '3',
      isReferente: true,
      createdAt: '2023-12-05T09:15:00Z',
    },
  ];

  // Filter my groups
  const myGroups = allGroups.filter(group => group.members.includes(userId));

  return { allGroups, myGroups, memberships };
};

const mockCreateGroup = async (
  userId: string,
  institutionName: string,
  sala: string,
  año: number,
  nombre?: string
): Promise<Group> => {
  await new Promise(resolve => setTimeout(resolve, 1000));

  return {
    id: Math.random().toString(36).substring(2, 9),
    institutionId: 'mock-institution-id',
    institutionName,
    sala,
    año,
    nombre,
    createdBy: userId,
    createdAt: new Date().toISOString(),
    members: [userId],
    kids: [],
  };
};

const mockJoinGroup = async (groupId: string, userId: string, kidIds: string[]): Promise<void> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
};

const mockLeaveGroup = async (groupId: string, userId: string): Promise<void> => {
  await new Promise(resolve => setTimeout(resolve, 1000));
};

const mockSearchGroups = async (query: string): Promise<Group[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // Return mock search results
  return [
    {
      id: '4',
      institutionId: 'mock-institution-1',
      institutionName: 'Colegio Nacional',
      sala: '2do Grado',
      año: 2024,
      createdBy: '4',
      createdAt: '2023-11-20T11:30:00Z',
      members: ['4'],
      kids: [],
    },
    {
      id: '5',
      institutionId: 'mock-institution-2',
      institutionName: 'Escuela Técnica 5',
      sala: '1er Año',
      año: 2024,
      nombre: 'División A',
      createdBy: '5',
      createdAt: '2023-11-15T08:45:00Z',
      members: ['5'],
      kids: [],
    },
  ];
};

const mockSearchSchools = async (query: string): Promise<string[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));

  // Return mock school search results
  return [
    'Colegio San Martín',
    'Colegio Nacional',
    'Colegio Santa María',
    'Colegio Nuestra Señora',
    'Colegio Mariano Moreno',
  ].filter(school => school.toLowerCase().includes(query.toLowerCase()));
};

export const useGroupsStore = create<GroupsState>()(
  persist(
    (set, get) => ({
      allGroups: [],
      myGroups: [],
      memberships: [],
      schools: null,
      isLoading: false,
      isCreatingGroup: false,
      schoolsLoading: false,
      error: null,

      getGroups: async (forceRefresh = false) => {
        // Check if we already have groups data to avoid unnecessary loading state
        const currentGroups = get().allGroups;
        const currentlyLoading = get().isLoading;

        // If we're already loading, return current groups
        if (currentlyLoading) {
          console.log('GroupsStore: Already loading groups, returning current groups');
          return get().myGroups;
        }

        // If we have groups and not forcing a refresh, return current groups
        if (currentGroups.length > 0 && !forceRefresh) {
          // Only log this once per session to reduce noise
          if (!groupsDataCachedLogged) {
            console.log('GroupsStore: Using cached groups data');
            groupsDataCachedLogged = true;
          }
          return get().myGroups;
        }

        const shouldShowLoading = currentGroups.length === 0;

        if (shouldShowLoading) {
          set({ isLoading: true, error: null });
        }

        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          console.log('GroupsStore: Fetching groups...');
          // Get groups from Supabase
          const { allGroups, myGroups } = await GroupsService.getGroups(currentUser.id);
          console.log(`GroupsStore: Fetched ${allGroups.length} groups`);

          // If no groups, no need to fetch memberships
          if (allGroups.length === 0) {
            set({ allGroups: [], myGroups: [], memberships: [], isLoading: false });
            return [];
          }

          // Get all memberships in a single query instead of one per group
          const groupIds = allGroups.map(g => g.id);

          console.log('GroupsStore: Fetching memberships...');
          const { data: membershipData, error: membershipError } = await supabase
            .from('group_memberships')
            .select('id, group_id, user_id, kid_id, is_referente, created_at')
            .in('group_id', groupIds);

          if (membershipError) {
            console.error('GroupsStore: Error fetching memberships:', membershipError);
            throw new Error(membershipError.message);
          }

          // Transform memberships
          const memberships: GroupMembership[] = (membershipData || []).map(m => ({
            id: m.id,
            groupId: m.group_id,
            userId: m.user_id,
            kidId: m.kid_id,
            isReferente: m.is_referente,
            createdAt: m.created_at,
          }));

          console.log(`GroupsStore: Fetched ${memberships.length} memberships`);

          // Only update state if the data has changed
          const currentState = {
            allGroups: get().allGroups,
            myGroups: get().myGroups,
            memberships: get().memberships
          };
          const newState = { allGroups, myGroups, memberships };

          if (JSON.stringify(newState) !== JSON.stringify(currentState)) {
            console.log('GroupsStore: Data changed, updating state');
            set({ ...newState, isLoading: false });
          } else {
            console.log('GroupsStore: Data unchanged, not updating state');
            set({ isLoading: false });
          }

          return myGroups;
        } catch (error) {
          console.error('GroupsStore: Error in getGroups:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to get groups',
            isLoading: false,
            // Don't clear existing data on error
            allGroups: get().allGroups,
            myGroups: get().myGroups,
            memberships: get().memberships
          });
          return get().myGroups;
        }
      },

      getGroupById: (id) => {
        const { allGroups } = get();
        return allGroups ? allGroups.find(group => group.id === id) : undefined;
      },

      getMembershipsByGroupId: (groupId) => {
        const { memberships } = get();
        return memberships ? memberships.filter(membership => membership.groupId === groupId) : [];
      },

      isReferente: (groupId) => {
        const { currentUser } = useAuthStore.getState();
        const { memberships } = get();
        if (!currentUser || !memberships) return false;

        return memberships.some(membership =>
          membership.groupId === groupId &&
          membership.userId === currentUser.id &&
          membership.isReferente
        );
      },

      createGroup: async (institutionName, sala, año, kidId, nombre, institutionAddress) => {
        set({ isCreatingGroup: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Create group using GroupsService
          const newGroup = await GroupsService.createGroup(currentUser.id, {
            institutionName,
            institutionAddress,
            sala,
            año,
            nombre,
            kidId,
          });

          // Create a new membership object
          const newMembership: GroupMembership = {
            id: `temp-${Date.now()}`,
            groupId: newGroup.id,
            userId: currentUser.id,
            kidId,
            isReferente: true,
            createdAt: new Date().toISOString(),
          };

          // Update state with new group and membership
          set(state => ({
            allGroups: [...state.allGroups, newGroup],
            myGroups: [...state.myGroups, newGroup],
            memberships: [...state.memberships, newMembership],
            isCreatingGroup: false
          }));

          return newGroup;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to create group', isCreatingGroup: false });
          throw error;
        }
      },

      joinGroup: async (groupId, kidIds) => {
        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Join group using GroupsService
          await GroupsService.joinGroup(groupId, currentUser.id, kidIds);

          // Update state with joined group
          const { allGroups } = get();
          const groupToJoin = allGroups.find(group => group.id === groupId);

          if (!groupToJoin) {
            throw new Error('Group not found');
          }

          // Add user to members and kids to kids array
          const updatedGroup = {
            ...groupToJoin,
            members: [...groupToJoin.members, currentUser.id],
            kids: [...groupToJoin.kids, ...kidIds],
          };

          // Update allGroups
          const updatedAllGroups = allGroups.map(group =>
            group.id === groupId ? updatedGroup : group
          );

          // Update myGroups
          const { myGroups } = get();
          const updatedMyGroups = [...myGroups, updatedGroup];

          // Create new memberships
          const newMemberships: GroupMembership[] = [];

          // Add kid memberships (one per kid)
          kidIds.forEach(kidId => {
            const kidMembership: GroupMembership = {
              id: `temp-${Date.now()}-${kidId}`,
              groupId,
              userId: currentUser.id,
              kidId,
              isReferente: false,
              createdAt: new Date().toISOString(),
            };
            newMemberships.push(kidMembership);
          });

          // Update memberships
          const { memberships } = get();
          const updatedMemberships = [...memberships, ...newMemberships];

          set({
            allGroups: updatedAllGroups,
            myGroups: updatedMyGroups,
            memberships: updatedMemberships,
            isLoading: false
          });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to join group', isLoading: false });
          throw error;
        }
      },

      leaveGroup: async (groupId) => {
        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Leave group using GroupsService
          await GroupsService.leaveGroup(groupId, currentUser.id);

          // Update state by removing user from group members
          const { allGroups, myGroups, memberships } = get();

          // Update allGroups
          const updatedAllGroups = allGroups.map(group => {
            if (group.id === groupId) {
              return {
                ...group,
                members: group.members.filter(id => id !== currentUser.id),
                // Remove user's kids
                kids: group.kids.filter(kidId => {
                  // Check if this kid belongs to the current user
                  const kidMembership = memberships.find(m =>
                    m.groupId === groupId &&
                    m.kidId === kidId &&
                    m.userId === currentUser.id
                  );
                  return !kidMembership; // Keep kids that don't belong to this user
                }),
              };
            }
            return group;
          });

          // Remove from myGroups
          const updatedMyGroups = myGroups.filter(group => group.id !== groupId);

          // Remove memberships for this group and user
          const updatedMemberships = memberships.filter(m =>
            !(m.groupId === groupId && m.userId === currentUser.id)
          );

          set({
            allGroups: updatedAllGroups,
            myGroups: updatedMyGroups,
            memberships: updatedMemberships,
            isLoading: false
          });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to leave group', isLoading: false });
          throw error;
        }
      },

      searchGroups: async (query = '', forceRefresh = false) => {
        // If we're already loading, don't start another search
        if (get().isLoading) {
          console.log('GroupsStore: Already loading, skipping search');
          return get().allGroups;
        }

        // If we have groups and no query and not forcing a refresh, just return the current groups
        const currentGroups = get().allGroups;
        if (currentGroups.length > 0 && !query && !forceRefresh) {
          // Only log this once per session to reduce noise
          if (!groupsDataCachedLogged) {
            console.log('GroupsStore: Using cached groups for empty query');
          }
          return currentGroups;
        }

        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          console.log(`GroupsStore: Searching groups with query: "${query}"`);
          // If no query, just get all groups
          if (!query) {
            return await get().getGroups(forceRefresh);
          }

          // Search groups using GroupsService
          const results = await GroupsService.searchGroups(query);
          console.log(`GroupsStore: Search returned ${results.length} results`);
          set({ isLoading: false });
          return results;
        } catch (error) {
          console.error('GroupsStore: Error searching groups:', error);
          set({ error: error instanceof Error ? error.message : 'Failed to search groups', isLoading: false });
          return currentGroups; // Return current groups on error
        }
      },

      searchSchools: async (query) => {
        set({ schoolsLoading: true, error: null });
        try {
          // Search schools using GroupsService
          const schools = await GroupsService.searchSchools(query);
          set({ schools, schoolsLoading: false });
          return schools;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to search schools',
            schoolsLoading: false,
            schools: []
          });
          return [];
        }
      },

      clearError: () => {
        set({ error: null });
      },

      clearUserData: () => {
        console.log('GroupsStore: Clearing user data');
        set({
          allGroups: [],
          myGroups: [],
          memberships: [],
          schools: null,
          error: null,
        });
      },

      // Get a group by ID
      getGroupById: (id: string) => {
        return get().allGroups.find(group => group.id === id);
      },

      // Get memberships for a specific group
      getMembershipsByGroupId: (groupId: string) => {
        return get().memberships.filter(membership => membership.groupId === groupId);
      },

      // Check if the current user is a referente for a group
      isReferente: (groupId: string) => {
        const { currentUser } = useAuthStore.getState();
        if (!currentUser) return false;

        return get().memberships.some(
          membership =>
            membership.groupId === groupId &&
            membership.userId === currentUser.id &&
            membership.isReferente
        );
      },

      // Toggle referente status for a membership
      toggleReferenteStatus: async (membershipId: string, isReferente: boolean) => {
        try {
          // Find the membership
          const membership = get().memberships.find(m => m.id === membershipId);
          if (!membership) throw new Error('Membership not found');

          // Update the membership in Supabase
          const { error } = await supabase
            .from('group_memberships')
            .update({ is_referente: isReferente })
            .eq('id', membershipId);

          if (error) throw new Error(error.message);

          // Update the membership in state
          const updatedMemberships = get().memberships.map(m =>
            m.id === membershipId ? { ...m, isReferente } : m
          );

          set({ memberships: updatedMemberships });
        } catch (error) {
          console.error('Error toggling referente status:', error);
          throw error;
        }
      },

      // Remove a member from a group
      removeMember: async (membershipId: string) => {
        try {
          // Find the membership
          const membership = get().memberships.find(m => m.id === membershipId);
          if (!membership) throw new Error('Membership not found');

          // Delete the membership from Supabase
          const { error } = await supabase
            .from('group_memberships')
            .delete()
            .eq('id', membershipId);

          if (error) throw new Error(error.message);

          // Update the memberships in state
          const updatedMemberships = get().memberships.filter(m => m.id !== membershipId);

          // Update the groups in state
          const { allGroups, myGroups } = get();
          const updatedAllGroups = allGroups.map(group => {
            if (group.id === membership.groupId) {
              // Remove the user from members if they have no other memberships in this group
              const userHasOtherMemberships = updatedMemberships.some(
                m => m.groupId === group.id && m.userId === membership.userId
              );

              const updatedMembers = userHasOtherMemberships
                ? group.members
                : group.members.filter(id => id !== membership.userId);

              // Remove the kid from kids
              const updatedKids = membership.kidId
                ? group.kids.filter(id => id !== membership.kidId)
                : group.kids;

              return {
                ...group,
                members: updatedMembers,
                kids: updatedKids,
              };
            }
            return group;
          });

          // Update myGroups accordingly
          const updatedMyGroups = myGroups.map(group => {
            const updatedGroup = updatedAllGroups.find(g => g.id === group.id);
            return updatedGroup || group;
          });

          set({
            memberships: updatedMemberships,
            allGroups: updatedAllGroups,
            myGroups: updatedMyGroups,
          });
        } catch (error) {
          console.error('Error removing member:', error);
          throw error;
        }
      },
    }),
    {
      name: 'groups-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        console.log('GroupsStore: Rehydrated from storage');
        // Ensure isLoading is always false when the store is rehydrated
        if (state) {
          state.isLoading = false;
          state.schoolsLoading = false;
        }
      },
      // Only persist the groups and memberships, not the loading states or error
      onRehydrateStorage: () => (state) => {
        console.log('GroupsStore: Rehydrated from storage');
        // Ensure loading states are reset when the store is rehydrated
        if (state) {
          state.isLoading = false;
          state.schoolsLoading = false;
        }
      },
      partialize: (state) => ({
        allGroups: state.allGroups,
        myGroups: state.myGroups,
        memberships: state.memberships,
        schools: state.schools,
      }),
    }
  )
);