/**
 * Polls Store
 *
 * Manages the state and operations for polls (votaciones) in the application.
 * <PERSON>les creating, updating, publishing, closing, and voting on polls.
 *
 * Features:
 * - Poll CRUD operations
 * - Poll status management (draft, active, closed)
 * - Voting functionality
 * - User vote tracking
 * - Persistence with AsyncStorage
 *
 * @store
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import { Poll, PollOption, PollStatus, Vote } from '@/types';
import { useAuthStore } from './authStore';
import { useNotificationsStore } from './notificationsStore';
import { PollsService } from '@/services/pollsService';

interface PollsState {
  /** List of all polls */
  polls: Poll[];
  /** List of all votes */
  votes: Vote[];
  /** Loading state for async operations */
  isLoading: boolean;
  /** Error message if any operation fails */
  error: string | null;

  // Actions
  /**
   * Get all polls for a specific group
   * @param groupId - ID of the group
   * @returns Promise resolving to array of polls
   */
  getPollsByGroupId: (groupId: string) => Promise<Poll[]>;

  /**
   * Get a specific poll by ID
   * @param id - ID of the poll
   * @returns Poll object or null if not found
   */
  getPollById: (id: string) => Poll | null;

  /**
   * Create a new poll (in draft status)
   * @param groupId - ID of the group
   * @param title - Poll title
   * @param description - Poll description
   * @param options - Array of option texts
   * @param endDate - ISO date string for poll end date
   * @param allowMultipleChoice - Whether to allow multiple choice selection
   * @returns Promise resolving to the created poll
   */
  createPoll: (
    groupId: string,
    title: string,
    description: string,
    options: string[],
    endDate: string,
    allowMultipleChoice?: boolean,
  ) => Promise<Poll>;

  /**
   * Update an existing poll (draft only)
   * @param pollId - ID of the poll to update
   * @param title - New title (optional)
   * @param description - New description (optional)
   * @param options - New options array (optional)
   * @param endDate - New end date (optional)
   * @returns Promise resolving to the updated poll
   */
  updatePoll: (
    pollId: string,
    title?: string,
    description?: string,
    options?: string[],
    endDate?: string,
  ) => Promise<Poll>;

  /**
   * Publish a draft poll (changes status to ACTIVE)
   * @param pollId - ID of the poll to publish
   * @returns Promise resolving to the published poll
   */
  publishPoll: (pollId: string) => Promise<Poll>;

  /**
   * Close an active poll (changes status to CLOSED)
   * @param pollId - ID of the poll to close
   * @returns Promise resolving to the closed poll
   */
  closePoll: (pollId: string) => Promise<Poll>;

  /**
   * Delete a poll and its votes
   * @param pollId - ID of the poll to delete
   * @returns Promise resolving when deletion is complete
   */
  deletePoll: (pollId: string) => Promise<void>;

  /**
   * Register a vote on a poll
   * @param pollId - ID of the poll
   * @param optionId - ID of the selected option
   * @returns Promise resolving when vote is registered
   */
  voteOnPoll: (pollId: string, optionId: string) => Promise<void>;

  /**
   * Get the current user's vote on a poll
   * @param pollId - ID of the poll
   * @returns Vote object or null if user hasn't voted
   */
  getUserVote: (pollId: string) => Vote | null;

  /**
   * Clear any error message
   */
  clearError: () => void;
}

export const usePollsStore = create<PollsState>()(
  persist(
    (set, get) => ({
      polls: [],
      votes: [],
      isLoading: false,
      error: null,

      getPollsByGroupId: async (groupId: string) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Fetching polls for group:', groupId);

          // Get polls from Supabase
          const polls = await PollsService.getPollsByGroupId(groupId);

          console.log(`Fetched ${polls.length} polls`);

          // Get votes for these polls
          const pollIds = polls.map(p => p.id);
          let votes: Vote[] = [];

          if (pollIds.length > 0) {
            console.log('Fetching votes for polls:', pollIds);
            votes = await PollsService.getVotesByPollIds(pollIds);
            console.log(`Fetched ${votes.length} votes`);
          }

          // Update the store with the fetched data
          set(state => {
            // Filter out existing polls for this group
            const filteredPolls = state.polls ?
              state.polls.filter(p => p.groupId !== groupId) : [];

            // Filter out existing votes for these polls
            const filteredVotes = state.votes ?
              state.votes.filter(v => !pollIds.includes(v.pollId)) : [];

            return {
              polls: [...filteredPolls, ...polls],
              votes: [...filteredVotes, ...votes],
              isLoading: false
            };
          });

          return polls;
        } catch (error) {
          console.error('Error fetching polls:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar las votaciones. Intenta nuevamente.',
            isLoading: false
          });
          return [];
        }
      },

      getPollById: (id: string) => {
        const { polls } = get();
        if (!polls) return null;
        return polls.find(poll => poll.id === id) || null;
      },

      createPoll: async (
        groupId: string,
        title: string,
        description: string,
        options: string[],
        endDate: string,
        allowMultipleChoice: boolean = false,
      ) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Creating poll for group:', groupId);

          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          console.log('Current user:', currentUser.id);
          console.log('Poll options:', options);

          // Create poll using the service
          const newPoll = await PollsService.createPoll(
            groupId,
            currentUser.id,
            {
              title,
              description,
              endDate,
              options,
              allowMultipleChoice
            }
          );

          console.log('Poll created successfully:', newPoll);

          // Update the store with the new poll
          set(state => ({
            polls: [newPoll, ...(state.polls || [])],
            isLoading: false
          }));

          return newPoll;
        } catch (error) {
          console.error('Error creating poll:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudo crear la votación. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      updatePoll: async (
        pollId: string,
        title?: string,
        description?: string,
        options?: string[],
        endDate?: string,
      ) => {
        set({ isLoading: true, error: null });

        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));

          const { polls } = get();
          const poll = polls?.find(p => p.id === pollId) || null;
          if (!poll) {
            throw new Error('Votación no encontrada');
          }

          // Only allow updates to draft polls
          if (poll.status !== 'DRAFT') {
            throw new Error('Solo se pueden editar votaciones en estado borrador');
          }

          // Update poll options if provided
          let updatedOptions = poll.options;
          if (options) {
            // Keep existing options that match new options
            const existingOptions = poll.options.filter(
              option => options.includes(option.text)
            );

            // Add new options
            const newOptions = options
              .filter(option => !poll.options.some(o => o.text === option))
              .map(option => ({
                id: uuidv4(),
                text: option,
                votes: 0,
              }));

            updatedOptions = [...existingOptions, ...newOptions];
          }

          // Create updated poll object
          const updatedPoll: Poll = {
            ...poll,
            title: title || poll.title,
            description: description !== undefined ? description : poll.description,
            options: updatedOptions,
            endDate: endDate || poll.endDate,
          };

          set(state => ({
            polls: state.polls ? state.polls.map(p => p.id === pollId ? updatedPoll : p) : [],
            isLoading: false
          }));

          return updatedPoll;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo actualizar la votación. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      publishPoll: async (pollId: string) => {
        set({ isLoading: true, error: null });

        try {
          const { polls } = get();
          const poll = polls?.find(p => p.id === pollId) || null;
          if (!poll) {
            throw new Error('Votación no encontrada');
          }

          // Only allow publishing draft polls
          if (poll.status !== 'DRAFT') {
            throw new Error('Solo se pueden publicar votaciones en estado borrador');
          }

          // Update poll status in Supabase
          await PollsService.updatePollStatus(pollId, 'ACTIVE');

          // Update poll status locally
          const updatedPoll: Poll = {
            ...poll,
            status: 'ACTIVE' as PollStatus,
          };

          set(state => ({
            polls: state.polls ? state.polls.map(p => p.id === pollId ? updatedPoll : p) : [],
            isLoading: false
          }));

          // Create notification for new poll
          const { addNotification } = useNotificationsStore.getState();
          addNotification({
            type: 'NEW_POLL',
            groupId: poll.groupId,
            pollId: poll.id,
            message: `Nueva votación: ${poll.title}`,
          });

          return updatedPoll;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo publicar la votación. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      closePoll: async (pollId: string) => {
        set({ isLoading: true, error: null });

        try {
          const { polls } = get();
          const poll = polls?.find(p => p.id === pollId) || null;
          if (!poll) {
            throw new Error('Votación no encontrada');
          }

          // Only allow closing active polls
          if (poll.status !== 'ACTIVE') {
            throw new Error('Solo se pueden cerrar votaciones activas');
          }

          // Update poll status in Supabase
          await PollsService.updatePollStatus(pollId, 'CLOSED');

          // Update poll status locally
          const updatedPoll: Poll = {
            ...poll,
            status: 'CLOSED' as PollStatus,
          };

          set(state => ({
            polls: state.polls ? state.polls.map(p => p.id === pollId ? updatedPoll : p) : [],
            isLoading: false
          }));

          return updatedPoll;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo cerrar la votación. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      deletePoll: async (pollId: string) => {
        set({ isLoading: true, error: null });

        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));

          const { polls } = get();
          const poll = polls?.find(p => p.id === pollId) || null;
          if (!poll) {
            throw new Error('Votación no encontrada');
          }

          // Delete poll and related votes
          set(state => ({
            polls: state.polls ? state.polls.filter(p => p.id !== pollId) : [],
            votes: state.votes ? state.votes.filter(v => v.pollId !== pollId) : [],
            isLoading: false
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo eliminar la votación. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      voteOnPoll: async (pollId: string, optionId: string) => {
        set({ isLoading: true, error: null });

        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          const { polls } = get();
          const poll = polls?.find(p => p.id === pollId) || null;
          if (!poll) {
            throw new Error('Votación no encontrada');
          }

          // Check if poll is active
          if (poll.status !== 'ACTIVE') {
            throw new Error('Solo se puede votar en votaciones activas');
          }

          // Check if option exists
          const option = poll.options.find(o => o.id === optionId);
          if (!option) {
            throw new Error('Opción no encontrada');
          }

          // Vote using Supabase service
          const newVote = await PollsService.voteOnPoll(pollId, optionId, currentUser.id);

          // Update option votes locally
          const updatedOptions = poll.options.map(o =>
            o.id === optionId ? { ...o, votes: o.votes + 1 } : o
          );

          const updatedPoll: Poll = {
            ...poll,
            options: updatedOptions,
          };

          // Convert PollVote to Vote format
          const vote: Vote = {
            id: newVote.id,
            pollId: newVote.pollId,
            userId: newVote.userId,
            optionId: newVote.optionId,
            createdAt: newVote.createdAt,
          };

          set(state => ({
            votes: [...(state.votes || []), vote],
            polls: state.polls ? state.polls.map(p => p.id === pollId ? updatedPoll : p) : [],
            isLoading: false
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo registrar el voto. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      getUserVote: (pollId: string) => {
        const { votes } = get();
        const { currentUser } = useAuthStore.getState();

        if (!currentUser || !votes) return null;

        return votes.find(
          v => v.pollId === pollId && v.userId === currentUser.id
        ) || null;
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'polls-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);