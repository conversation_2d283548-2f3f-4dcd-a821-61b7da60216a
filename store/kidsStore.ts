import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthStore } from './authStore';
import { KidsService } from '@/services/kidsService';
import { Kid } from '@/types';
import { handleAuthError, getAuthErrorMessage } from '@/utils/authErrorHandler';

// Flags to track state
let isFetchingKids = false;
let kidsDataCachedLogged = false;



interface KidsState {
  kids: Kid[];
  groupKids: Kid[]; // Kids from groups, not necessarily owned by the current user
  isLoading: boolean;
  error: string | null;
  lastFetchedUserId?: string; // Track the user ID we last fetched kids for

  // Actions
  getKids: (forceRefresh?: boolean) => Promise<Kid[]>; // Add forceRefresh parameter
  getKidsByIds: (kidIds: string[], forceRefresh?: boolean) => Promise<Kid[]>; // New method to get kids by IDs
  addKid: (kidData: { dni: string, firstName: string, lastName: string, nickname?: string, birthDate: string }) => Promise<Kid>;
  updateKid: (id: string, kidData: { dni: string, firstName: string, lastName: string, nickname?: string, birthDate: string }) => Promise<Kid>;
  linkToKid: (kidData: { dni: string, firstName: string, lastName: string, nickname?: string, birthDate: string }) => Promise<Kid>;
  deleteKid: (id: string) => Promise<void>;
  addKidToGroup: (kidId: string, groupId: string) => Promise<void>;
  removeKidFromGroup: (kidId: string, groupId: string) => Promise<void>;
  clearError: () => void;
}



export const useKidsStore = create<KidsState>()(
  persist(
    (set, get) => ({
      kids: [],
      groupKids: [], // Initialize groupKids array
      isLoading: false,
      error: null,

      getKids: async (forceRefresh = false) => {
        // Only force refresh when explicitly requested
        const currentKids = get().kids;
        const currentlyLoading = get().isLoading;
        const { currentUser } = useAuthStore.getState();

        // Always get current user ID to check if we need to refresh
        const currentUserId = currentUser?.id;

        // Store the last user ID we fetched kids for
        const lastFetchedUserId = get().lastFetchedUserId;

        // Force refresh if the user has changed
        const userChanged = currentUserId && lastFetchedUserId && currentUserId !== lastFetchedUserId;
        if (userChanged) {
          console.log('KidsStore: User changed, forcing refresh');
          forceRefresh = true;
        }

        // If we're already fetching, return current kids
        if (isFetchingKids) {
          console.log('KidsStore: Already fetching kids, returning current kids');
          return currentKids;
        }

        // If we're already loading in the store state, return current kids
        if (currentlyLoading) {
          console.log('KidsStore: Already loading kids, returning current kids');
          return currentKids;
        }

        // If we have kids and not forcing a refresh, return current kids
        if (currentKids.length > 0 && !forceRefresh) {
          console.log('KidsStore: Using cached kids data');
          return currentKids;
        }

        console.log('KidsStore: Fetching kids from service...');

        // Show loading indicator
        set({ isLoading: true, error: null });

        // Set fetching flag
        isFetchingKids = true;

        try {
          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          console.log('KidsStore: Fetching kids from service...');
          const kids = await KidsService.getKids(currentUser.id);
          console.log(`KidsStore: Fetched ${kids.length} kids`);

          // Update state with new kids and the current user ID
          set({
            kids,
            isLoading: false,
            lastFetchedUserId: currentUser.id // Store the user ID we just fetched for
          });

          return kids;
        } catch (error) {
          console.error('KidsStore: Error fetching kids:', error);

          // Handle authentication errors
          await handleAuthError(error);

          set({
            error: getAuthErrorMessage(error),
            isLoading: false
          });
          return currentKids; // Return current kids on error instead of empty array
        } finally {
          // Reset fetching flag
          isFetchingKids = false;
        }
      },

      addKid: async (kidData) => {
        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          const newKid = await KidsService.addKid(currentUser.id, kidData);

          // Update state with new kid
          const updatedKids = [...(get().kids || []), newKid];
          set({ kids: updatedKids, isLoading: false });

          return newKid;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to add kid', isLoading: false });
          throw error;
        }
      },

      updateKid: async (id, kidData) => {
        set({ isLoading: true, error: null });
        try {
          const updatedKid = await KidsService.updateKid(id, kidData);

          // Update state with updated kid
          const updatedKids = get().kids.map(kid =>
            kid.id === id ? updatedKid : kid
          );
          set({ kids: updatedKids, isLoading: false });

          return updatedKid;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to update kid', isLoading: false });
          throw error;
        }
      },

      deleteKid: async (id) => {
        set({ isLoading: true, error: null });
        try {
          await KidsService.deleteKid(id);

          // Remove kid from state
          const updatedKids = get().kids.filter(kid => kid.id !== id);
          set({ kids: updatedKids, isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to delete kid', isLoading: false });
          throw error;
        }
      },

      addKidToGroup: async (kidId, groupId) => {
        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          await KidsService.addKidToGroup(kidId, groupId, currentUser.id);

          // Update kid's groups in state
          const updatedKids = get().kids.map(kid => {
            if (kid.id === kidId) {
              return {
                ...kid,
                groups: [...(kid.groups || []), groupId],
              };
            }
            return kid;
          });

          set({ kids: updatedKids, isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to add kid to group', isLoading: false });
          throw error;
        }
      },

      removeKidFromGroup: async (kidId, groupId) => {
        set({ isLoading: true, error: null });
        try {
          await KidsService.removeKidFromGroup(kidId, groupId);

          // Update kid's groups in state
          const updatedKids = get().kids.map(kid => {
            if (kid.id === kidId) {
              return {
                ...kid,
                groups: (kid.groups || []).filter(id => id !== groupId),
              };
            }
            return kid;
          });

          set({ kids: updatedKids, isLoading: false });
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to remove kid from group', isLoading: false });
          throw error;
        }
      },

      linkToKid: async (kidData) => {
        set({ isLoading: true, error: null });
        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // For now, we'll just use the addKid function since the functionality is the same
          const newKid = await KidsService.addKid(currentUser.id, kidData);

          // Update state with new kid
          const updatedKids = [...(get().kids || []), newKid];
          set({ kids: updatedKids, isLoading: false });

          return newKid;
        } catch (error) {
          set({ error: error instanceof Error ? error.message : 'Failed to link to kid', isLoading: false });
          throw error;
        }
      },

      // Get kids by their IDs (for group members)
      getKidsByIds: async (kidIds, forceRefresh = false) => {
        if (!kidIds || kidIds.length === 0) {
          return [];
        }

        // Check if we already have these kids in the groupKids array
        const currentGroupKids = get().groupKids;
        const currentlyLoading = get().isLoading;

        // If we're already loading, return current groupKids
        if (currentlyLoading) {
          console.log('KidsStore: Already loading kids, returning current groupKids');
          return currentGroupKids;
        }

        // If we have all the requested kids and not forcing a refresh, return them
        if (!forceRefresh && currentGroupKids.length > 0) {
          const allKidsPresent = kidIds.every(id => currentGroupKids.some(kid => kid.id === id));
          if (allKidsPresent) {
            console.log('KidsStore: Using cached group kids data');
            return currentGroupKids;
          }
        }

        console.log('KidsStore: Fetching kids by IDs...');

        // Show loading indicator
        set({ isLoading: true, error: null });

        try {
          // Fetch kids by their IDs
          const kids = await KidsService.getKidsByIds(kidIds);
          console.log(`KidsStore: Fetched ${kids.length} kids by IDs`);

          // Merge the fetched kids with existing ones, avoiding duplicates
          const currentGroupKids = get().groupKids || [];
          const mergedKids = [...currentGroupKids];

          kids.forEach(newKid => {
            const existingIndex = mergedKids.findIndex(k => k.id === newKid.id);
            if (existingIndex >= 0) {
              mergedKids[existingIndex] = newKid;
            } else {
              mergedKids.push(newKid);
            }
          });

          // Update state with the merged kids
          set({
            groupKids: mergedKids,
            isLoading: false
          });

          return kids;
        } catch (error) {
          console.error('KidsStore: Error fetching kids by IDs:', error);

          // Handle authentication errors
          await handleAuthError(error);

          set({
            error: getAuthErrorMessage(error),
            isLoading: false
          });
          return currentGroupKids; // Return current groupKids on error
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'kids-storage',
      storage: createJSONStorage(() => AsyncStorage),
      onRehydrateStorage: () => (state) => {
        console.log('KidsStore: Rehydrated from storage');
        // Ensure isLoading is always false when the store is rehydrated
        if (state) {
          state.isLoading = false;
        }
      },
      // Persist the kids arrays and lastFetchedUserId, not the loading state or error
      partialize: (state) => ({
        kids: state.kids,
        groupKids: state.groupKids,
        lastFetchedUserId: state.lastFetchedUserId,
      }),
    }
  )
);