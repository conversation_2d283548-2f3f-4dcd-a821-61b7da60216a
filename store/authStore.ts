import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AuthService } from '@/services/authService';

interface AuthState {
  isAuthenticated: boolean;
  currentUser: any | null;
  users: any[];
  isLoading: boolean;
  error: string | null;
  lastFetchTime: number;
  lastFetchedUserIds: string[];
  MIN_FETCH_INTERVAL: number;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, firstName: string, lastName: string, nickname?: string, phone?: string) => Promise<void>;
  logout: () => Promise<boolean>;
  updateProfile: (data: { firstName: string; lastName: string; nickname?: string; email: string; phone?: string }) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  clearError: () => void;
  checkAuth: () => Promise<void>;
  fetchUsersByIds: (userIds: string[]) => Promise<any[]>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      currentUser: null,
      users: [],
      isLoading: false,
      error: null,
      lastFetchTime: 0,
      lastFetchedUserIds: [],
      MIN_FETCH_INTERVAL: 10000,

      login: async (email, password) => {
        set({ isLoading: true, error: null });
        try {
          console.log('Login attempt with email:', email);
          const user = await AuthService.signIn({
            email: String(email),
            password: String(password)
          });

          // Clear groups data when logging in as a different user
          console.log('AuthStore: Clearing groups data for new user');
          // Use setTimeout to avoid circular dependency issues
          setTimeout(() => {
            try {
              // Clear groups data after login
              const groupsStore = require('./groupsStore').useGroupsStore;
              groupsStore.getState().clearUserData();
            } catch (error) {
              console.error('AuthStore: Error clearing groups data:', error);
            }
          }, 100);

          set({ isAuthenticated: true, currentUser: user, isLoading: false });
        } catch (error) {
          console.error('Login error:', error);
          set({
            error: error instanceof Error ? error.message : 'Login failed',
            isLoading: false
          });
          throw error;
        }
      },

      register: async (email, password, firstName, lastName, nickname, phone) => {
        set({ isLoading: true, error: null });
        try {
          console.log('Registering user with email:', email);
          const user = await AuthService.signUp({
            email: String(email),
            password: String(password),
            firstName: String(firstName),
            lastName: String(lastName),
            nickname: nickname ? String(nickname) : undefined,
            phone: phone ? String(phone) : undefined
          });
          set({
            isAuthenticated: true,
            currentUser: user,
            isLoading: false
          });
        } catch (error) {
          console.error('Registration error:', error);
          set({
            error: error instanceof Error ? error.message : 'Registration failed',
            isLoading: false
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true, error: null });
        try {
          console.log('AuthStore: Starting logout process');
          await AuthService.signOut();
          await AsyncStorage.removeItem('auth-storage');
          set({
            isAuthenticated: false,
            currentUser: null,
            users: [],
            isLoading: false,
            error: null,
            lastFetchTime: 0,
            lastFetchedUserIds: []
          });
          return true;
        } catch (error) {
          console.error('AuthStore: Logout error:', error);
          set({
            isAuthenticated: false,
            currentUser: null,
            users: [],
            isLoading: false,
            error: error instanceof Error ? error.message : 'Logout failed',
            lastFetchTime: 0,
            lastFetchedUserIds: []
          });
          await AsyncStorage.removeItem('auth-storage');
          return false;
        }
      },

      updateProfile: async (data) => {
        set({ isLoading: true, error: null });
        try {
          if (!get().currentUser) {
            throw new Error('No user is logged in');
          }
          const updatedUser = await AuthService.updateProfile(
            get().currentUser.id,
            data
          );
          set({ currentUser: updatedUser, isLoading: false });
        } catch (error) {
          console.error('Profile update failed:', error);
          set({
            error: error instanceof Error ? error.message : 'Profile update failed',
            isLoading: false
          });
          throw error;
        }
      },

      resetPassword: async (email) => {
        set({ isLoading: true, error: null });
        try {
          await AuthService.resetPassword(email);
          set({ isLoading: false });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Password reset failed',
            isLoading: false
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },

      fetchUsersByIds: async (userIds: string[]) => {
        if (!userIds || userIds.length === 0) {
          console.log('No user IDs provided to fetchUsersByIds');
          return [];
        }

        try {
          console.log('Fetching users with IDs:', userIds);
          const { data, error } = await AuthService.getUsersByIds(userIds);

          if (error) {
            console.error('Error fetching users:', error);
            return [];
          }

          const currentUsers = get().users || [];
          const newUsers = data || [];
          const mergedUsers = [...currentUsers];

          newUsers.forEach(newUser => {
            const existingIndex = mergedUsers.findIndex(u => u.id === newUser.id);
            if (existingIndex >= 0) {
              mergedUsers[existingIndex] = newUser;
            } else {
              mergedUsers.push(newUser);
            }
          });

          set({ users: mergedUsers });
          return newUsers;
        } catch (error) {
          console.error('Exception fetching users:', error);
          return [];
        }
      },

      checkAuth: async () => {
        const { isLoading } = get();
        if (isLoading) return;

        set({ isLoading: true, error: null });
        try {
          console.log('checkAuth: called');

          // First check if we have a valid session
          const { data: { session }, error: sessionError } = await AuthService.getSession();

          if (sessionError) {
            console.log('checkAuth: session error', sessionError);
            throw sessionError;
          }

          if (!session) {
            console.log('checkAuth: no session found');
            set({
              isAuthenticated: false,
              currentUser: null,
              isLoading: false,
              error: null
            });
            return;
          }

          // Check if token is expired
          const now = Math.floor(Date.now() / 1000);
          if (session.expires_at && session.expires_at < now) {
            console.log('checkAuth: token expired, attempting refresh');

            // Try to refresh the token
            const { data: refreshData, error: refreshError } = await AuthService.refreshSession();

            if (refreshError || !refreshData.session) {
              console.log('checkAuth: token refresh failed', refreshError);
              set({
                isAuthenticated: false,
                currentUser: null,
                isLoading: false,
                error: 'Sesión expirada. Por favor inicia sesión nuevamente.'
              });
              return;
            }

            console.log('checkAuth: token refreshed successfully');
          }

          const user = await AuthService.getCurrentUser();

          if (user) {
            console.log('checkAuth: user found', user);

            // Check if this is a different user than the current one
            const currentUser = get().currentUser;
            const isDifferentUser = !currentUser || currentUser.id !== user.id;

            if (isDifferentUser) {
              console.log('AuthStore: Different user detected, clearing groups data');
              // Clear groups data for different user
              setTimeout(() => {
                try {
                  const groupsStore = require('./groupsStore').useGroupsStore;
                  groupsStore.getState().clearUserData();
                } catch (error) {
                  console.error('AuthStore: Error clearing groups data:', error);
                }
              }, 100);
            }

            set({
              isAuthenticated: true,
              currentUser: user,
              isLoading: false,
              error: null
            });
          } else {
            console.log('checkAuth: no user found');
            set({
              isAuthenticated: false,
              currentUser: null,
              isLoading: false,
              error: null
            });
          }
        } catch (error) {
          console.error('checkAuth error:', error);
          set({
            isAuthenticated: false,
            currentUser: null,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Authentication check failed'
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);