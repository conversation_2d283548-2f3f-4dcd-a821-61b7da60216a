import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface SettingsState {
  darkMode: boolean;
  language: 'en' | 'es';
  notificationsEnabled: boolean;
  toggleDarkMode: () => void;
  setLanguage: (language: 'en' | 'es') => void;
  toggleNotifications: () => void;
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set) => ({
      darkMode: false,
      language: 'es',
      notificationsEnabled: true,
      
      toggleDarkMode: () => set(state => ({ darkMode: !state.darkMode })),
      setLanguage: (language) => set({ language }),
      toggleNotifications: () => set(state => ({ notificationsEnabled: !state.notificationsEnabled })),
    }),
    {
      name: 'settings-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);