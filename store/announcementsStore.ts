import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { v4 as uuidv4 } from 'uuid';
import { Announcement, AnnouncementType } from '@/types';
import { AnnouncementsService } from '@/services/announcementsService';
import { useAuthStore } from './authStore';

interface AnnouncementsState {
  announcements: Announcement[];
  isLoading: boolean;
  error: string | null;

  // Actions
  getAnnouncementsByGroupId: (groupId: string) => Promise<void>;
  createAnnouncement: (
    groupId: string,
    title: string,
    message: string,
    type: AnnouncementType,
    date?: string
  ) => Promise<Announcement>;
  clearError: () => void;
}

export const useAnnouncementsStore = create<AnnouncementsState>()(
  persist(
    (set, get) => ({
      announcements: [],
      isLoading: false,
      error: null,

      getAnnouncementsByGroupId: async (groupId: string) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Fetching announcements for group:', groupId);

          // Get announcements from Supabase
          const announcements = await AnnouncementsService.getAnnouncementsByGroupId(groupId);

          console.log(`Fetched ${announcements.length} announcements`);

          // Update the store with the fetched announcements
          set(state => {
            // Filter out existing announcements for this group
            const filteredAnnouncements = state.announcements ?
              state.announcements.filter(a => a.groupId !== groupId) : [];

            // Add the new announcements
            return {
              announcements: [...filteredAnnouncements, ...announcements],
              isLoading: false
            };
          });
        } catch (error) {
          console.error('Error fetching announcements:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar los anuncios. Intenta nuevamente.',
            isLoading: false
          });
        }
      },

      createAnnouncement: async (
        groupId: string,
        title: string,
        message: string,
        type: AnnouncementType,
        date?: string
      ) => {
        set({ isLoading: true, error: null });

        try {
          console.log('Creating announcement for group:', groupId);

          // Get the current user
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          console.log('Current user:', currentUser.id);

          // Create new announcement using the service
          const newAnnouncement = await AnnouncementsService.createAnnouncement(
            groupId,
            currentUser.id,
            { title, message, type, date }
          );

          console.log('Announcement created successfully:', newAnnouncement);

          // Update the store with the new announcement
          set(state => ({
            announcements: [newAnnouncement, ...(state.announcements || [])],
            isLoading: false
          }));

          return newAnnouncement;
        } catch (error) {
          console.error('Error creating announcement:', error);
          set({
            error: error instanceof Error ? error.message : 'No se pudo crear el anuncio. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'announcements-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);