import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Event, EventType } from '@/types';
import { useAuthStore } from './authStore';
import { useNotificationsStore } from './notificationsStore';
import { EventsService } from '@/services/eventsService';

interface EventsState {
  events: Event[];
  isLoading: boolean;
  error: string | null;

  // Actions
  getEventsByGroupId: (groupId: string) => Promise<Event[]>;
  getEventById: (id: string) => Event | null;
  createEvent: (
    groupId: string,
    title: string,
    description: string,
    type: EventType,
    date: string,
    endDate?: string,
    location?: string,
  ) => Promise<Event>;
  updateEvent: (
    eventId: string,
    title?: string,
    description?: string,
    type?: EventType,
    date?: string,
    endDate?: string,
    location?: string,
  ) => Promise<Event>;
  deleteEvent: (eventId: string) => Promise<void>;
  clearError: () => void;
}

export const useEventsStore = create<EventsState>()(
  persist(
    (set, get) => ({
      events: [],
      isLoading: false,
      error: null,

      getEventsByGroupId: async (groupId: string) => {
        set({ isLoading: true, error: null });

        try {
          // Get events from Supabase
          const events = await EventsService.getEventsByGroupId(groupId);

          set({ events, isLoading: false });
          return events;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudieron cargar los eventos. Intenta nuevamente.',
            isLoading: false
          });
          return [];
        }
      },

      getEventById: (id: string) => {
        const { events } = get();
        if (!events) return null;
        return events.find(event => event.id === id) || null;
      },

      createEvent: async (
        groupId: string,
        title: string,
        description: string,
        type: EventType,
        date: string,
        endDate?: string,
        location?: string,
      ) => {
        set({ isLoading: true, error: null });

        try {
          const { currentUser } = useAuthStore.getState();

          if (!currentUser) {
            throw new Error('No user is logged in');
          }

          // Create event using EventsService
          const newEvent = await EventsService.createEvent(groupId, currentUser.id, {
            title,
            description,
            type,
            date,
            endDate,
            location,
          });

          set(state => ({
            events: [newEvent, ...(state.events || [])],
            isLoading: false
          }));

          // Create notification for new event
          const { addNotification } = useNotificationsStore.getState();
          addNotification({
            type: 'NEW_EVENT',
            groupId,
            eventId: newEvent.id,
            message: `Nuevo evento: ${title}`,
          });

          return newEvent;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo crear el evento. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      updateEvent: async (
        eventId: string,
        title?: string,
        description?: string,
        type?: EventType,
        date?: string,
        endDate?: string,
        location?: string,
      ) => {
        set({ isLoading: true, error: null });

        try {
          // Update event using EventsService
          const updatedEvent = await EventsService.updateEvent(eventId, {
            title,
            description,
            type,
            date,
            endDate,
            location,
          });

          set(state => ({
            events: state.events?.map(e => e.id === eventId ? updatedEvent : e) || [],
            isLoading: false
          }));

          return updatedEvent;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo actualizar el evento. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      deleteEvent: async (eventId: string) => {
        set({ isLoading: true, error: null });

        try {
          // Delete event using EventsService
          await EventsService.deleteEvent(eventId);

          // Update local state
          set(state => ({
            events: state.events ? state.events.filter(e => e.id !== eventId) : [],
            isLoading: false
          }));
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'No se pudo eliminar el evento. Intenta nuevamente.',
            isLoading: false
          });
          throw error;
        }
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'events-storage',
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);