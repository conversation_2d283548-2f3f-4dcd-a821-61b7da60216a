<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Qids - Redirecting...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
            background-color: #f9f9f9;
            color: #333;
        }
        .logo {
            width: 100px;
            height: 100px;
            margin-bottom: 20px;
        }
        .container {
            max-width: 500px;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            margin-bottom: 20px;
            color: #4a6cf7;
        }
        p {
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .button {
            display: inline-block;
            background-color: #4a6cf7;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 600;
            margin-top: 10px;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #3a5ce5;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(74, 108, 247, 0.3);
            border-radius: 50%;
            border-top-color: #4a6cf7;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Qids</h1>
        <div id="loading">
            <span class="loading"></span>
            <p>Redirigiendo a la aplicación...</p>
        </div>
        <div id="manual" style="display: none;">
            <p>Si no eres redirigido automáticamente, haz clic en el botón de abajo para abrir la aplicación:</p>
            <a href="#" id="open-app" class="button">Abrir Qids</a>
            <p style="margin-top: 20px; font-size: 14px; color: #666;">
                Si tienes problemas, asegúrate de tener la aplicación Qids instalada en tu dispositivo.
            </p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Extract the hash fragment or query parameters from the URL
            const hash = window.location.hash;
            const searchParams = new URLSearchParams(window.location.search);
            const encodedUrl = searchParams.get('url');
            const token = searchParams.get('token');
            const type = searchParams.get('type');

            // Also check if token/type are in the hash
            let hashToken = null;
            let hashType = null;
            if (hash) {
                const hashParams = new URLSearchParams(hash.substring(1));
                hashToken = hashParams.get('access_token') || hashParams.get('token');
                hashType = hashParams.get('type');
            }

            console.log('URL analysis:', {
                hash,
                token: token ? `${token.substring(0, 20)}...` : null,
                type,
                hashToken: hashToken ? `${hashToken.substring(0, 20)}...` : null,
                hashType,
                fullUrl: window.location.href
            });

            // First check if we have direct token and type parameters
            if (token && type) {
                console.log('Found direct token and type parameters:', { token: `${token.substring(0, 20)}...`, type });

                // For recovery type, redirect to reset-password screen
                if (type === 'recovery') {
                    const deepLink = `qids://reset-password?access_token=${token}`;
                    console.log('Redirecting to reset password:', deepLink);

                    // Try to open the app
                    window.location.href = deepLink;

                    // Show manual button after a delay in case automatic redirect fails
                    setTimeout(function() {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('manual').style.display = 'block';

                        // Set the manual button href
                        document.getElementById('open-app').href = deepLink;
                        document.getElementById('open-app').textContent = 'Abrir Qids';
                    }, 3000);
                } else {
                    // For other types (like email confirmation)
                    const deepLink = `qids://confirm-email?token=${token}&type=${type}`;
                    console.log('Redirecting to confirm email:', deepLink);

                    // Try to open the app
                    window.location.href = deepLink;

                    // Show manual button after a delay in case automatic redirect fails
                    setTimeout(function() {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('manual').style.display = 'block';

                        // Set the manual button href
                        document.getElementById('open-app').href = deepLink;
                        document.getElementById('open-app').textContent = 'Abrir Qids';
                    }, 3000);
                }
                return;
            }

            // If we don't have direct parameters, check if we have an encoded URL in the query parameters
            if (encodedUrl) {
                try {
                    // Decode the URL
                    const decodedUrl = decodeURIComponent(encodedUrl);

                    // Extract the hash from the decoded URL
                    const hashMatch = decodedUrl.match(/#(.+)$/);
                    if (hashMatch && hashMatch[1]) {
                        // Create the deep link URL with the hash
                        // Extract access_token if present
                        const params = new URLSearchParams(hashMatch[1]);
                        const accessToken = params.get('access_token');

                        // Create deep link with specific parameters for better handling
                        let deepLink;
                        if (accessToken) {
                            deepLink = `qids://confirm-email?access_token=${accessToken}`;
                        } else {
                            deepLink = `qids://confirm-email#${hashMatch[1]}`;
                        }

                        console.log('Redirecting to:', deepLink);

                        // Try to open the app
                        window.location.href = deepLink;

                        // Show manual button after a delay in case automatic redirect fails
                        setTimeout(function() {
                            document.getElementById('loading').style.display = 'none';
                            document.getElementById('manual').style.display = 'block';

                            // Set the manual button href
                            document.getElementById('open-app').href = deepLink;
                            document.getElementById('open-app').textContent = 'Abrir Qids';
                        }, 3000);
                        return;
                    }
                } catch (e) {
                    console.error('Error decoding URL:', e);
                }
            }

            // If we don't have an encoded URL, check if we have a hash
            if (hash) {
                // Create the deep link URL with the hash
                // Extract access_token if present
                const hashContent = hash.substring(1); // Remove the leading #
                const params = new URLSearchParams(hashContent);
                const accessToken = params.get('access_token');

                // Create deep link with specific parameters for better handling
                let deepLink;
                if (accessToken) {
                    deepLink = `qids://confirm-email?access_token=${accessToken}`;
                } else {
                    deepLink = `qids://confirm-email${hash}`;
                }

                console.log('Redirecting to:', deepLink);

                // Try to open the app
                window.location.href = deepLink;

                // Show manual button after a delay in case automatic redirect fails
                setTimeout(function() {
                    document.getElementById('loading').style.display = 'none';
                    document.getElementById('manual').style.display = 'block';

                    // Set the manual button href
                    document.getElementById('open-app').href = deepLink;
                    document.getElementById('open-app').textContent = 'Abrir Qids';
                }, 3000);
            } else {
                // No hash or encoded URL found, show error
                document.getElementById('loading').innerHTML = '<p>Error: No se encontró un enlace válido.</p>';
            }
        });
    </script>
</body>
</html>
