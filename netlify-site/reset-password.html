<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restablecer Contraseña - Qids</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333;
        }
        .container {
            max-width: 400px;
            width: 100%;
            padding: 30px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo img {
            max-width: 150px;
            height: auto;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            color: #4a6cf7;
            font-size: 24px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
            transition: border-color 0.2s;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #4a6cf7;
        }
        .token-input {
            text-align: center;
            font-size: 18px;
            letter-spacing: 2px;
            font-weight: 600;
        }
        .button {
            width: 100%;
            background-color: #4a6cf7;
            color: white;
            padding: 12px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .button:hover {
            background-color: #3a5ce5;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        .success {
            color: #27ae60;
            font-size: 14px;
            margin-top: 10px;
            text-align: center;
        }
        .info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #6c757d;
        }
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://main--cheerful-genie-53baa3.netlify.app/qids-logo-full.png" alt="Qids Logo">
        </div>
        
        <h1>Restablecer Contraseña</h1>
        
        <div class="info">
            Ingresa el código de 6 dígitos que recibiste por correo electrónico junto con tu nueva contraseña.
        </div>

        <form id="resetForm">
            <div class="form-group">
                <label for="token">Código de verificación (6 dígitos)</label>
                <input type="text" id="token" name="token" class="token-input" maxlength="6" placeholder="000000" required>
            </div>

            <div class="form-group">
                <label for="password">Nueva contraseña</label>
                <input type="password" id="password" name="password" placeholder="Ingresa tu nueva contraseña" required>
            </div>

            <div class="form-group">
                <label for="confirmPassword">Confirmar nueva contraseña</label>
                <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirma tu nueva contraseña" required>
            </div>

            <button type="submit" class="button" id="submitBtn">
                Restablecer Contraseña
            </button>
        </form>

        <div id="message"></div>
    </div>

    <script>
        document.getElementById('resetForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const messageDiv = document.getElementById('message');
            const token = document.getElementById('token').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Clear previous messages
            messageDiv.innerHTML = '';

            // Validate inputs
            if (!token || token.length !== 6) {
                messageDiv.innerHTML = '<div class="error">Por favor ingresa un código de 6 dígitos válido.</div>';
                return;
            }

            if (password !== confirmPassword) {
                messageDiv.innerHTML = '<div class="error">Las contraseñas no coinciden.</div>';
                return;
            }

            if (password.length < 6) {
                messageDiv.innerHTML = '<div class="error">La contraseña debe tener al menos 6 caracteres.</div>';
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="loading"></span>Procesando...';

            try {
                // Here you would normally call your Supabase API to verify the token and reset the password
                // For now, we'll create a deep link to the app with the token and password
                const deepLink = `qids://reset-password?token=${token}&password=${encodeURIComponent(password)}`;
                
                messageDiv.innerHTML = '<div class="success">Redirigiendo a la aplicación...</div>';
                
                // Try to open the app
                window.location.href = deepLink;
                
                // Reset button after a delay
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = 'Restablecer Contraseña';
                    messageDiv.innerHTML = '<div class="info">Si la aplicación no se abrió automáticamente, ábrela manualmente e ingresa el código: <strong>' + token + '</strong></div>';
                }, 3000);

            } catch (error) {
                console.error('Error:', error);
                messageDiv.innerHTML = '<div class="error">Ocurrió un error. Por favor intenta nuevamente.</div>';
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Restablecer Contraseña';
            }
        });

        // Auto-format token input
        document.getElementById('token').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 6) value = value.slice(0, 6);
            e.target.value = value;
        });
    </script>
</body>
</html>
