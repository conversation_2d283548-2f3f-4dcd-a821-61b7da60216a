# Qids Auth Redirect Site

Este directorio contiene el archivo de redirección de autenticación para la aplicación Qids.

## Archivos

- `auth-redirect.html`: Página de redirección que maneja los enlaces de autenticación de Supabase
- `netlify.toml`: Configuración de Netlify para servir correctamente los archivos HTML

## Despliegue

Este sitio se despliega automáticamente en Netlify cuando se hacen cambios al repositorio.

## URL del sitio

Una vez desplegado en Netlify, la URL será algo como:
`https://qids-auth.netlify.app/auth-redirect.html`

Esta URL debe configurarse en:
1. Supabase Authentication > URL Configuration > Site URL
2. Las plantillas de email de Supabase
