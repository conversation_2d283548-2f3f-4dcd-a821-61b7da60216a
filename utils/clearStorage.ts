import AsyncStorage from '@react-native-async-storage/async-storage';
import { useColectasStore } from '@/store/colectasStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAnnouncementsStore } from '@/store/announcementsStore';
import { usePollsStore } from '@/store/pollsStore';
import { useEventsStore } from '@/store/eventsStore';

/**
 * Clears all data from AsyncStorage and resets all stores
 */
export const clearAllStorage = async (): Promise<void> => {
  try {
    // Clear all AsyncStorage data
    await AsyncStorage.clear();
    
    // Reset all stores
    useColectasStore.getState().clearStore();
    
    // Reload the app (this will force a re-fetch of data from the server)
    console.log('AsyncStorage cleared successfully');
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error clearing AsyncStorage:', error);
    return Promise.reject(error);
  }
};

/**
 * Clears only the colectas store data
 */
export const clearColectasStorage = async (): Promise<void> => {
  try {
    // Clear colectas store data from AsyncStorage
    await AsyncStorage.removeItem('colectas-storage');
    
    // Reset the colectas store
    useColectasStore.getState().clearStore();
    
    console.log('Colectas storage cleared successfully');
    
    return Promise.resolve();
  } catch (error) {
    console.error('Error clearing colectas storage:', error);
    return Promise.reject(error);
  }
};
