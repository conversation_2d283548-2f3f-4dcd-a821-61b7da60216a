/**
 * Utility functions for handling authentication errors consistently across the app
 */

/**
 * Check if an error is related to JWT expiration or authentication
 */
export const isAuthError = (error: any): boolean => {
  if (!error) return false;
  
  const errorMessage = error instanceof Error ? error.message : String(error);
  const lowerMessage = errorMessage.toLowerCase();
  
  return (
    lowerMessage.includes('jwt expired') ||
    lowerMessage.includes('invalid_token') ||
    lowerMessage.includes('token_expired') ||
    lowerMessage.includes('unauthorized') ||
    lowerMessage.includes('authentication required') ||
    lowerMessage.includes('session expired') ||
    lowerMessage.includes('invalid jwt') ||
    lowerMessage.includes('token invalid')
  );
};

/**
 * Handle authentication errors by triggering logout
 */
export const handleAuthError = async (error: any): Promise<void> => {
  if (!isAuthError(error)) {
    return;
  }
  
  console.log('AuthErrorHandler: JWT/Auth error detected, triggering logout');
  
  try {
    // Dynamically import to avoid circular dependencies
    const { useAuthStore } = await import('@/store/authStore');
    const { logout } = useAuthStore.getState();
    
    // Use setTimeout to avoid blocking the current execution
    setTimeout(() => {
      logout().catch((logoutError) => {
        console.error('AuthErrorHandler: Error during logout:', logoutError);
      });
    }, 100);
  } catch (importError) {
    console.error('AuthErrorHandler: Error importing auth store:', importError);
  }
};

/**
 * Get user-friendly error message for authentication errors
 */
export const getAuthErrorMessage = (error: any): string => {
  if (isAuthError(error)) {
    return 'Sesión expirada. Por favor inicia sesión nuevamente.';
  }
  
  return error instanceof Error ? error.message : String(error);
};

/**
 * Wrapper function to handle errors in store methods
 */
export const withAuthErrorHandling = async <T>(
  operation: () => Promise<T>,
  fallbackValue: T
): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    console.error('Operation failed:', error);
    
    // Handle auth errors
    await handleAuthError(error);
    
    // Return fallback value
    return fallbackValue;
  }
};
