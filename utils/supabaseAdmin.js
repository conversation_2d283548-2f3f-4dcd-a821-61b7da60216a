/**
 * Supabase Admin Utility
 *
 * This utility provides direct access to the Supabase database for debugging and data management.
 * It uses the anon key from the environment variables, so it's subject to RLS policies.
 * For admin operations that bypass RLS, you would need to use a service role key.
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables must be set');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Execute a query on a table
 * @param {string} table - The table name
 * @param {Object} options - Query options
 * @param {string[]} options.select - Columns to select
 * @param {Object} options.eq - Equality filters { column: value }
 * @param {Object} options.neq - Not equal filters { column: value }
 * @param {Object} options.gt - Greater than filters { column: value }
 * @param {Object} options.lt - Less than filters { column: value }
 * @param {Object} options.gte - Greater than or equal filters { column: value }
 * @param {Object} options.lte - Less than or equal filters { column: value }
 * @param {Object} options.like - LIKE filters { column: value }
 * @param {Object} options.ilike - ILIKE filters { column: value }
 * @param {string[]} options.in - IN filters { column: [value1, value2] }
 * @param {number} options.limit - Limit results
 * @param {number} options.offset - Offset results
 * @param {string} options.orderBy - Order by column
 * @param {boolean} options.ascending - Order ascending or descending
 * @param {string[]} options.relationships - Relationships to include
 * @returns {Promise<Object>} - Query result
 */
async function query(table, options = {}) {
  let query = supabase.from(table).select(options.select ? options.select.join(',') : '*');

  // Apply filters
  if (options.eq) {
    Object.entries(options.eq).forEach(([column, value]) => {
      query = query.eq(column, value);
    });
  }

  if (options.neq) {
    Object.entries(options.neq).forEach(([column, value]) => {
      query = query.neq(column, value);
    });
  }

  if (options.gt) {
    Object.entries(options.gt).forEach(([column, value]) => {
      query = query.gt(column, value);
    });
  }

  if (options.lt) {
    Object.entries(options.lt).forEach(([column, value]) => {
      query = query.lt(column, value);
    });
  }

  if (options.gte) {
    Object.entries(options.gte).forEach(([column, value]) => {
      query = query.gte(column, value);
    });
  }

  if (options.lte) {
    Object.entries(options.lte).forEach(([column, value]) => {
      query = query.lte(column, value);
    });
  }

  if (options.like) {
    Object.entries(options.like).forEach(([column, value]) => {
      query = query.like(column, value);
    });
  }

  if (options.ilike) {
    Object.entries(options.ilike).forEach(([column, value]) => {
      query = query.ilike(column, value);
    });
  }

  if (options.in) {
    Object.entries(options.in).forEach(([column, values]) => {
      query = query.in(column, values);
    });
  }

  // Apply pagination
  if (options.limit) {
    query = query.limit(options.limit);
  }

  if (options.offset) {
    query = query.offset(options.offset);
  }

  // Apply ordering
  if (options.orderBy) {
    query = query.order(options.orderBy, { ascending: options.ascending !== false });
  }

  // Execute query
  const { data, error } = await query;

  if (error) {
    console.error('Error executing query:', error);
    throw error;
  }

  return data;
}

/**
 * Insert data into a table
 * @param {string} table - The table name
 * @param {Object|Object[]} data - Data to insert
 * @param {Object} options - Insert options
 * @param {boolean} options.upsert - Whether to upsert
 * @returns {Promise<Object>} - Insert result
 */
async function insert(table, data, options = {}) {
  let query = supabase.from(table).insert(data);

  if (options.upsert) {
    query = query.upsert(data);
  }

  const { data: result, error } = await query;

  if (error) {
    console.error('Error inserting data:', error);
    throw error;
  }

  return result;
}

/**
 * Update data in a table
 * @param {string} table - The table name
 * @param {Object} data - Data to update
 * @param {Object} filters - Filters to apply
 * @returns {Promise<Object>} - Update result
 */
async function update(table, data, filters) {
  let query = supabase.from(table).update(data);

  // Apply filters
  Object.entries(filters).forEach(([column, value]) => {
    query = query.eq(column, value);
  });

  const { data: result, error } = await query;

  if (error) {
    console.error('Error updating data:', error);
    throw error;
  }

  return result;
}

/**
 * Delete data from a table
 * @param {string} table - The table name
 * @param {Object} filters - Filters to apply
 * @returns {Promise<Object>} - Delete result
 */
async function remove(table, filters) {
  let query = supabase.from(table).delete();

  // Apply filters
  Object.entries(filters).forEach(([column, value]) => {
    query = query.eq(column, value);
  });

  const { data: result, error } = await query;

  if (error) {
    console.error('Error deleting data:', error);
    throw error;
  }

  return result;
}

/**
 * Execute a raw SQL query
 * @param {string} sql - SQL query to execute
 * @returns {Promise<Object>} - Query result
 */
async function rawQuery(sql) {
  // For security reasons, Supabase doesn't allow executing arbitrary SQL via the client library
  // We'll use a different approach - querying specific system tables for metadata

  console.log('Warning: Raw SQL execution is limited for security reasons');
  console.log('Executing query:', sql);

  // If this is a metadata query, we can handle it specially
  if (sql.includes('information_schema.tables') || sql.includes('information_schema.columns')) {
    // For table listing, we'll use a different approach
    const { data, error } = await supabase
      .from('_metadata')
      .select('*');

    if (error) {
      console.error('Error executing metadata query:', error);
      throw error;
    }

    return data || [];
  }

  // For other queries, we'll need to use the REST API directly
  console.error('Error: Direct SQL execution is not supported for security reasons');
  throw new Error('Direct SQL execution is not supported for security reasons');
}

/**
 * Get table schema
 * @param {string} table - The table name
 * @returns {Promise<Object>} - Table schema
 */
async function getTableSchema(table) {
  const sql = `
    SELECT column_name, data_type, is_nullable, column_default
    FROM information_schema.columns
    WHERE table_schema = 'public'
    AND table_name = '${table}'
    ORDER BY ordinal_position;
  `;

  return await rawQuery(sql);
}

/**
 * List all tables
 * @returns {Promise<Object>} - List of tables
 */
async function listTables() {
  const sql = `
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = 'public'
    ORDER BY table_name;
  `;

  return await rawQuery(sql);
}

module.exports = {
  supabase,
  query,
  insert,
  update,
  remove,
  rawQuery,
  getTableSchema,
  listTables
};
