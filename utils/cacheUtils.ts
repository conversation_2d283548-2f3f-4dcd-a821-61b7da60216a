import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Clear all cached data in the app
 */
export const clearAllCaches = async (): Promise<void> => {
  try {
    // Get all keys
    const keys = await AsyncStorage.getAllKeys();
    
    // Filter for store keys (those ending with -storage)
    const storeKeys = keys.filter(key => key.endsWith('-storage'));
    
    // Log the keys being cleared
    console.log('Clearing caches for:', storeKeys);
    
    // Clear all store caches
    if (storeKeys.length > 0) {
      await AsyncStorage.multiRemove(storeKeys);
    }
    
    console.log('All caches cleared successfully');
  } catch (error) {
    console.error('Error clearing caches:', error);
  }
};

/**
 * Clear specific cache by store name
 */
export const clearCache = async (storeName: string): Promise<void> => {
  try {
    // Ensure the store name ends with -storage
    const key = storeName.endsWith('-storage') ? storeName : `${storeName}-storage`;
    
    // Clear the specific cache
    await AsyncStorage.removeItem(key);
    
    console.log(`Cache cleared for: ${key}`);
  } catch (error) {
    console.error(`Error clearing cache for ${storeName}:`, error);
  }
};
