/**
 * Safe UUID generation utilities with fallback for environments that don't support crypto.getRandomValues()
 */
import { v4 as originalUuidv4 } from 'uuid';

/**
 * Generate a random UUID string with fallback for environments 
 * where crypto.getRandomValues() is not supported
 * 
 * @returns A UUID string
 */
export function generateUuid(): string {
  try {
    // Try the normal UUID v4 implementation first
    return originalUuidv4();
  } catch (error) {
    // If crypto.getRandomValues() is not available, use a fallback
    console.warn('crypto.getRandomValues() not supported, using fallback UUID implementation');
    return fallbackUuid();
  }
}

/**
 * Fallback UUID generator that doesn't rely on crypto.getRandomValues()
 * Note: This is less secure/random than the real implementation, but works in all environments
 * 
 * @returns A string that resembles a UUID
 */
function fallbackUuid(): string {
  // Implementation based on a simplified version of the UUID v4 format
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 1000000000);
  const deviceId = Math.floor(Math.random() * 10000); // Some device-specific value would be better
  
  // Format in UUID v4 style (not a real UUID, but a unique-enough string)
  return `${s4()}${s4()}-${s4()}-4${s3()}-${getVariant()}${s3()}-${s4()}${s4()}${s4()}`;
  
  // Helper to generate 4 random hex chars
  function s4(): string {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
  
  // Helper to generate 3 random hex chars
  function s3(): string {
    return Math.floor((1 + Math.random()) * 0x1000)
      .toString(16)
      .substring(1);
  }
  
  // UUID v4 variant bits
  function getVariant(): string {
    return '89ab'[Math.floor(Math.random() * 4)];
  }
} 