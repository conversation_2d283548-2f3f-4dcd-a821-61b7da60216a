#!/bin/bash

# This script runs the SQL migrations directly against the Supabase database
# Make sure you have the Supabase CLI installed and are logged in

# Get the current directory
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
MIGRATIONS_DIR="$DIR/../supabase/migrations"

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI is not installed. Please install it first."
    echo "https://supabase.com/docs/guides/cli"
    exit 1
fi

# Check if the migrations directory exists
if [ ! -d "$MIGRATIONS_DIR" ]; then
    echo "Migrations directory not found: $MIGRATIONS_DIR"
    exit 1
fi

echo "Running migrations from $MIGRATIONS_DIR"

# Get all SQL files in the migrations directory
SQL_FILES=$(find "$MIGRATIONS_DIR" -name "*.sql" | sort)

# Run each migration
for file in $SQL_FILES; do
    echo "Running migration: $(basename "$file")"
    
    # Run the SQL file against the Supabase database
    supabase db execute --file "$file"
    
    # Check if the command was successful
    if [ $? -ne 0 ]; then
        echo "Error running migration: $(basename "$file")"
        exit 1
    fi
    
    echo "Migration $(basename "$file") completed successfully"
done

echo "All migrations completed successfully"
