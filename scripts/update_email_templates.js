/**
 * <PERSON><PERSON><PERSON> to update Supabase email templates
 *
 * Usage:
 * node scripts/update_email_templates.js
 *
 * Requirements:
 * - SUPABASE_URL: Your Supabase project URL
 * - SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key (not the anon key)
 * - SUPABASE_PROJECT_ID: Your Supabase project ID (can be found in the URL of your Supabase dashboard)
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
require('dotenv').config();

// Get Supabase URL, service role key, and project ID from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabaseProjectId = process.env.SUPABASE_PROJECT_ID;

if (!supabaseUrl || !supabaseServiceRoleKey || !supabaseProjectId) {
  console.error('Error: Required environment variables are not set.');
  console.error('Please set the following environment variables:');
  console.error('- EXPO_PUBLIC_SUPABASE_URL: Your Supabase project URL');
  console.error('- SUPABASE_SERVICE_ROLE_KEY: Your Supabase service role key');
  console.error('- SUPABASE_PROJECT_ID: Your Supabase project ID');
  process.exit(1);
}

// Template types and their corresponding file names and subjects
const templates = [
  {
    type: 'confirmation',
    file: 'confirm-signup.html',
    subject: 'Confirma tu correo electrónico - Qids'
  },
  {
    type: 'invite',
    file: 'invite-user.html',
    subject: 'Invitación a Qids'
  },
  {
    type: 'magic_link',
    file: 'magic-link.html',
    subject: 'Tu enlace mágico para Qids'
  },
  {
    type: 'change_email',
    file: 'change-email.html',
    subject: 'Confirma tu nuevo correo electrónico - Qids'
  },
  {
    type: 'recovery',
    file: 'reset-password.html',
    subject: 'Restablece tu contraseña - Qids'
  },
  {
    type: 'mfa_verify',
    file: 'reauthentication.html',
    subject: 'Verifica tu identidad - Qids'
  }
];

// Function to read template file
function readTemplateFile(fileName) {
  const filePath = path.join(__dirname, '..', 'supabase', 'email-templates', fileName);
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading file ${fileName}:`, error);
    return null;
  }
}

// Function to update a template using the Supabase Management API
async function updateTemplate(type, content, subject) {
  try {
    const url = `https://api.supabase.com/v1/projects/${supabaseProjectId}/auth/email-templates/${type}`;

    const response = await axios({
      method: 'PUT',
      url: url,
      headers: {
        'Authorization': `Bearer ${supabaseServiceRoleKey}`,
        'Content-Type': 'application/json'
      },
      data: {
        content: content,
        subject: subject
      }
    });

    if (response.status === 200) {
      console.log(`✅ Successfully updated ${type} template`);
      return true;
    } else {
      console.error(`❌ Failed to update ${type} template. Status: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error updating ${type} template:`, error.response?.data || error.message);
    return false;
  }
}

// Function to update email templates
async function updateEmailTemplates() {
  console.log('🔄 Updating email templates...');
  console.log('');

  let successCount = 0;
  let failCount = 0;

  for (const template of templates) {
    const content = readTemplateFile(template.file);
    if (!content) {
      failCount++;
      continue;
    }

    console.log(`📝 Processing ${template.type} template...`);

    const success = await updateTemplate(template.type, content, template.subject);
    if (success) {
      successCount++;
    } else {
      failCount++;
    }
  }

  console.log('');
  console.log('📊 Summary:');
  console.log(`✅ Successfully updated: ${successCount} templates`);
  console.log(`❌ Failed to update: ${failCount} templates`);

  if (failCount > 0) {
    console.log('');
    console.log('💡 If you encountered errors, you can manually update the templates:');
    console.log('1. Go to the Supabase dashboard: https://supabase.com/dashboard');
    console.log('2. Select your project');
    console.log('3. Navigate to Authentication > Email Templates');
    console.log('4. For each template type, click on the template and replace the content with the corresponding template file');
    console.log('5. Click "Save"');
  }
}

// Run the function
updateEmailTemplates().catch(console.error);
