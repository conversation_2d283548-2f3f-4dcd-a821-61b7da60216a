#!/bin/bash

# Manual Backup Script for Qids Database
# This script will prompt you for your database password

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="backups/qids_manual_backup_$TIMESTAMP.sql"

echo "🗄️  Qids Database Manual Backup"
echo "================================"
echo ""
echo "📁 Backup will be saved to: $BACKUP_FILE"
echo ""
echo "🔑 You need your Supabase database password."
echo "   Find it in: Supabase Dashboard > Settings > Database > Database password"
echo ""

# Prompt for password
read -s -p "Enter your database password: " DB_PASSWORD
echo ""

if [ -z "$DB_PASSWORD" ]; then
    echo "❌ No password provided. Exiting."
    exit 1
fi

# Construct database URL
DATABASE_URL="postgresql://postgres:$<EMAIL>:5432/postgres"

echo ""
echo "🚀 Starting backup..."

# Create backup
pg_dump "$DATABASE_URL" > "$BACKUP_FILE" 2>/dev/null

if [ $? -eq 0 ]; then
    echo "✅ Backup completed successfully!"
    echo ""
    echo "📊 Backup Details:"
    echo "   📁 File: $BACKUP_FILE"
    echo "   📏 Size: $(du -sh $BACKUP_FILE | cut -f1)"
    echo "   📅 Date: $(date)"
    
    # Create compressed version
    echo ""
    echo "🗜️  Creating compressed version..."
    gzip -c "$BACKUP_FILE" > "${BACKUP_FILE}.gz"
    echo "   📦 Compressed: ${BACKUP_FILE}.gz"
    echo "   📏 Compressed size: $(du -sh ${BACKUP_FILE}.gz | cut -f1)"
    
    # Create info file
    cat > "backups/backup_info_$TIMESTAMP.txt" << EOF
Qids Database Backup Information
================================

Backup Date: $(date)
Backup Method: Manual pg_dump
Original File: $BACKUP_FILE
Compressed File: ${BACKUP_FILE}.gz
Original Size: $(du -sh $BACKUP_FILE | cut -f1)
Compressed Size: $(du -sh ${BACKUP_FILE}.gz | cut -f1)

Database Info:
- Project: jbvwbuzrqzprapjtdcsc
- Host: db.jbvwbuzrqzprapjtdcsc.supabase.co
- Database: postgres

To restore this backup:
psql "postgresql://postgres:[PASSWORD]@db.jbvwbuzrqzprapjtdcsc.supabase.co:5432/postgres" < $BACKUP_FILE

Or from compressed:
gunzip -c ${BACKUP_FILE}.gz | psql "postgresql://postgres:[PASSWORD]@db.jbvwbuzrqzprapjtdcsc.supabase.co:5432/postgres"

⚠️  Always test restores in a development environment first!
EOF
    
    echo ""
    echo "📝 Backup info saved to: backups/backup_info_$TIMESTAMP.txt"
    echo ""
    echo "🎉 Backup process completed!"
    echo ""
    echo "💡 Next steps:"
    echo "   1. Verify the backup file exists and has reasonable size"
    echo "   2. Store the backup in a secure location"
    echo "   3. Consider setting up automated backups"
    
else
    echo "❌ Backup failed!"
    echo ""
    echo "🔍 Possible issues:"
    echo "   - Incorrect password"
    echo "   - Network connectivity"
    echo "   - Database permissions"
    echo ""
    echo "💡 Try using the Supabase Dashboard backup instead:"
    echo "   https://supabase.com/dashboard/project/jbvwbuzrqzprapjtdcsc/settings/database"
    exit 1
fi
