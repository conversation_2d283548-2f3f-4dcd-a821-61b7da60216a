#!/bin/bash

# Qids Database Backup Script
# This script creates comprehensive backups of your Supabase database

# Set colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Create backup directory with timestamp
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="backups/db_backup_$TIMESTAMP"
mkdir -p "$BACKUP_DIR"

echo -e "${BLUE}🗄️  Starting Qids Database Backup...${NC}"
echo -e "${YELLOW}📁 Backup directory: $BACKUP_DIR${NC}"

# Function to check if command was successful
check_success() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ $1 completed successfully${NC}"
    else
        echo -e "${RED}❌ $1 failed${NC}"
        exit 1
    fi
}

# 1. Full database dump (schema + data)
echo -e "\n${BLUE}📊 Creating full database dump...${NC}"
supabase db dump --linked -f "$BACKUP_DIR/full_database_dump.sql"
check_success "Full database dump"

# 2. Schema-only dump
echo -e "\n${BLUE}🏗️  Creating schema-only dump...${NC}"
supabase db dump --linked --data-only=false -f "$BACKUP_DIR/schema_only.sql"
check_success "Schema-only dump"

# 3. Data-only dump
echo -e "\n${BLUE}📋 Creating data-only dump...${NC}"
supabase db dump --linked --data-only -f "$BACKUP_DIR/data_only.sql"
check_success "Data-only dump"

# 4. Individual table dumps for critical tables
echo -e "\n${BLUE}📑 Creating individual table dumps...${NC}"

CRITICAL_TABLES=("users" "kids" "groups" "institutions" "group_memberships" "colectas" "contributions" "polls" "poll_options" "poll_votes")

for table in "${CRITICAL_TABLES[@]}"; do
    echo -e "${YELLOW}  Dumping table: $table${NC}"
    supabase db dump --linked --data-only -s public -f "$BACKUP_DIR/table_${table}.sql" 2>/dev/null
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}    ✅ $table dumped${NC}"
    else
        echo -e "${YELLOW}    ⚠️  $table might not exist or is empty${NC}"
    fi
done

# 5. Create a backup info file
echo -e "\n${BLUE}📝 Creating backup information file...${NC}"
cat > "$BACKUP_DIR/backup_info.txt" << EOF
Qids Database Backup Information
================================

Backup Date: $(date)
Backup Directory: $BACKUP_DIR
Supabase CLI Version: $(supabase --version)

Files in this backup:
- full_database_dump.sql: Complete database with schema and data
- schema_only.sql: Database structure only (tables, functions, policies, etc.)
- data_only.sql: All data without schema
- table_*.sql: Individual table data dumps
- backup_info.txt: This information file

To restore:
1. Full restore: psql -d your_database < full_database_dump.sql
2. Schema only: psql -d your_database < schema_only.sql
3. Data only: psql -d your_database < data_only.sql

Critical Tables Backed Up:
$(for table in "${CRITICAL_TABLES[@]}"; do echo "- $table"; done)

Notes:
- Always test restores in a development environment first
- Keep multiple backup versions
- Store backups in a secure location
EOF

# 6. Create a quick restore script
echo -e "\n${BLUE}🔧 Creating restore script...${NC}"
cat > "$BACKUP_DIR/restore.sh" << 'EOF'
#!/bin/bash

# Quick restore script for this backup
# Usage: ./restore.sh [full|schema|data]

RESTORE_TYPE=${1:-full}

case $RESTORE_TYPE in
    "full")
        echo "Restoring full database..."
        supabase db reset --linked
        psql -d your_database < full_database_dump.sql
        ;;
    "schema")
        echo "Restoring schema only..."
        psql -d your_database < schema_only.sql
        ;;
    "data")
        echo "Restoring data only..."
        psql -d your_database < data_only.sql
        ;;
    *)
        echo "Usage: $0 [full|schema|data]"
        echo "  full   - Restore complete database"
        echo "  schema - Restore schema only"
        echo "  data   - Restore data only"
        ;;
esac
EOF

chmod +x "$BACKUP_DIR/restore.sh"

# 7. Compress the backup (optional)
echo -e "\n${BLUE}🗜️  Compressing backup...${NC}"
tar -czf "${BACKUP_DIR}.tar.gz" -C "backups" "$(basename $BACKUP_DIR)"
check_success "Backup compression"

# 8. Display summary
echo -e "\n${GREEN}🎉 Backup completed successfully!${NC}"
echo -e "${BLUE}📊 Backup Summary:${NC}"
echo -e "  📁 Directory: $BACKUP_DIR"
echo -e "  🗜️  Compressed: ${BACKUP_DIR}.tar.gz"
echo -e "  📏 Size: $(du -sh $BACKUP_DIR | cut -f1)"
echo -e "  🗜️  Compressed size: $(du -sh ${BACKUP_DIR}.tar.gz | cut -f1)"

echo -e "\n${YELLOW}💡 Next steps:${NC}"
echo -e "  1. Store the backup in a secure location"
echo -e "  2. Test the restore process in a development environment"
echo -e "  3. Consider setting up automated backups"
echo -e "  4. Keep multiple backup versions"

echo -e "\n${BLUE}📋 Files created:${NC}"
ls -la "$BACKUP_DIR"
