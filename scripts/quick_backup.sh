#!/bin/bash

# Quick Database Backup Script
# Creates a simple backup with timestamp

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="backups/qids_backup_$TIMESTAMP.sql"

# Create backups directory if it doesn't exist
mkdir -p backups

echo "🗄️  Creating database backup..."
echo "📁 File: $BACKUP_FILE"

# Create the backup
supabase db dump --linked -f "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Backup completed successfully!"
    echo "📏 Size: $(du -sh $BACKUP_FILE | cut -f1)"
    echo "📁 Location: $BACKUP_FILE"
else
    echo "❌ Backup failed!"
    exit 1
fi
