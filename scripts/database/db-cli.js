#!/usr/bin/env node

/**
 * Supabase Database CLI
 * 
 * A command-line interface for interacting with the Supabase database.
 * 
 * Usage:
 *   node scripts/database/db-cli.js [command] [options]
 * 
 * Commands:
 *   list-tables                     List all tables
 *   schema [table]                  Get schema for a table
 *   query [table] [options]         Query a table
 *   insert [table] [data]           Insert data into a table
 *   update [table] [data] [filters] Update data in a table
 *   delete [table] [filters]        Delete data from a table
 *   raw [sql]                       Execute a raw SQL query
 * 
 * Examples:
 *   node scripts/database/db-cli.js list-tables
 *   node scripts/database/db-cli.js schema users
 *   node scripts/database/db-cli.js query users --limit=10
 *   node scripts/database/db-cli.js query users --eq.email=<EMAIL>
 *   node scripts/database/db-cli.js insert users '{"email":"<EMAIL>","first_name":"Test","last_name":"User"}'
 *   node scripts/database/db-cli.js update users '{"first_name":"Updated"}' '{"id":"123"}'
 *   node scripts/database/db-cli.js delete users '{"id":"123"}'
 *   node scripts/database/db-cli.js raw "SELECT * FROM users LIMIT 10"
 */

const { supabase, query, insert, update, remove, rawQuery, getTableSchema, listTables } = require('../../utils/supabaseAdmin');

// Parse command-line arguments
const args = process.argv.slice(2);
const command = args[0];

async function main() {
  try {
    switch (command) {
      case 'list-tables':
        const tables = await listTables();
        console.log('Tables:');
        tables.forEach(table => console.log(`- ${table.table_name}`));
        break;

      case 'schema':
        const table = args[1];
        if (!table) {
          console.error('Error: Table name is required');
          process.exit(1);
        }
        const schema = await getTableSchema(table);
        console.log(`Schema for table '${table}':`);
        console.table(schema);
        break;

      case 'query':
        const queryTable = args[1];
        if (!queryTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }

        // Parse options
        const options = {};
        for (let i = 2; i < args.length; i++) {
          const arg = args[i];
          if (arg.startsWith('--')) {
            const [key, value] = arg.slice(2).split('=');
            
            if (key === 'select') {
              options.select = value.split(',');
            } else if (key === 'limit') {
              options.limit = parseInt(value);
            } else if (key === 'offset') {
              options.offset = parseInt(value);
            } else if (key === 'orderBy') {
              options.orderBy = value;
            } else if (key === 'ascending') {
              options.ascending = value === 'true';
            } else if (key.startsWith('eq.')) {
              const column = key.slice(3);
              options.eq = options.eq || {};
              options.eq[column] = value;
            } else if (key.startsWith('neq.')) {
              const column = key.slice(4);
              options.neq = options.neq || {};
              options.neq[column] = value;
            } else if (key.startsWith('gt.')) {
              const column = key.slice(3);
              options.gt = options.gt || {};
              options.gt[column] = value;
            } else if (key.startsWith('lt.')) {
              const column = key.slice(3);
              options.lt = options.lt || {};
              options.lt[column] = value;
            } else if (key.startsWith('gte.')) {
              const column = key.slice(4);
              options.gte = options.gte || {};
              options.gte[column] = value;
            } else if (key.startsWith('lte.')) {
              const column = key.slice(4);
              options.lte = options.lte || {};
              options.lte[column] = value;
            } else if (key.startsWith('like.')) {
              const column = key.slice(5);
              options.like = options.like || {};
              options.like[column] = value;
            } else if (key.startsWith('ilike.')) {
              const column = key.slice(6);
              options.ilike = options.ilike || {};
              options.ilike[column] = value;
            } else if (key.startsWith('in.')) {
              const column = key.slice(3);
              options.in = options.in || {};
              options.in[column] = value.split(',');
            }
          }
        }

        const results = await query(queryTable, options);
        console.log(`Query results for table '${queryTable}':`);
        console.log(JSON.stringify(results, null, 2));
        break;

      case 'insert':
        const insertTable = args[1];
        const insertData = JSON.parse(args[2]);
        
        if (!insertTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }
        
        if (!insertData) {
          console.error('Error: Data is required');
          process.exit(1);
        }

        const insertResult = await insert(insertTable, insertData);
        console.log(`Insert result for table '${insertTable}':`);
        console.log(JSON.stringify(insertResult, null, 2));
        break;

      case 'update':
        const updateTable = args[1];
        const updateData = JSON.parse(args[2]);
        const updateFilters = JSON.parse(args[3]);
        
        if (!updateTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }
        
        if (!updateData) {
          console.error('Error: Data is required');
          process.exit(1);
        }
        
        if (!updateFilters) {
          console.error('Error: Filters are required');
          process.exit(1);
        }

        const updateResult = await update(updateTable, updateData, updateFilters);
        console.log(`Update result for table '${updateTable}':`);
        console.log(JSON.stringify(updateResult, null, 2));
        break;

      case 'delete':
        const deleteTable = args[1];
        const deleteFilters = JSON.parse(args[2]);
        
        if (!deleteTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }
        
        if (!deleteFilters) {
          console.error('Error: Filters are required');
          process.exit(1);
        }

        const deleteResult = await remove(deleteTable, deleteFilters);
        console.log(`Delete result for table '${deleteTable}':`);
        console.log(JSON.stringify(deleteResult, null, 2));
        break;

      case 'raw':
        const sql = args.slice(1).join(' ');
        
        if (!sql) {
          console.error('Error: SQL query is required');
          process.exit(1);
        }

        const rawResult = await rawQuery(sql);
        console.log('Raw query result:');
        console.log(JSON.stringify(rawResult, null, 2));
        break;

      default:
        console.error(`Error: Unknown command '${command}'`);
        console.log(`
Usage:
  node scripts/database/db-cli.js [command] [options]

Commands:
  list-tables                     List all tables
  schema [table]                  Get schema for a table
  query [table] [options]         Query a table
  insert [table] [data]           Insert data into a table
  update [table] [data] [filters] Update data in a table
  delete [table] [filters]        Delete data from a table
  raw [sql]                       Execute a raw SQL query
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
