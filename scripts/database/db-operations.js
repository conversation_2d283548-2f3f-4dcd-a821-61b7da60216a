#!/usr/bin/env node

/**
 * Supabase Database Operations
 * 
 * A script for common database operations.
 * 
 * Usage:
 *   node scripts/database/db-operations.js [operation]
 * 
 * Operations:
 *   list-users                     List all users
 *   list-kids                      List all kids
 *   list-groups                    List all groups
 *   list-memberships               List all group memberships
 *   get-user [id]                  Get user by ID
 *   get-user-by-email [email]      Get user by email
 *   get-kid [id]                   Get kid by ID
 *   get-group [id]                 Get group by ID
 *   get-user-kids [userId]         Get kids for a user
 *   get-user-groups [userId]       Get groups for a user
 *   get-group-members [groupId]    Get members of a group
 */

const { query } = require('../../utils/supabaseAdmin');

// Parse command-line arguments
const args = process.argv.slice(2);
const operation = args[0];

async function main() {
  try {
    switch (operation) {
      case 'list-users':
        const users = await query('users', { limit: 100 });
        console.log('Users:');
        console.log(JSON.stringify(users, null, 2));
        break;

      case 'list-kids':
        const kids = await query('kids', { limit: 100 });
        console.log('Kids:');
        console.log(JSON.stringify(kids, null, 2));
        break;

      case 'list-groups':
        const groups = await query('groups', { limit: 100 });
        console.log('Groups:');
        console.log(JSON.stringify(groups, null, 2));
        break;

      case 'list-memberships':
        const memberships = await query('group_memberships', { limit: 100 });
        console.log('Group Memberships:');
        console.log(JSON.stringify(memberships, null, 2));
        break;

      case 'get-user':
        const userId = args[1];
        if (!userId) {
          console.error('Error: User ID is required');
          process.exit(1);
        }
        const user = await query('users', { eq: { id: userId } });
        console.log(`User with ID '${userId}':`);
        console.log(JSON.stringify(user, null, 2));
        break;

      case 'get-user-by-email':
        const email = args[1];
        if (!email) {
          console.error('Error: Email is required');
          process.exit(1);
        }
        const userByEmail = await query('users', { eq: { email } });
        console.log(`User with email '${email}':`);
        console.log(JSON.stringify(userByEmail, null, 2));
        break;

      case 'get-kid':
        const kidId = args[1];
        if (!kidId) {
          console.error('Error: Kid ID is required');
          process.exit(1);
        }
        const kid = await query('kids', { eq: { id: kidId } });
        console.log(`Kid with ID '${kidId}':`);
        console.log(JSON.stringify(kid, null, 2));
        break;

      case 'get-group':
        const groupId = args[1];
        if (!groupId) {
          console.error('Error: Group ID is required');
          process.exit(1);
        }
        const group = await query('groups', { eq: { id: groupId } });
        console.log(`Group with ID '${groupId}':`);
        console.log(JSON.stringify(group, null, 2));
        break;

      case 'get-user-kids':
        const parentId = args[1];
        if (!parentId) {
          console.error('Error: User ID is required');
          process.exit(1);
        }
        const userKids = await query('kids', { eq: { parent_id: parentId } });
        console.log(`Kids for user with ID '${parentId}':`);
        console.log(JSON.stringify(userKids, null, 2));
        break;

      case 'get-user-groups':
        const memberId = args[1];
        if (!memberId) {
          console.error('Error: User ID is required');
          process.exit(1);
        }
        
        // Get all memberships for this user
        const userMemberships = await query('group_memberships', { eq: { user_id: memberId } });
        
        // Get all groups for these memberships
        const groupIds = userMemberships.map(membership => membership.group_id);
        const userGroups = await query('groups', { in: { id: groupIds } });
        
        console.log(`Groups for user with ID '${memberId}':`);
        console.log(JSON.stringify(userGroups, null, 2));
        break;

      case 'get-group-members':
        const gId = args[1];
        if (!gId) {
          console.error('Error: Group ID is required');
          process.exit(1);
        }
        
        // Get all memberships for this group
        const groupMemberships = await query('group_memberships', { eq: { group_id: gId } });
        
        // Get all users for these memberships
        const userIds = groupMemberships.map(membership => membership.user_id);
        const groupMembers = await query('users', { in: { id: userIds } });
        
        console.log(`Members of group with ID '${gId}':`);
        console.log(JSON.stringify(groupMembers, null, 2));
        break;

      default:
        console.error(`Error: Unknown operation '${operation}'`);
        console.log(`
Usage:
  node scripts/database/db-operations.js [operation]

Operations:
  list-users                     List all users
  list-kids                      List all kids
  list-groups                    List all groups
  list-memberships               List all group memberships
  get-user [id]                  Get user by ID
  get-user-by-email [email]      Get user by email
  get-kid [id]                   Get kid by ID
  get-group [id]                 Get group by ID
  get-user-kids [userId]         Get kids for a user
  get-user-groups [userId]       Get groups for a user
  get-group-members [groupId]    Get members of a group
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
