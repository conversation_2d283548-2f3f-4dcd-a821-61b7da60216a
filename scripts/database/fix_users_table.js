// This script fixes the users table in Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables must be set');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixUsersTable() {
  try {
    console.log('Fixing users table...');
    
    // Read the SQL migration file
    const sql = fs.readFileSync('./supabase/migrations/20240502_fix_users_table.sql', 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error fixing users table:', error);
      process.exit(1);
    }
    
    console.log('Users table fixed successfully');
  } catch (error) {
    console.error('Exception fixing users table:', error);
    process.exit(1);
  }
}

fixUsersTable();
