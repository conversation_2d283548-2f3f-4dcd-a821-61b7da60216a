#!/bin/bash

# Supabase Database Utility Script
# This script provides a convenient way to interact with your Supabase database
# using the Supabase CLI.

# Usage:
#   ./scripts/database/supabase-db.sh [command] [args...]
#
# Commands:
#   list-tables                   List all tables in the database
#   describe-table [table]        Show the structure of a table
#   query [sql]                   Execute a SQL query
#   execute-file [file]           Execute a SQL file
#   backup [file]                 Backup the database to a file
#   reset                         Reset the database (DANGEROUS!)
#   help                          Show this help message

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "Error: Supabase CLI is not installed."
    echo "Please install it using: brew install supabase/tap/supabase"
    exit 1
fi

# Check if the project is linked
if [ ! -f "supabase/.temp/project-ref" ]; then
    echo "Error: Project is not linked to Supabase."
    echo "Please run: supabase link --project-ref <project-id>"
    exit 1
fi

# Function to show help
show_help() {
    echo "Supabase Database Utility Script"
    echo ""
    echo "Usage:"
    echo "  ./scripts/database/supabase-db.sh [command] [args...]"
    echo ""
    echo "Commands:"
    echo "  list-tables                   List all tables in the database"
    echo "  describe-table [table]        Show the structure of a table"
    echo "  query [sql]                   Execute a SQL query"
    echo "  execute-file [file]           Execute a SQL file"
    echo "  backup [file]                 Backup the database to a file"
    echo "  reset                         Reset the database (DANGEROUS!)"
    echo "  help                          Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./scripts/database/supabase-db.sh list-tables"
    echo "  ./scripts/database/supabase-db.sh describe-table users"
    echo "  ./scripts/database/supabase-db.sh query \"SELECT * FROM users LIMIT 5\""
    echo "  ./scripts/database/supabase-db.sh execute-file sql_scripts/create_tables.sql"
    echo "  ./scripts/database/supabase-db.sh backup backup.sql"
}

# Main command handler
case "$1" in
    list-tables)
        echo "Listing all tables in the database..."
        supabase db execute --file - <<EOF
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY table_name;
EOF
        ;;

    describe-table)
        if [ -z "$2" ]; then
            echo "Error: Table name is required."
            echo "Usage: ./scripts/database/supabase-db.sh describe-table [table]"
            exit 1
        fi

        echo "Describing table $2..."
        supabase db execute --file - <<EOF
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM
    information_schema.columns
WHERE
    table_schema = 'public'
    AND table_name = '$2'
ORDER BY
    ordinal_position;
EOF
        ;;

    query)
        if [ -z "$2" ]; then
            echo "Error: SQL query is required."
            echo "Usage: ./scripts/database/supabase-db.sh query \"[sql]\""
            exit 1
        fi

        echo "Executing query: $2"
        supabase db execute --file - <<EOF
$2
EOF
        ;;

    execute-file)
        if [ -z "$2" ]; then
            echo "Error: SQL file path is required."
            echo "Usage: ./scripts/database/supabase-db.sh execute-file [file]"
            exit 1
        fi

        if [ ! -f "$2" ]; then
            echo "Error: File $2 does not exist."
            exit 1
        fi

        echo "Executing SQL file: $2"
        supabase db execute --file "$2"
        ;;

    backup)
        if [ -z "$2" ]; then
            BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
        else
            BACKUP_FILE="$2"
        fi

        echo "Backing up database to $BACKUP_FILE..."
        supabase db dump -f "$BACKUP_FILE"
        echo "Backup completed successfully."
        ;;

    reset)
        echo "WARNING: This will reset your database and delete all data."
        echo "This action cannot be undone."
        read -p "Are you sure you want to continue? (y/N): " confirm

        if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
            echo "Operation cancelled."
            exit 0
        fi

        echo "Resetting database..."
        supabase db reset
        echo "Database reset completed."
        ;;

    help|*)
        show_help
        ;;
esac

exit 0
