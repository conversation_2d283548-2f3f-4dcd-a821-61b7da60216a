// This script executes SQL commands on your Supabase project
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Replace these with your Supabase URL and anon key
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Function to execute SQL
async function executeSQL(sql) {
  try {
    console.log('Executing SQL...');
    const { data, error } = await supabase.rpc('pgexec', { sql });
    
    if (error) {
      console.error('Error executing SQL:', error);
      return false;
    }
    
    console.log('SQL executed successfully:', data);
    return true;
  } catch (error) {
    console.error('Exception executing SQL:', error);
    return false;
  }
}

// Main function
async function main() {
  // Enable UUID extension
  console.log('Enabling UUID extension...');
  await executeSQL('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";');
  
  // Execute announcements table script
  console.log('Creating announcements table...');
  const announcementsSQL = fs.readFileSync('./create_announcements_table.sql', 'utf8');
  await executeSQL(announcementsSQL);
  
  // Execute colectas table script
  console.log('Creating colectas table...');
  const colectasSQL = fs.readFileSync('./create_colectas_table.sql', 'utf8');
  await executeSQL(colectasSQL);
  
  // Execute polls table script
  console.log('Creating polls table...');
  const pollsSQL = fs.readFileSync('./create_polls_table.sql', 'utf8');
  await executeSQL(pollsSQL);
  
  // Execute events table script
  console.log('Creating events table...');
  const eventsSQL = fs.readFileSync('./create_events_table.sql', 'utf8');
  await executeSQL(eventsSQL);
  
  console.log('All SQL scripts executed!');
}

// Run the main function
main().catch(console.error);
