#!/usr/bin/env node

/**
 * Supabase REST API Client
 *
 * This script provides a simple way to interact with your Supabase database
 * using the Supabase REST API.
 *
 * Usage:
 *   node scripts/database/supabase-rest.js [command] [args...]
 *
 * Commands:
 *   list-tables                     List all tables in the database
 *   describe-table [table]          Show the structure of a table
 *   query [table] [options]         Query a table
 *   insert [table] [data]           Insert data into a table
 *   update [table] [data] [filters] Update data in a table
 *   delete [table] [filters]        Delete data from a table
 *
 * Examples:
 *   node scripts/database/supabase-rest.js list-tables
 *   node scripts/database/supabase-rest.js describe-table users
 *   node scripts/database/supabase-rest.js query users --limit=10
 *   node scripts/database/supabase-rest.js query users --eq.email=<EMAIL>
 *   node scripts/database/supabase-rest.js insert users '{"email":"<EMAIL>","first_name":"Test","last_name":"User"}'
 *   node scripts/database/supabase-rest.js update users '{"first_name":"Updated"}' '{"id":"123"}'
 *   node scripts/database/supabase-rest.js delete users '{"id":"123"}'
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables must be set');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// Parse command-line arguments
const args = process.argv.slice(2);
const command = args[0];

async function listTables() {
  try {
    // Query all tables in the public schema
    const { data, error } = await supabase
      .rpc('list_tables');

    if (error) {
      console.error('Error listing tables:', error.message);
      // Try a different approach
      console.log('Trying to query tables directly...');

      // Try to query some known tables
      const knownTables = ['users', 'kids', 'groups', 'group_memberships', 'announcements', 'colectas', 'polls', 'events'];

      console.log('Tables:');
      for (const table of knownTables) {
        try {
          const { data } = await supabase.from(table).select('count(*)', { count: 'exact' });
          if (data) {
            console.log(`- ${table}`);
          }
        } catch (e) {
          // Table doesn't exist or can't be accessed
        }
      }
      return;
    }

    console.log('Tables:');
    data.forEach(table => console.log(`- ${table.table_name}`));
  } catch (error) {
    console.error('Error listing tables:', error.message);
  }
}

async function describeTable(tableName) {
  try {
    const { data, error } = await supabase
      .from('information_schema.columns')
      .select('column_name, data_type, is_nullable, column_default')
      .eq('table_schema', 'public')
      .eq('table_name', tableName)
      .order('ordinal_position');

    if (error) {
      console.error(`Error describing table ${tableName}:`, error.message);
      return;
    }

    console.log(`Schema for table '${tableName}':`);
    console.table(data);
  } catch (error) {
    console.error(`Error describing table ${tableName}:`, error.message);
  }
}

async function queryTable(tableName, options) {
  try {
    let query = supabase.from(tableName).select('*');

    // Apply filters
    if (options.eq) {
      Object.entries(options.eq).forEach(([column, value]) => {
        query = query.eq(column, value);
      });
    }

    if (options.neq) {
      Object.entries(options.neq).forEach(([column, value]) => {
        query = query.neq(column, value);
      });
    }

    if (options.gt) {
      Object.entries(options.gt).forEach(([column, value]) => {
        query = query.gt(column, value);
      });
    }

    if (options.lt) {
      Object.entries(options.lt).forEach(([column, value]) => {
        query = query.lt(column, value);
      });
    }

    if (options.gte) {
      Object.entries(options.gte).forEach(([column, value]) => {
        query = query.gte(column, value);
      });
    }

    if (options.lte) {
      Object.entries(options.lte).forEach(([column, value]) => {
        query = query.lte(column, value);
      });
    }

    if (options.like) {
      Object.entries(options.like).forEach(([column, value]) => {
        query = query.like(column, value);
      });
    }

    if (options.ilike) {
      Object.entries(options.ilike).forEach(([column, value]) => {
        query = query.ilike(column, value);
      });
    }

    if (options.in) {
      Object.entries(options.in).forEach(([column, values]) => {
        query = query.in(column, values);
      });
    }

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }

    if (options.offset) {
      query = query.offset(options.offset);
    }

    // Apply ordering
    if (options.orderBy) {
      query = query.order(options.orderBy, { ascending: options.ascending !== false });
    }

    const { data, error } = await query;

    if (error) {
      console.error(`Error querying table ${tableName}:`, error.message);
      return;
    }

    console.log(`Query results for table '${tableName}':`);
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error(`Error querying table ${tableName}:`, error.message);
  }
}

async function insertData(tableName, data) {
  try {
    const { data: result, error } = await supabase
      .from(tableName)
      .insert(data)
      .select();

    if (error) {
      console.error(`Error inserting data into table ${tableName}:`, error.message);
      return;
    }

    console.log(`Insert result for table '${tableName}':`);
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`Error inserting data into table ${tableName}:`, error.message);
  }
}

async function updateData(tableName, data, filters) {
  try {
    let query = supabase.from(tableName).update(data);

    // Apply filters
    Object.entries(filters).forEach(([column, value]) => {
      query = query.eq(column, value);
    });

    const { data: result, error } = await query.select();

    if (error) {
      console.error(`Error updating data in table ${tableName}:`, error.message);
      return;
    }

    console.log(`Update result for table '${tableName}':`);
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`Error updating data in table ${tableName}:`, error.message);
  }
}

async function deleteData(tableName, filters) {
  try {
    let query = supabase.from(tableName).delete();

    // Apply filters
    Object.entries(filters).forEach(([column, value]) => {
      query = query.eq(column, value);
    });

    const { data: result, error } = await query.select();

    if (error) {
      console.error(`Error deleting data from table ${tableName}:`, error.message);
      return;
    }

    console.log(`Delete result for table '${tableName}':`);
    console.log(JSON.stringify(result, null, 2));
  } catch (error) {
    console.error(`Error deleting data from table ${tableName}:`, error.message);
  }
}

async function main() {
  try {
    switch (command) {
      case 'list-tables':
        await listTables();
        break;

      case 'describe-table':
        const tableName = args[1];
        if (!tableName) {
          console.error('Error: Table name is required');
          process.exit(1);
        }
        await describeTable(tableName);
        break;

      case 'query':
        const queryTable = args[1];
        if (!queryTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }

        // Parse options
        const options = {};
        for (let i = 2; i < args.length; i++) {
          const arg = args[i];
          if (arg.startsWith('--')) {
            const [key, value] = arg.slice(2).split('=');

            if (key === 'select') {
              options.select = value.split(',');
            } else if (key === 'limit') {
              options.limit = parseInt(value);
            } else if (key === 'offset') {
              options.offset = parseInt(value);
            } else if (key === 'orderBy') {
              options.orderBy = value;
            } else if (key === 'ascending') {
              options.ascending = value === 'true';
            } else if (key.startsWith('eq.')) {
              const column = key.slice(3);
              options.eq = options.eq || {};
              options.eq[column] = value;
            } else if (key.startsWith('neq.')) {
              const column = key.slice(4);
              options.neq = options.neq || {};
              options.neq[column] = value;
            } else if (key.startsWith('gt.')) {
              const column = key.slice(3);
              options.gt = options.gt || {};
              options.gt[column] = value;
            } else if (key.startsWith('lt.')) {
              const column = key.slice(3);
              options.lt = options.lt || {};
              options.lt[column] = value;
            } else if (key.startsWith('gte.')) {
              const column = key.slice(4);
              options.gte = options.gte || {};
              options.gte[column] = value;
            } else if (key.startsWith('lte.')) {
              const column = key.slice(4);
              options.lte = options.lte || {};
              options.lte[column] = value;
            } else if (key.startsWith('like.')) {
              const column = key.slice(5);
              options.like = options.like || {};
              options.like[column] = value;
            } else if (key.startsWith('ilike.')) {
              const column = key.slice(6);
              options.ilike = options.ilike || {};
              options.ilike[column] = value;
            } else if (key.startsWith('in.')) {
              const column = key.slice(3);
              options.in = options.in || {};
              options.in[column] = value.split(',');
            }
          }
        }

        await queryTable(queryTable, options);
        break;

      case 'insert':
        const insertTable = args[1];
        const insertData = JSON.parse(args[2]);

        if (!insertTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }

        if (!insertData) {
          console.error('Error: Data is required');
          process.exit(1);
        }

        await insertData(insertTable, insertData);
        break;

      case 'update':
        const updateTable = args[1];
        const updateData = JSON.parse(args[2]);
        const updateFilters = JSON.parse(args[3]);

        if (!updateTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }

        if (!updateData) {
          console.error('Error: Data is required');
          process.exit(1);
        }

        if (!updateFilters) {
          console.error('Error: Filters are required');
          process.exit(1);
        }

        await updateData(updateTable, updateData, updateFilters);
        break;

      case 'delete':
        const deleteTable = args[1];
        const deleteFilters = JSON.parse(args[2]);

        if (!deleteTable) {
          console.error('Error: Table name is required');
          process.exit(1);
        }

        if (!deleteFilters) {
          console.error('Error: Filters are required');
          process.exit(1);
        }

        await deleteData(deleteTable, deleteFilters);
        break;

      default:
        console.error(`Error: Unknown command '${command}'`);
        console.log(`
Usage:
  node scripts/database/supabase-rest.js [command] [args...]

Commands:
  list-tables                     List all tables in the database
  describe-table [table]          Show the structure of a table
  query [table] [options]         Query a table
  insert [table] [data]           Insert data into a table
  update [table] [data] [filters] Update data in a table
  delete [table] [filters]        Delete data from a table
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
