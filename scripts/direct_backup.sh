#!/bin/bash

# Direct Database Backup Script using pg_dump
# This bypasses Docker and connects directly to Supabase

TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="backups"
BACKUP_FILE="$BACKUP_DIR/qids_backup_$TIMESTAMP.sql"

# Create backups directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "🗄️  Creating direct database backup..."
echo "📁 File: $BACKUP_FILE"

# Check if .env file exists and source it
if [ -f .env ]; then
    source .env
    echo "✅ Loaded environment variables from .env"
else
    echo "⚠️  .env file not found. You'll need to provide database URL manually."
fi

# Extract database URL from environment or ask user
if [ -z "$DATABASE_URL" ] && [ -z "$EXPO_PUBLIC_SUPABASE_URL" ]; then
    echo "❌ No database URL found in environment variables."
    echo "Please provide your Supabase database URL:"
    echo "Format: postgresql://postgres:[password]@[host]:5432/postgres"
    read -p "Database URL: " DATABASE_URL
fi

# If we have Supabase URL but not direct database URL, construct it
if [ -z "$DATABASE_URL" ] && [ -n "$EXPO_PUBLIC_SUPABASE_URL" ]; then
    echo "🔧 Constructing database URL from Supabase URL..."
    # Extract project reference from Supabase URL
    PROJECT_REF=$(echo "$EXPO_PUBLIC_SUPABASE_URL" | sed 's/https:\/\/\([^.]*\).*/\1/')
    echo "📋 Project reference: $PROJECT_REF"
    echo "🔑 You'll need to provide your database password."
    echo "You can find it in your Supabase Dashboard > Settings > Database"
    read -s -p "Database password: " DB_PASSWORD
    echo ""
    DATABASE_URL="*******************************************************************/postgres"
fi

# Perform the backup using pg_dump
if command -v pg_dump &> /dev/null; then
    echo "🚀 Starting backup with pg_dump..."
    pg_dump "$DATABASE_URL" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        echo "✅ Backup completed successfully!"
        echo "📏 Size: $(du -sh $BACKUP_FILE | cut -f1)"
        echo "📁 Location: $BACKUP_FILE"
        
        # Create a compressed version
        gzip -c "$BACKUP_FILE" > "${BACKUP_FILE}.gz"
        echo "🗜️  Compressed backup: ${BACKUP_FILE}.gz ($(du -sh ${BACKUP_FILE}.gz | cut -f1))"
        
        # Create backup info
        cat > "$BACKUP_DIR/backup_info_$TIMESTAMP.txt" << EOF
Qids Database Backup Information
================================

Backup Date: $(date)
Backup File: $BACKUP_FILE
Compressed File: ${BACKUP_FILE}.gz
Original Size: $(du -sh $BACKUP_FILE | cut -f1)
Compressed Size: $(du -sh ${BACKUP_FILE}.gz | cut -f1)

To restore this backup:
1. psql "your_database_url" < $BACKUP_FILE
2. Or: gunzip -c ${BACKUP_FILE}.gz | psql "your_database_url"

Note: Always test restores in a development environment first!
EOF
        
        echo "📝 Backup info saved to: $BACKUP_DIR/backup_info_$TIMESTAMP.txt"
        
    else
        echo "❌ Backup failed!"
        exit 1
    fi
else
    echo "❌ pg_dump not found. Please install PostgreSQL client tools."
    echo "On macOS: brew install postgresql"
    echo "On Ubuntu: sudo apt-get install postgresql-client"
    exit 1
fi
