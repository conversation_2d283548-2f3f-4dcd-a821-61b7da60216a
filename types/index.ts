// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  nickname?: string;
  fullName: string; // Computed from firstName + lastName
  phone?: string;
  profilePicture?: string;
  createdAt: string;
}

// Kid types
export interface Kid {
  id: string;
  firstName: string;
  lastName: string;
  nickname?: string;
  fullName: string; // Computed from firstName + lastName
  birthDate: string; // Required
  dni: string; // Required
  parentId: string;
  groups?: string[];
  createdAt: string;
}

// Institution types
export interface Institution {
  id: string;
  name: string;
  address?: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

// Group types
export interface Group {
  id: string;
  institutionId: string;
  institutionName: string;
  institution?: Institution; // Optional populated field
  sala: string;
  año: number;
  nombre?: string;
  createdBy: string;
  createdAt: string;
  members?: any[];
  kids?: any[];
}

export interface GroupMembership {
  id: string;
  parentId: string;
  kidId: string;
  groupId: string;
  isReferente: boolean;
}

// Payment types
export interface Payment {
  id: string;
  parentId: string;
  kidId: string;
  groupId: string;
  amount: number;
  currency: string;
  status: 'PAID' | 'PENDING' | 'UNPAID';
  paymentReceiptUrl?: string;
  createdAt: string;
}

// Announcement types
export type AnnouncementType = 'ANNOUNCEMENT' | 'REMINDER' | 'IMPORTANT_DATE';

export interface Announcement {
  id: string;
  groupId: string;
  title: string;
  message: string;
  type: AnnouncementType;
  date?: string;
  createdBy?: string;
  createdAt: string;
}

// Colecta types
export type ColectaStatus = 'ACTIVA' | 'FINALIZADA' | 'CANCELADA';

export type ContributionStatus = 'REGISTRADA' | 'DESCARTADA';

export interface Colecta {
  id: string;
  groupId: string;
  motivo: string;
  descripcion?: string;
  baseAmount: number;
  fechaFin: string;
  status: ColectaStatus;
  totalCollected?: number;
  totalContributions?: number;
  accountInfo?: string;
  titularCuenta?: string;
  cuitCuil?: string;
  aliasCbu?: string;
  createdBy?: string;
  createdAt: string;
}

export interface Contribution {
  id: string;
  colectaId: string;
  kidId: string;
  amount: number;
  paymentMethod?: string;
  paymentReference?: string;
  proofOfPaymentUrl?: string;
  status: ContributionStatus;
  createdAt: string;
}

// Notification types
export type NotificationType =
  | 'NEW_ANNOUNCEMENT'
  | 'NEW_COLECTA'
  | 'COLECTA_REMINDER'
  | 'PAYMENT_CONFIRMATION'
  | 'PAYMENT_REMINDER'
  | 'GROUP_INVITATION'
  | 'NEW_POLL'
  | 'POLL_REMINDER'
  | 'NEW_EVENT';

export interface Notification {
  id: string;
  userId: string;
  groupId?: string;
  colectaId?: string;
  pollId?: string;
  eventId?: string;
  type: NotificationType;
  message: string;
  status: 'READ' | 'UNREAD';
  createdAt: string;
}

// Poll/Voting types
export type PollStatus = 'DRAFT' | 'ACTIVE' | 'CLOSED';

export interface PollOption {
  id: string;
  text: string;
  votes: number;
}

export interface Poll {
  id: string;
  groupId: string;
  title: string;
  description?: string;
  options: PollOption[];
  endDate: string;
  status: PollStatus;
  allowMultipleChoice?: boolean;
  createdBy: string;
  createdAt: string;
}

export interface Vote {
  id: string;
  pollId: string;
  userId: string;
  optionId: string;
  createdAt: string;
}

// Event types
export type EventType = 'MEETING' | 'ACTIVITY' | 'HOLIDAY' | 'EXAM' | 'OTHER';

export interface Event {
  id: string;
  groupId: string;
  title: string;
  description?: string;
  type: EventType;
  date: string;
  endDate?: string;
  location?: string;
  createdBy: string;
  createdAt: string;
}