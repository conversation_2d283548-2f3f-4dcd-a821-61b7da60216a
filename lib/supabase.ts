import 'react-native-url-polyfill/auto';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import { EXPO_PUBLIC_SUPABASE_URL, EXPO_PUBLIC_SUPABASE_ANON_KEY } from '@env';

// Debug environment variables
console.log('Supabase config debug:', {
  url: EXPO_PUBLIC_SUPABASE_URL,
  keyLength: EXPO_PUBLIC_SUPABASE_ANON_KEY?.length || 0,
  hasUrl: !!EXPO_PUBLIC_SUPABASE_URL,
  hasKey: !!EXPO_PUBLIC_SUPABASE_ANON_KEY
});

export const supabase = createClient(
  EXPO_PUBLIC_SUPABASE_URL,
  EXPO_PUBLIC_SUPABASE_ANON_KEY,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
    global: {
      // Add fetch options for better network handling
      fetch: (url, options = {}) => {
        // Create a timeout promise
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 30000);
        });

        // Ensure API key is always included in headers
        const headers = {
          'Content-Type': 'application/json',
          'apikey': EXPO_PUBLIC_SUPABASE_ANON_KEY,
          ...options.headers,
        };

        // Race between fetch and timeout
        return Promise.race([
          fetch(url, {
            ...options,
            headers,
          }),
          timeoutPromise
        ]);
      },
    },
    // Add retries for better resilience
    db: {
      schema: 'public',
    },
    realtime: {
      params: {
        eventsPerSecond: 10,
      },
    },
  });

// Add auth state change listener to handle token expiration
supabase.auth.onAuthStateChange(async (event, session) => {
  console.log('Supabase auth state change:', event, session ? 'session exists' : 'no session');

  if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
    console.log('Auth event:', event);
  }

  if (event === 'SIGNED_OUT' && !session) {
    // Clear any cached data when user is signed out
    console.log('User signed out, clearing storage');
    try {
      await AsyncStorage.removeItem('auth-storage');
      await AsyncStorage.removeItem('kids-storage');
      await AsyncStorage.removeItem('groups-storage');
      await AsyncStorage.removeItem('polls-storage');
      await AsyncStorage.removeItem('notifications-storage');
    } catch (error) {
      console.error('Error clearing storage on signout:', error);
    }
  }
});
