# Parentium

<p align="center">
  <img src="./assets/icon.png" alt="Parentium Logo" width="120" />
</p>

Parentium is a comprehensive mobile application designed to facilitate communication and coordination between parents and schools. It provides a platform for parents to join school groups, manage their children's information, coordinate events, create announcements, organize collections (colectas), and conduct polls.

## 🌟 Features

### 👨‍👩‍👧‍👦 User & Kids Management
- User registration and authentication with email verification
- Profile management for parents
- Add and manage children's information
- Link children to school groups

### 🏫 Groups
- Create school groups with grade/class information
- Join existing groups
- Assign referentes (group administrators)
- View group members and their children

### 📢 Communication
- Create and view announcements
- Organize events with date and location
- Calendar view of all upcoming events
- Notification system for important updates

### 💰 Collections (Colectas)
- Create money collections for gifts, events, or group needs
- Track contributions from group members
- View collection status and deadlines

### 📊 Polls
- Create polls for group decisions
- Vote on polls
- View poll results

## 🚀 Technology Stack

- **Frontend**: React Native with Expo
- **State Management**: Zustand
- **Backend & Authentication**: Supabase
- **Navigation**: Expo Router
- **UI Components**: Custom components with Lucide React Native icons

## 📁 Project Structure

- `app/`: Main application code using Expo Router
  - `(auth)/`: Authentication screens
  - `(tabs)/`: Main tab navigation screens
  - `group/`: Group-related screens
  - `kids/`: Kid management screens
- `assets/`: Images, fonts, and other static assets
- `components/`: Reusable UI components
- `constants/`: Application constants like colors, sizes, etc.
- `docs/`: Documentation files
- `lib/`: Library configurations (Supabase, etc.)
- `scripts/`: Utility scripts
  - `database/`: Database-related scripts
- `services/`: API and service integrations
- `sql_scripts/`: SQL scripts for database setup and maintenance
- `store/`: State management using Zustand
- `supabase/`: Supabase configuration and setup
- `types/`: TypeScript type definitions
- `utils/`: Utility functions

## 🛠️ Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone https://github.com/nicodeangelis/parentium.git
cd parentium
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env` file in the root directory with your Supabase credentials:
```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Start the development server:
```bash
npm start
```

5. Run on iOS or Android:
```bash
npm run ios
# or
npm run android
```

## 🗄️ Database Setup

The application uses Supabase for backend services. Follow these steps to set up your database:

1. Create a new Supabase project
2. Go to the SQL Editor in your Supabase dashboard
3. Run the SQL scripts from the `supabase/create_tables.sql` file
4. Configure authentication settings in the Supabase dashboard

For detailed instructions, see the documentation in `docs/supabase_sql_instructions.md`.

### Database Notes

#### Colecta Contributions Count

The application now calculates contribution counts on demand rather than relying on stored counters. This ensures that the displayed count is always accurate and in sync with the actual data.

If you're upgrading from a previous version that used the `total_contributions` counter in the database, you can run the following SQL script to mark this field as deprecated:

```bash
supabase db execute --file sql_scripts/remove_total_contributions_counter.sql
```

The counter field is kept in the database for backward compatibility but is no longer used by the application.

## 📱 User Flows

### Registration & Login
1. User registers with email and password
2. Email verification is sent
3. User verifies email and logs in
4. User completes profile information

### Creating a Group
1. User navigates to Groups tab
2. User taps "Create New Group"
3. User enters school, class, and year information
4. User selects a child to add to the group
5. Group is created with the user as a referente

### Joining a Group
1. User navigates to Groups tab
2. User searches for a group
3. User taps on the group and selects "Join Group"
4. User selects which children to add to the group
5. User joins the group

### Creating a Collection (Colecta)
1. Referente navigates to a group
2. Referente taps on "Colectas" tab
3. Referente taps "Create Collection"
4. Referente enters collection details (reason, amount, deadline)
5. Collection is created and visible to all group members

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Contact

For support or inquiries, please contact [<EMAIL>](mailto:<EMAIL>).
