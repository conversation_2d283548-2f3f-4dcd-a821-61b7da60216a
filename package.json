{"name": "parentium_v2_new2", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "start-dev": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "clear": "expo start --clear"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^7.1.9", "@react-navigation/stack": "^7.3.2", "@supabase/supabase-js": "^2.49.4", "@types/react-native-calendars": "^1.1264.7", "@types/uuid": "^10.0.0", "date-fns": "^4.1.0", "expo": "~52.0.46", "expo-constants": "~17.0.8", "expo-font": "~13.0.4", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-router": "~4.0.21", "expo-status-bar": "~2.0.1", "lucide-react-native": "^0.487.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-native": "0.76.9", "react-native-calendars": "^1.1312.0", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-get-random-values": "~1.11.0", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "^4.4.0", "react-native-svg": "15.8.0", "react-native-svg-transformer": "^1.5.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.19.13", "tailwindcss": "^4.1.3", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~18.3.12", "babel-plugin-module-resolver": "^5.0.2", "dotenv": "^16.5.0", "typescript": "^5.3.3"}, "private": true}