/**
 * <PERSON><PERSON><PERSON> to upload the auth-redirect.html file to Supabase storage
 * 
 * Usage:
 * node upload-auth-redirect.js
 */

const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Get Supabase URL and key from environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Error: EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY environment variables must be set');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function uploadAuthRedirect() {
  try {
    // Read the HTML file
    const fileContent = fs.readFileSync('public/auth-redirect.html');

    // Delete the existing file if it exists
    const { error: deleteError } = await supabase
      .storage
      .from('publicbucket')
      .remove(['auth-redirect.html']);

    if (deleteError) {
      console.error('Error deleting file:', deleteError);
    } else {
      console.log('Successfully deleted existing file');
    }

    // Upload the new file
    const { data, error } = await supabase
      .storage
      .from('publicbucket')
      .upload('auth-redirect.html', fileContent, {
        contentType: 'text/html',
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Error uploading file:', error);
    } else {
      console.log('Successfully uploaded file:', data);
      
      // Get the public URL
      const { data: publicUrlData } = supabase
        .storage
        .from('publicbucket')
        .getPublicUrl('auth-redirect.html');
      
      console.log('Public URL:', publicUrlData.publicUrl);
    }
  } catch (error) {
    console.error('Error:', error);
  }
}

uploadAuthRedirect();
