-- Simple fix for group visibility issues without functions

-- First, let's drop all problematic policies
DROP POLICY IF EXISTS "Group memberships in my groups" ON public.group_memberships;
DROP POLICY IF EXISTS "Kids in my groups" ON public.kids;
DROP POLICY IF EXISTS "View memberships in my groups" ON public.group_memberships;
DROP POLICY IF EXISTS "View kids in my groups" ON public.kids;
DROP POLICY IF EXISTS "View colectas in my groups" ON public.colectas;
DROP POLICY IF EXISTS "View contributions in my groups" ON public.contributions;

-- Drop the functions if they exist
DROP FUNCTION IF EXISTS public.user_is_member_of_group(uuid);
DROP FUNCTION IF EXISTS public.user_created_group(uuid);

-- STEP 1: Create a materialized view that stores group membership information
-- This view will be used by policies instead of complex queries
DROP MATERIALIZED VIEW IF EXISTS public.user_group_memberships;
CREATE MATERIALIZED VIEW public.user_group_memberships AS
SELECT DISTINCT
    auth_uid,
    group_id
FROM (
    -- Groups the user created
    SELECT 
        created_by as auth_uid,
        id as group_id
    FROM 
        public.groups
    
    UNION
    
    -- Groups the user is a member of
    SELECT 
        user_id as auth_uid,
        group_id
    FROM 
        public.group_memberships
) AS combined_memberships;

-- Create an index for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_auth_uid ON public.user_group_memberships(auth_uid);
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_group_id ON public.user_group_memberships(group_id);

-- STEP 2: Create a function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_user_group_memberships()
RETURNS TRIGGER AS $$
BEGIN
    REFRESH MATERIALIZED VIEW public.user_group_memberships;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to refresh the view when data changes
DROP TRIGGER IF EXISTS refresh_user_group_memberships_groups ON public.groups;
CREATE TRIGGER refresh_user_group_memberships_groups
AFTER INSERT OR UPDATE OR DELETE ON public.groups
FOR EACH STATEMENT
EXECUTE FUNCTION refresh_user_group_memberships();

DROP TRIGGER IF EXISTS refresh_user_group_memberships_memberships ON public.group_memberships;
CREATE TRIGGER refresh_user_group_memberships_memberships
AFTER INSERT OR UPDATE OR DELETE ON public.group_memberships
FOR EACH STATEMENT
EXECUTE FUNCTION refresh_user_group_memberships();

-- STEP 3: Create simple policies using the materialized view

-- 1. Policy for viewing group memberships
CREATE POLICY "Simple view memberships in my groups"
ON public.group_memberships FOR SELECT
USING (
    EXISTS (
        SELECT 1 
        FROM public.user_group_memberships 
        WHERE user_group_memberships.auth_uid = auth.uid()
        AND user_group_memberships.group_id = group_memberships.group_id
    )
);

-- 2. Policy for viewing kids in my groups
CREATE POLICY "Simple view kids in my groups"
ON public.kids FOR SELECT
USING (
    parent_id = auth.uid() OR
    EXISTS (
        SELECT 1 
        FROM public.group_memberships 
        JOIN public.user_group_memberships ON group_memberships.group_id = user_group_memberships.group_id
        WHERE user_group_memberships.auth_uid = auth.uid()
        AND group_memberships.kid_id = kids.id
    )
);

-- 3. Policy for viewing colectas in my groups
CREATE POLICY "Simple view colectas in my groups"
ON public.colectas FOR SELECT
USING (
    EXISTS (
        SELECT 1 
        FROM public.user_group_memberships 
        WHERE user_group_memberships.auth_uid = auth.uid()
        AND user_group_memberships.group_id = colectas.group_id
    )
);

-- 4. Policy for viewing contributions in colectas in my groups
CREATE POLICY "Simple view contributions in my groups"
ON public.contributions FOR SELECT
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 
        FROM public.colectas 
        JOIN public.user_group_memberships ON colectas.group_id = user_group_memberships.group_id
        WHERE user_group_memberships.auth_uid = auth.uid()
        AND colectas.id = contributions.colecta_id
    )
);

-- Initial refresh of the materialized view
REFRESH MATERIALIZED VIEW public.user_group_memberships;
