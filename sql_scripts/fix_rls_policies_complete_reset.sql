-- COMPLETE RESET OF ALL RLS POLICIES
-- This script will completely disable <PERSON><PERSON> on all tables to fix the infinite recursion issues

-- Disable <PERSON><PERSON> on all tables
ALTER TABLE IF EXISTS public.kids DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.group_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.announcements DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.colectas DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.contributions DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.polls DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.poll_options DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.poll_votes DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.events DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.event_attendees DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.notifications DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Kids basic policy" ON public.kids;
DROP POLICY IF EXISTS "Kids in my groups" ON public.kids;
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can insert their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can update their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can delete their own kids" ON public.kids;

DROP POLICY IF EXISTS "Groups basic policy" ON public.groups;
DROP POLICY IF EXISTS "Groups member policy" ON public.groups;
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they created" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON public.groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.groups;

DROP POLICY IF EXISTS "Group memberships basic policy" ON public.group_memberships;
DROP POLICY IF EXISTS "Group memberships in my groups" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can view their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can update their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can delete their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Group creators can view all memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create memberships for their kids" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can view group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create group memberships" ON public.group_memberships;

-- Drop policies for other tables as well
DROP POLICY IF EXISTS "Users can view announcements" ON public.announcements;
DROP POLICY IF EXISTS "Group referentes can create announcements" ON public.announcements;
DROP POLICY IF EXISTS "Group referentes can update announcements" ON public.announcements;
DROP POLICY IF EXISTS "Group referentes can delete announcements" ON public.announcements;

DROP POLICY IF EXISTS "Users can view colectas" ON public.colectas;
DROP POLICY IF EXISTS "Group referentes can create colectas" ON public.colectas;
DROP POLICY IF EXISTS "Group referentes can update colectas" ON public.colectas;
DROP POLICY IF EXISTS "Group referentes can delete colectas" ON public.colectas;

DROP POLICY IF EXISTS "Users can view contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can create contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can update their contributions" ON public.contributions;
DROP POLICY IF EXISTS "Users can delete their contributions" ON public.contributions;

DROP POLICY IF EXISTS "Users can view polls" ON public.polls;
DROP POLICY IF EXISTS "Group referentes can create polls" ON public.polls;
DROP POLICY IF EXISTS "Group referentes can update polls" ON public.polls;
DROP POLICY IF EXISTS "Group referentes can delete polls" ON public.polls;

DROP POLICY IF EXISTS "Users can view poll options" ON public.poll_options;
DROP POLICY IF EXISTS "Group referentes can create poll options" ON public.poll_options;
DROP POLICY IF EXISTS "Group referentes can update poll options" ON public.poll_options;
DROP POLICY IF EXISTS "Group referentes can delete poll options" ON public.poll_options;

DROP POLICY IF EXISTS "Users can view poll votes" ON public.poll_votes;
DROP POLICY IF EXISTS "Users can create poll votes" ON public.poll_votes;
DROP POLICY IF EXISTS "Users can update their poll votes" ON public.poll_votes;
DROP POLICY IF EXISTS "Users can delete their poll votes" ON public.poll_votes;

DROP POLICY IF EXISTS "Users can view events" ON public.events;
DROP POLICY IF EXISTS "Group referentes can create events" ON public.events;
DROP POLICY IF EXISTS "Group referentes can update events" ON public.events;
DROP POLICY IF EXISTS "Group referentes can delete events" ON public.events;

DROP POLICY IF EXISTS "Users can view event attendees" ON public.event_attendees;
DROP POLICY IF EXISTS "Users can create event attendees" ON public.event_attendees;
DROP POLICY IF EXISTS "Users can update their event attendees" ON public.event_attendees;
DROP POLICY IF EXISTS "Users can delete their event attendees" ON public.event_attendees;

DROP POLICY IF EXISTS "Users can view their notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can update their notifications" ON public.notifications;
DROP POLICY IF EXISTS "Users can delete their notifications" ON public.notifications;
