-- Fix colecta total_contributions count
-- This script recalculates the total_contributions for each colecta based on the actual count of contributions

-- First, let's create a function to update the total_contributions for a specific colecta
CREATE OR REPLACE FUNCTION fix_colecta_contributions_count(colecta_id UUID)
RETURNS VOID AS $$
DECLARE
    actual_count INTEGER;
BEGIN
    -- Get the actual count of contributions for this colecta
    SELECT COUNT(*) INTO actual_count
    FROM public.contributions
    WHERE colecta_id = colecta_id;
    
    -- Update the colecta with the correct count
    UPDATE public.colectas
    SET total_contributions = actual_count
    WHERE id = colecta_id;
    
    RAISE NOTICE 'Updated colecta % with % contributions', colecta_id, actual_count;
END;
$$ LANGUAGE plpgsql;

-- Now, let's fix all colectas
DO $$
DECLARE
    colecta_record RECORD;
    contribution_count INTEGER;
BEGIN
    FOR colecta_record IN SELECT id, motivo, total_contributions FROM public.colectas LOOP
        -- Count actual contributions
        SELECT COUNT(*) INTO contribution_count
        FROM public.contributions
        WHERE colecta_id = colecta_record.id;
        
        -- If the counts don't match, update the colecta
        IF colecta_record.total_contributions != contribution_count THEN
            UPDATE public.colectas
            SET total_contributions = contribution_count
            WHERE id = colecta_record.id;
            
            RAISE NOTICE 'Fixed colecta % (%) - Changed count from % to %', 
                colecta_record.motivo, colecta_record.id, colecta_record.total_contributions, contribution_count;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'Finished fixing colecta contribution counts';
END;
$$;

-- Return a confirmation message
SELECT 'Colecta contribution counts have been fixed.' AS result;
