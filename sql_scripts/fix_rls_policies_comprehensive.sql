-- Comprehensive RLS policies with careful attention to avoid circular references

-- First, disable all RLS to start fresh
ALTER TABLE IF EXISTS public.kids DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.group_memberships DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can insert their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can update their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can delete their own kids" ON public.kids;

DROP POLICY IF EXISTS "Users can view their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can update their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can delete their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Group creators can view all memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create memberships for their kids" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can view group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create group memberships" ON public.group_memberships;

DROP POLICY IF EXISTS "Users can view groups they created" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON public.groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;

-- Now re-enable RLS
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_memberships ENABLE ROW LEVEL SECURITY;

-- 1. Kids policies - these are straightforward
CREATE POLICY "Kids select policy"
ON public.kids FOR SELECT
USING (parent_id = auth.uid());

CREATE POLICY "Kids insert policy"
ON public.kids FOR INSERT
WITH CHECK (parent_id = auth.uid());

CREATE POLICY "Kids update policy"
ON public.kids FOR UPDATE
USING (parent_id = auth.uid());

CREATE POLICY "Kids delete policy"
ON public.kids FOR DELETE
USING (parent_id = auth.uid());

-- 2. Groups policies - keep these simple
CREATE POLICY "Groups select created policy"
ON public.groups FOR SELECT
USING (created_by = auth.uid());

-- We'll add a second policy for groups the user is a member of later

CREATE POLICY "Groups insert policy"
ON public.groups FOR INSERT
WITH CHECK (created_by = auth.uid());

CREATE POLICY "Groups update policy"
ON public.groups FOR UPDATE
USING (created_by = auth.uid());

CREATE POLICY "Groups delete policy"
ON public.groups FOR DELETE
USING (created_by = auth.uid());

-- 3. Group memberships policies - start with basic ones
CREATE POLICY "Group memberships select policy"
ON public.group_memberships FOR SELECT
USING (user_id = auth.uid());

CREATE POLICY "Group memberships insert policy"
ON public.group_memberships FOR INSERT
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Group memberships update policy"
ON public.group_memberships FOR UPDATE
USING (user_id = auth.uid());

CREATE POLICY "Group memberships delete policy"
ON public.group_memberships FOR DELETE
USING (user_id = auth.uid());

-- 4. Now add the additional policy for groups - users can see groups they're members of
-- This depends on group_memberships but not in a circular way
CREATE POLICY "Groups select member policy"
ON public.groups FOR SELECT
USING (
    id IN (
        SELECT group_id 
        FROM public.group_memberships 
        WHERE user_id = auth.uid()
    )
);

-- 5. Finally, add additional policy for group memberships - group creators can see all memberships
-- This depends on groups but not in a circular way
CREATE POLICY "Group memberships creator policy"
ON public.group_memberships FOR SELECT
USING (
    group_id IN (
        SELECT id 
        FROM public.groups 
        WHERE created_by = auth.uid()
    )
);
