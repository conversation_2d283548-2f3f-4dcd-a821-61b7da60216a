-- Step 5: Create or update the trigger for new user creation
DO $$
DECLARE
    users_trigger_exists BOOLEAN;
BEGIN
    -- Check if the user creation trigger exists
    SELECT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'on_auth_user_created'
    ) INTO users_trigger_exists;

    -- Create the trigger if it doesn't exist or recreate it
    IF users_trigger_exists THEN
        DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
    END IF;
    
    CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();
    
    RAISE NOTICE 'Created/updated trigger for automatic user profile creation';
END $$;
