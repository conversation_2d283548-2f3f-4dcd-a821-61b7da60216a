-- TEMPORARY SOLUTION: Disable RLS on specific tables for testing
-- This is not a permanent solution but will help identify if <PERSON><PERSON> is the issue

-- Disable RLS on the problematic tables
ALTER TABLE public.group_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.colectas DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.contributions DISABLE ROW LEVEL SECURITY;

-- Keep RLS enabled on kids and groups to maintain basic security
-- But simplify the policies to avoid recursion

-- Drop existing policies on kids
DROP POLICY IF EXISTS "Kids basic policy" ON public.kids;
DROP POLICY IF EXISTS "Kids in my groups" ON public.kids;
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Simple view kids in my groups" ON public.kids;
DROP POLICY IF EXISTS "View kids in my groups" ON public.kids;

-- Create a simple policy for kids
CREATE POLICY "Simple kids policy"
ON public.kids FOR SELECT
USING (parent_id = auth.uid());

-- Drop existing policies on groups
DROP POLICY IF EXISTS "Groups basic policy" ON public.groups;
DROP POLICY IF EXISTS "Groups member policy" ON public.groups;
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they created" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON public.groups;

-- Create a simple policy for groups
CREATE POLICY "Simple groups policy"
ON public.groups FOR SELECT
USING (
    created_by = auth.uid() OR
    id IN (
        SELECT group_id 
        FROM public.group_memberships 
        WHERE user_id = auth.uid()
    )
);
