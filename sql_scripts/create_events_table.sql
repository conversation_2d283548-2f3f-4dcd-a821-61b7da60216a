-- Create events table
CREATE TABLE IF NOT EXISTS public.events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL,
    date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    location TEXT,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create event attendees table
CREATE TABLE IF NOT EXISTS public.event_attendees (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_id UUID NOT NULL REFERENCES public.events(id) ON DELETE CASCADE,
    kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(event_id, kid_id)
);

-- Add RLS policies for events
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select events for groups they are members of
DROP POLICY IF EXISTS "Users can view events for their groups" ON public.events;
CREATE POLICY "Users can view events for their groups" ON public.events
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = events.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert events for groups they are members of
DROP POLICY IF EXISTS "Users can create events for their groups" ON public.events;
CREATE POLICY "Users can create events for their groups" ON public.events
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = events.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own events
DROP POLICY IF EXISTS "Users can update their own events" ON public.events;
CREATE POLICY "Users can update their own events" ON public.events
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own events
DROP POLICY IF EXISTS "Users can delete their own events" ON public.events;
CREATE POLICY "Users can delete their own events" ON public.events
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for event attendees
ALTER TABLE public.event_attendees ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select event attendees for events in their groups
DROP POLICY IF EXISTS "Users can view event attendees for their groups" ON public.event_attendees;
CREATE POLICY "Users can view event attendees for their groups" ON public.event_attendees
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.events
            JOIN public.group_memberships ON events.group_id = group_memberships.group_id
            WHERE events.id = event_attendees.event_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert event attendees for their kids
DROP POLICY IF EXISTS "Users can create event attendees for their kids" ON public.event_attendees;
CREATE POLICY "Users can create event attendees for their kids" ON public.event_attendees
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to update event attendees for their kids
DROP POLICY IF EXISTS "Users can update event attendees for their kids" ON public.event_attendees;
CREATE POLICY "Users can update event attendees for their kids" ON public.event_attendees
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to delete event attendees for their kids
DROP POLICY IF EXISTS "Users can delete event attendees for their kids" ON public.event_attendees;
CREATE POLICY "Users can delete event attendees for their kids" ON public.event_attendees
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = event_attendees.kid_id
            AND kids.parent_id = auth.uid()
        )
    );
