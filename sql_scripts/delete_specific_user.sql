-- Delete specific user by ID from auth.users
-- Execute this in the Supabase SQL Editor

-- First, check if the user exists by ID
SELECT id, email, created_at FROM auth.users 
WHERE id = '1d6a3e3c-5bc-4e85-b1c9-5227f7cca5f7';

-- Delete the user directly by ID
DELETE FROM auth.users 
WHERE id = '1d6a3e3c-5bc-4e85-b1c9-5227f7cca5f7';

-- Confirm the user is deleted
SELECT COUNT(*) FROM auth.users 
WHERE id = '1d6a3e3c-5bc-4e85-b1c9-5227f7cca5f7';
