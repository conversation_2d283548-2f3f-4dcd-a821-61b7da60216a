-- Add payment detail fields to colectas table
ALTER TABLE public.colectas ADD COLUMN IF NOT EXISTS titular_cuenta TEXT;
ALTER TABLE public.colectas ADD COLUMN IF NOT EXISTS cuit_cuil TEXT;
ALTER TABLE public.colectas ADD COLUMN IF NOT EXISTS alias_cbu TEXT;

-- Update existing records to have empty values for these fields
UPDATE public.colectas SET 
  titular_cuenta = '',
  cuit_cuil = '',
  alias_cbu = ''
WHERE titular_cuenta IS NULL OR cuit_cuil IS NULL OR alias_cbu IS NULL;

-- Make the fields NOT NULL for future records
ALTER TABLE public.colectas ALTER COLUMN titular_cuenta SET NOT NULL;
ALTER TABLE public.colectas ALTER COLUMN cuit_cuil SET NOT NULL;
ALTER TABLE public.colectas ALTER COLUMN alias_cbu SET NOT NULL;
