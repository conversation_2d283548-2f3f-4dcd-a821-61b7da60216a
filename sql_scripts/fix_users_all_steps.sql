-- Step 1: Check if users table exists and create it if needed
DO $$
DECLARE
    users_table_exists BOOLEAN;
BEGIN
    -- Check if users table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'users'
    ) INTO users_table_exists;

    -- If users table doesn't exist, create it with the correct structure
    IF NOT users_table_exists THEN
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) PRIMARY KEY,
            email TEXT NOT NULL,
            first_name TEXT NOT NULL DEFAULT 'Usuario',
            last_name TEXT NOT NULL DEFAULT 'Nuevo',
            nickname TEXT,
            full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
            phone TEXT,
            profile_picture TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );
        
        RAISE NOTICE 'Created users table with correct structure';
    END IF;
END $$;

-- Step 2: Check if first_name column exists and add it if needed
DO $$
DECLARE
    first_name_exists BOOLEAN;
    full_name_is_regular BOOLEAN;
BEGIN
    -- Check if first_name column exists
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'first_name'
    ) INTO first_name_exists;
    
    -- Check if full_name is a regular column (not generated)
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name'
        AND is_generated = 'NEVER'
    ) INTO full_name_is_regular;
    
    -- If first_name doesn't exist
    IF NOT first_name_exists THEN
        -- If full_name exists as a regular column, we need to modify the table
        IF full_name_is_regular THEN
            -- Backup the full_name values
            ALTER TABLE public.users ADD COLUMN temp_full_name TEXT;
            UPDATE public.users SET temp_full_name = full_name;
            
            -- Drop the full_name column
            ALTER TABLE public.users DROP COLUMN full_name;
            
            -- Add the new columns
            ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
            ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
            
            -- Try to split the full_name into first_name and last_name
            UPDATE public.users 
            SET 
                first_name = CASE 
                    WHEN temp_full_name IS NULL THEN 'Usuario'
                    WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from 1 for position(' ' in temp_full_name) - 1)
                    ELSE temp_full_name
                END,
                last_name = CASE
                    WHEN temp_full_name IS NULL THEN 'Nuevo'
                    WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from position(' ' in temp_full_name) + 1)
                    ELSE 'Nuevo'
                END;
            
            -- Add the generated full_name column
            ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
            
            -- Drop the temporary column
            ALTER TABLE public.users DROP COLUMN temp_full_name;
            
            RAISE NOTICE 'Modified users table to use first_name, last_name, and generated full_name';
        ELSE
            -- Just add the missing columns
            ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
            ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
            
            -- Check if full_name column exists
            IF NOT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name'
            ) THEN
                ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
            END IF;
            
            RAISE NOTICE 'Added first_name, last_name, and generated full_name columns to users table';
        END IF;
    END IF;
END $$;

-- Step 3: Check if nickname column exists and add it if needed
DO $$
BEGIN
    -- Check if nickname column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'nickname'
    ) THEN
        ALTER TABLE public.users ADD COLUMN nickname TEXT;
        RAISE NOTICE 'Added nickname column to users table';
    END IF;
END $$;

-- Step 4: Create or replace the function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, first_name, last_name, nickname)
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'first_name', 'Usuario'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', 'Nuevo'),
        NEW.raw_user_meta_data->>'nickname'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 5: Create or update the trigger for new user creation
DO $$
DECLARE
    users_trigger_exists BOOLEAN;
BEGIN
    -- Check if the user creation trigger exists
    SELECT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'on_auth_user_created'
    ) INTO users_trigger_exists;

    -- Create the trigger if it doesn't exist or recreate it
    IF users_trigger_exists THEN
        DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
    END IF;
    
    CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();
    
    RAISE NOTICE 'Created/updated trigger for automatic user profile creation';
END $$;

-- Step 6: Create or update the function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create the trigger for updated_at if it doesn't exist
DO $$
DECLARE
    update_trigger_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'update_users_updated_at'
    ) INTO update_trigger_exists;

    IF NOT update_trigger_exists THEN
        CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON public.users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE 'Created trigger for updating updated_at column';
    END IF;
END $$;

-- Confirmation message
SELECT 'User registration system has been fixed.' AS result;
