-- Check for existing users and fix permissions
-- Execute this in the Supabase SQL Editor

-- Check if the email already exists in auth.users
SELECT id, email, created_at, last_sign_in_at, confirmed_at
FROM auth.users
WHERE email = '<EMAIL>';

-- Check if the email exists in public.users
SELECT id, email, first_name, last_name, created_at
FROM public.users
WHERE email = '<EMAIL>';

-- Fix permissions for the auth schema
GRANT USAGE ON SCHEMA auth TO anon, authenticated, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA auth TO anon, authenticated, service_role;

-- Fix permissions for the public schema
GRANT USAGE ON SCHEMA public TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated, service_role;
GRANT SELECT, INSERT ON ALL TABLES IN SCHEMA public TO anon;

-- Check for any orphaned users (in auth but not in public)
SELECT au.id, au.email, au.created_at
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL;
