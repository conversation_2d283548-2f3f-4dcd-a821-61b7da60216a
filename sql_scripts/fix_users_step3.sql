-- Step 3: Check if nickname column exists and add it if needed
DO $$
BEGIN
    -- Check if nickname column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'nickname'
    ) THEN
        ALTER TABLE public.users ADD COLUMN nickname TEXT;
        RAISE NOTICE 'Added nickname column to users table';
    END IF;
END $$;
