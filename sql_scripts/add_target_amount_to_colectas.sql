-- Add target_amount column to colectas table
-- This script adds the new "Total a Recaudar" field to existing colectas

-- Add the target_amount column
ALTER TABLE public.colectas 
ADD COLUMN IF NOT EXISTS target_amount DECIMAL(10, 2);

-- Set a default value for existing records (you may want to update these manually)
-- For now, we'll set target_amount to be 10 times the base_amount as a reasonable default
UPDATE public.colectas 
SET target_amount = base_amount * 10 
WHERE target_amount IS NULL;

-- Make the column NOT NULL after setting default values
ALTER TABLE public.colectas 
ALTER COLUMN target_amount SET NOT NULL;

-- Add a comment to document the column
COMMENT ON COLUMN public.colectas.target_amount IS 'Total amount to be collected for this colecta (required field)';

-- Verify the changes
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'colectas' 
AND column_name IN ('base_amount', 'target_amount')
ORDER BY ordinal_position;
