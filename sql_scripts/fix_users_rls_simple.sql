-- Simplified RLS policy for users table
-- Execute this in the Supabase SQL Editor

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view shared parents" ON public.users;

-- Create a simpler policy that allows authenticated users to see other users
-- This is safe because we're only showing basic profile info (name, email)
-- and only to authenticated users who are already part of the app

CREATE POLICY "Authenticated users can view all users"
ON public.users FOR SELECT
TO authenticated
USING (true);

-- Keep the update policy restrictive
CREATE POLICY "Users can update their own profile"
ON public.users FOR UPDATE
USING (auth.uid() = id);

-- Test the policy
SELECT 
    id,
    first_name,
    last_name,
    email,
    full_name
FROM public.users
ORDER BY first_name, last_name;
