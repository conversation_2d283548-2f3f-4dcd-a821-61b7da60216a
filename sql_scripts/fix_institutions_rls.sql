-- Fix RLS policies for institutions table
-- Execute this in the Supabase SQL Editor

-- Check current RLS status
SELECT schemaname, tablename, rowsecurity, hasrls 
FROM pg_tables 
WHERE tablename = 'institutions' AND schemaname = 'public';

-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'institutions' AND schemaname = 'public';

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow authenticated users to read institutions" ON public.institutions;
DROP POLICY IF EXISTS "Allow authenticated users to insert institutions" ON public.institutions;
DROP POLICY IF EXISTS "Allow all users to read institutions" ON public.institutions;
DROP POLICY IF EXISTS "Allow all users to insert institutions" ON public.institutions;

-- Create new policies that allow access
-- Allow all authenticated users to read institutions
CREATE POLICY "Allow authenticated users to read institutions" 
ON public.institutions FOR SELECT 
TO authenticated 
USING (true);

-- Allow all authenticated users to insert institutions (for creating new schools)
CREATE POLICY "Allow authenticated users to insert institutions" 
ON public.institutions FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Also allow anonymous users to read institutions (in case the app uses anon key)
CREATE POLICY "Allow anonymous users to read institutions" 
ON public.institutions FOR SELECT 
TO anon 
USING (true);

-- Grant necessary permissions
GRANT SELECT, INSERT ON public.institutions TO authenticated;
GRANT SELECT ON public.institutions TO anon;

-- Test the policies by selecting from institutions
SELECT id, name, address, status, created_at 
FROM public.institutions 
ORDER BY created_at DESC;
