-- Fix materialized view to use institutions instead of colegio
-- Execute this in the Supabase SQL Editor

-- Drop the existing materialized view if it exists
DROP MATERIALIZED VIEW IF EXISTS public.user_group_memberships CASCADE;

-- Recreate the materialized view with the correct structure using institutions
CREATE MATERIALIZED VIEW public.user_group_memberships AS
SELECT
    gm.id,
    gm.group_id,
    gm.user_id,
    gm.kid_id,
    gm.is_referente,
    gm.created_at,
    i.name as colegio,  -- Keep the alias 'colegio' for backward compatibility
    g.sala,
    g.año,
    g.nombre as grupo_nombre
FROM
    public.group_memberships gm
    JOIN public.groups g ON g.id = gm.group_id
    JOIN public.institutions i ON i.id = g.institution_id;

-- Grant permissions to the authenticated role
GRANT SELECT ON public.user_group_memberships TO authenticated;

-- <PERSON><PERSON> indexes to improve query performance
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_user_id ON public.user_group_memberships(user_id);
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_group_id ON public.user_group_memberships(group_id);

-- Create a function to refresh the materialized view
CREATE OR REPLACE FUNCTION public.refresh_user_group_memberships()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    REFRESH MATERIALIZED VIEW public.user_group_memberships;
    RETURN NULL;
END;
$function$;

-- Create triggers to automatically refresh the materialized view when data changes
DROP TRIGGER IF EXISTS refresh_user_group_memberships_on_group_memberships ON public.group_memberships;
CREATE TRIGGER refresh_user_group_memberships_on_group_memberships
    AFTER INSERT OR UPDATE OR DELETE ON public.group_memberships
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.refresh_user_group_memberships();

DROP TRIGGER IF EXISTS refresh_user_group_memberships_on_groups ON public.groups;
CREATE TRIGGER refresh_user_group_memberships_on_groups
    AFTER INSERT OR UPDATE OR DELETE ON public.groups
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.refresh_user_group_memberships();

DROP TRIGGER IF EXISTS refresh_user_group_memberships_on_institutions ON public.institutions;
CREATE TRIGGER refresh_user_group_memberships_on_institutions
    AFTER INSERT OR UPDATE OR DELETE ON public.institutions
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.refresh_user_group_memberships();

-- Confirmation message
SELECT 'Materialized view user_group_memberships has been fixed to use institutions.' AS result;
