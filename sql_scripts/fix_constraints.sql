-- Fix foreign key constraints that might prevent user deletion
-- Execute this in the Supabase SQL Editor

-- First, identify all foreign key constraints pointing to auth.users
SELECT
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND ccu.table_schema = 'auth' AND ccu.table_name = 'users';

-- Now, delete any records in public.users that reference the user we want to delete
DELETE FROM public.users 
WHERE id = '1d6a3e3c-5bc-4e85-b1c9-5227f7cca5f7';

-- Also try by email
DELETE FROM public.users 
WHERE email = '<EMAIL>';

-- Now try to delete from auth.users again
DELETE FROM auth.users 
WHERE id = '1d6a3e3c-5bc-4e85-b1c9-5227f7cca5f7';

-- Also try by email
DELETE FROM auth.users 
WHERE email = '<EMAIL>';
