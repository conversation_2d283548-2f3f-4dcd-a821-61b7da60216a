-- Create announcements table
CREATE TABLE IF NOT EXISTS public.announcements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL,
    date TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies
ALTER TABLE public.announcements ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select announcements for groups they are members of
DROP POLICY IF EXISTS "Users can view announcements for their groups" ON public.announcements;
CREATE POLICY "Users can view announcements for their groups" ON public.announcements
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = announcements.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert announcements for groups they are members of
DROP POLICY IF EXISTS "Users can create announcements for their groups" ON public.announcements;
CREATE POLICY "Users can create announcements for their groups" ON public.announcements
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = announcements.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own announcements
DROP POLICY IF EXISTS "Users can update their own announcements" ON public.announcements;
CREATE POLICY "Users can update their own announcements" ON public.announcements
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own announcements
DROP POLICY IF EXISTS "Users can delete their own announcements" ON public.announcements;
CREATE POLICY "Users can delete their own announcements" ON public.announcements
    FOR DELETE
    USING (created_by = auth.uid());
