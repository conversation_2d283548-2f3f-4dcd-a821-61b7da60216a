-- Fix permissions and check database structure
-- Execute this in the Supabase SQL Editor

-- First, let's check what tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public';

-- Check if the auth.users table has any records
SELECT count(*) as auth_users_count 
FROM auth.users;

-- Check if the public.users table has any records
SELECT count(*) as public_users_count 
FROM public.users;

-- Check for any orphaned users (in auth but not in public)
SELECT au.id, au.email, au.created_at
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL;

-- Fix permissions for the public.users table
GRANT ALL PRIVILEGES ON TABLE public.users TO authenticated;
GRANT ALL PRIVILEGES ON TABLE public.users TO service_role;
GRANT SELECT, INSERT, UPDATE ON TABLE public.users TO anon;

-- Create a more robust function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_exists BOOLEAN;
BEGIN
    -- Check if the user already exists in the public.users table
    SELECT EXISTS (
        SELECT 1 FROM public.users WHERE id = NEW.id
    ) INTO user_exists;

    -- If the user doesn't exist, insert them
    IF NOT user_exists THEN
        BEGIN
            INSERT INTO public.users (
                id, 
                email, 
                first_name, 
                last_name, 
                nickname, 
                phone
            )
            VALUES (
                NEW.id, 
                NEW.email, 
                COALESCE(NEW.raw_user_meta_data->>'first_name', 'Usuario'),
                COALESCE(NEW.raw_user_meta_data->>'last_name', 'Nuevo'),
                NEW.raw_user_meta_data->>'nickname',
                NEW.raw_user_meta_data->>'phone'
            );
            RAISE NOTICE 'Created new user record for %', NEW.email;
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE 'Error creating user record: %', SQLERRM;
        END;
    ELSE
        RAISE NOTICE 'User % already exists in public.users table', NEW.email;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Create a function to manually sync users from auth to public
CREATE OR REPLACE FUNCTION public.sync_missing_users()
RETURNS TEXT AS $$
DECLARE
    user_record RECORD;
    users_added INTEGER := 0;
    users_skipped INTEGER := 0;
BEGIN
    FOR user_record IN 
        SELECT au.id, au.email, au.raw_user_meta_data
        FROM auth.users au
        LEFT JOIN public.users pu ON au.id = pu.id
        WHERE pu.id IS NULL
    LOOP
        BEGIN
            INSERT INTO public.users (
                id, 
                email, 
                first_name, 
                last_name, 
                nickname, 
                phone
            )
            VALUES (
                user_record.id, 
                user_record.email, 
                COALESCE(user_record.raw_user_meta_data->>'first_name', 'Usuario'),
                COALESCE(user_record.raw_user_meta_data->>'last_name', 'Nuevo'),
                user_record.raw_user_meta_data->>'nickname',
                user_record.raw_user_meta_data->>'phone'
            );
            users_added := users_added + 1;
        EXCEPTION WHEN OTHERS THEN
            users_skipped := users_skipped + 1;
            RAISE NOTICE 'Error syncing user %: %', user_record.email, SQLERRM;
        END;
    END LOOP;
    
    RETURN format('Sync complete: %s users added, %s users skipped', users_added, users_skipped);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Run the sync function to fix any existing users
SELECT public.sync_missing_users();

-- Create the materialized view if it doesn't exist
CREATE MATERIALIZED VIEW IF NOT EXISTS public.user_group_memberships AS
SELECT 
    gm.id,
    gm.group_id,
    gm.user_id,
    gm.kid_id,
    gm.is_referente,
    gm.created_at,
    g.colegio,
    g.sala,
    g.año,
    g.nombre as grupo_nombre
FROM 
    public.group_memberships gm
    JOIN public.groups g ON g.id = gm.group_id;

-- Grant permissions to the authenticated role
GRANT SELECT ON public.user_group_memberships TO authenticated;

-- Create an index to improve query performance
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_user_id ON public.user_group_memberships(user_id);
CREATE INDEX IF NOT EXISTS idx_user_group_memberships_group_id ON public.user_group_memberships(group_id);

-- Create a function to refresh the materialized view
CREATE OR REPLACE FUNCTION public.refresh_user_group_memberships()
RETURNS trigger AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.user_group_memberships;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to refresh the view when data changes
DROP TRIGGER IF EXISTS refresh_user_group_memberships_trigger ON public.group_memberships;
CREATE TRIGGER refresh_user_group_memberships_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.group_memberships
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.refresh_user_group_memberships();

DROP TRIGGER IF EXISTS refresh_user_group_memberships_groups_trigger ON public.groups;
CREATE TRIGGER refresh_user_group_memberships_groups_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.groups
    FOR EACH STATEMENT
    EXECUTE FUNCTION public.refresh_user_group_memberships();
