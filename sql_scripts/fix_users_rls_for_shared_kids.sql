-- Fix RLS policies for users table to allow viewing other parents of shared kids
-- Execute this in the Supabase SQL Editor

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view shared parents" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Service role can manage users" ON public.users;
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.users;

-- Create new policies

-- 1. Users can view their own profile
CREATE POLICY "Users can view their own profile"
ON public.users FOR SELECT
USING (auth.uid() = id);

-- 2. Users can view other parents who share kids with them
CREATE POLICY "Users can view shared parents"
ON public.users FOR SELECT
USING (
    -- Allow viewing other users who are parents of kids that the current user is also a parent of
    EXISTS (
        SELECT 1 
        FROM public.kid_parents kp1
        JOIN public.kid_parents kp2 ON kp1.kid_id = kp2.kid_id
        WHERE kp1.parent_id = auth.uid()  -- Current user is a parent of the kid
        AND kp2.parent_id = users.id      -- The user being viewed is also a parent of the same kid
    )
);

-- 3. Users can update their own profile
CREATE POLICY "Users can update their own profile"
ON public.users FOR UPDATE
USING (auth.uid() = id);

-- 4. Service role can manage users
CREATE POLICY "Service role can manage users"
ON public.users
USING (auth.role() = 'service_role');

-- 5. Allow insert for authenticated users
CREATE POLICY "Allow insert for authenticated users"
ON public.users FOR INSERT
WITH CHECK (auth.role() = 'authenticated' OR auth.uid() = id);

-- Test the policy by checking what users the current user can see
-- (This will show the current user + any other parents who share kids)
SELECT 
    u.id,
    u.first_name,
    u.last_name,
    u.email,
    'Can see this user' as access_status
FROM public.users u
ORDER BY u.first_name, u.last_name;
