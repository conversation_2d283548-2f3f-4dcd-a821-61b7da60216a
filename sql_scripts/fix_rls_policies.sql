-- Fix RLS policies for kids and groups tables

-- First, let's fix the kids table policies
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid errors
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can insert their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can update their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can delete their own kids" ON public.kids;

-- Create policies for kids table
-- Policy to allow users to view only their own kids
CREATE POLICY "Users can view their own kids"
ON public.kids FOR SELECT
USING (parent_id = auth.uid());

-- Policy to allow users to insert only their own kids
CREATE POLICY "Users can insert their own kids"
ON public.kids FOR INSERT
WITH CHECK (parent_id = auth.uid());

-- Policy to allow users to update only their own kids
CREATE POLICY "Users can update their own kids"
ON public.kids FOR UPDATE
USING (parent_id = auth.uid());

-- Policy to allow users to delete only their own kids
CREATE POLICY "Users can delete their own kids"
ON public.kids FOR DELETE
USING (parent_id = auth.uid());

-- Now, let's fix the groups table policies
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid errors
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.groups;

-- Create policies for groups table
-- Policy to allow users to view groups they created or are members of
CREATE POLICY "Users can view their groups"
ON public.groups FOR SELECT
USING (
    created_by = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.group_memberships
        WHERE group_memberships.group_id = groups.id
        AND group_memberships.user_id = auth.uid()
    )
);

-- Policy to allow users to create groups
CREATE POLICY "Users can create groups"
ON public.groups FOR INSERT
WITH CHECK (created_by = auth.uid());

-- Policy to allow group creators to update their groups
CREATE POLICY "Group creators can update their groups"
ON public.groups FOR UPDATE
USING (created_by = auth.uid());

-- Policy to allow group creators to delete their groups
CREATE POLICY "Group creators can delete their groups"
ON public.groups FOR DELETE
USING (created_by = auth.uid());

-- Fix group_memberships table policies
ALTER TABLE public.group_memberships ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid errors
DROP POLICY IF EXISTS "Users can view group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can update their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can delete their own group memberships" ON public.group_memberships;

-- Create policies for group_memberships table
-- Policy to allow users to view memberships for groups they are members of
CREATE POLICY "Users can view group memberships"
ON public.group_memberships FOR SELECT
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.group_memberships AS gm
        WHERE gm.group_id = group_memberships.group_id
        AND gm.user_id = auth.uid()
    )
);

-- Policy to allow users to create memberships for their own kids
CREATE POLICY "Users can create group memberships"
ON public.group_memberships FOR INSERT
WITH CHECK (
    user_id = auth.uid() OR
    (
        kid_id IS NOT NULL AND
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = group_memberships.kid_id
            AND kids.parent_id = auth.uid()
        )
    )
);

-- Policy to allow users to update their own memberships
CREATE POLICY "Users can update their own group memberships"
ON public.group_memberships FOR UPDATE
USING (user_id = auth.uid());

-- Policy to allow users to delete their own memberships
CREATE POLICY "Users can delete their own group memberships"
ON public.group_memberships FOR DELETE
USING (user_id = auth.uid());
