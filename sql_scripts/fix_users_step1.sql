-- Step 1: Check if users table exists and create it if needed
DO $$
DECLARE
    users_table_exists BOOLEAN;
BEGIN
    -- Check if users table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'users'
    ) INTO users_table_exists;

    -- If users table doesn't exist, create it with the correct structure
    IF NOT users_table_exists THEN
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) PRIMARY KEY,
            email TEXT NOT NULL,
            first_name TEXT NOT NULL DEFAULT 'Usuario',
            last_name TEXT NOT NULL DEFAULT 'Nuevo',
            nickname TEXT,
            full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
            phone TEXT,
            profile_picture TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );
        
        RAISE NOTICE 'Created users table with correct structure';
    END IF;
END $$;
