-- Reset and fix the auth system
-- Execute this in the Supabase SQL Editor

-- First, check for any foreign key constraints that might prevent deletion
SELECT
    tc.table_schema, 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_schema AS foreign_table_schema,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND (ccu.table_schema = 'auth' OR tc.table_schema = 'auth');

-- Temporarily disable the RLS policies
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;

-- Fix permissions
GRANT USAGE ON SCHEMA auth TO anon, authenticated, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA auth TO anon, authenticated, service_role;
GRANT USAGE ON SCHEMA public TO anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated, service_role;
GRANT SELECT, INSERT ON ALL TABLES IN SCHEMA public TO anon;

-- Create or replace the function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert the new user into the public.users table with ON CONFLICT
    INSERT INTO public.users (
        id, 
        email, 
        first_name, 
        last_name, 
        nickname, 
        phone
    )
    VALUES (
        NEW.id, 
        NEW.email, 
        COALESCE(NEW.raw_user_meta_data->>'first_name', 'Usuario'),
        COALESCE(NEW.raw_user_meta_data->>'last_name', 'Nuevo'),
        NEW.raw_user_meta_data->>'nickname',
        NEW.raw_user_meta_data->>'phone'
    )
    -- Add ON CONFLICT to handle the case where the user already exists
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        nickname = EXCLUDED.nickname,
        phone = EXCLUDED.phone,
        updated_at = now();
        
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

CREATE TRIGGER on_auth_user_created
AFTER INSERT ON auth.users
FOR EACH ROW
EXECUTE FUNCTION public.handle_new_user();

-- Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows users to see their own data
DROP POLICY IF EXISTS "Users can view their own data" ON public.users;
CREATE POLICY "Users can view their own data" ON public.users
    FOR SELECT
    USING (auth.uid() = id);

-- Create a policy that allows users to update their own data
DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
CREATE POLICY "Users can update their own data" ON public.users
    FOR UPDATE
    USING (auth.uid() = id);

-- Create a policy that allows the service role to do anything
DROP POLICY IF EXISTS "Service role can do anything" ON public.users;
CREATE POLICY "Service role can do anything" ON public.users
    FOR ALL
    USING (auth.role() = 'service_role');

-- Create a policy that allows authenticated users to insert data
DROP POLICY IF EXISTS "Authenticated users can insert data" ON public.users;
CREATE POLICY "Authenticated users can insert data" ON public.users
    FOR INSERT
    WITH CHECK (auth.role() = 'authenticated');

-- Create a policy that allows anonymous users to insert data during signup
DROP POLICY IF EXISTS "Anonymous users can insert data during signup" ON public.users;
CREATE POLICY "Anonymous users can insert data during signup" ON public.users
    FOR INSERT
    WITH CHECK (auth.role() = 'anon');

-- Check for any orphaned users (in auth but not in public)
SELECT au.id, au.email, au.created_at
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL;
