-- Fix institution linkage for groups
-- Execute this in the Supabase SQL Editor

-- First, let's see what we have
SELECT 'Groups with institution_id' as info, count(*) as count
FROM public.groups 
WHERE institution_id IS NOT NULL;

SELECT 'Groups without institution_id' as info, count(*) as count
FROM public.groups 
WHERE institution_id IS NULL;

-- Check if the institution_ids in groups table actually exist in institutions table
SELECT 
    g.id as group_id,
    g.institution_id,
    g.sala,
    g.año,
    g.nombre,
    i.name as institution_name,
    CASE 
        WHEN i.id IS NULL THEN 'MISSING INSTITUTION'
        ELSE 'LINKED'
    END as status
FROM public.groups g
LEFT JOIN public.institutions i ON i.id = g.institution_id
ORDER BY g.created_at DESC;

-- If there are groups with invalid institution_ids, let's fix them
-- This will set the first available institution for groups with invalid institution_ids
UPDATE public.groups 
SET institution_id = (
    SELECT id 
    FROM public.institutions 
    WHERE status = 'active' 
    LIMIT 1
)
WHERE institution_id IS NOT NULL 
AND institution_id NOT IN (
    SELECT id FROM public.institutions
);

-- For groups that still don't have institution_id, create a default institution
INSERT INTO public.institutions (name, status)
VALUES ('Institución por defecto', 'active')
ON CONFLICT (name) DO NOTHING;

-- Update groups without institution_id to use the default institution
UPDATE public.groups 
SET institution_id = (
    SELECT id 
    FROM public.institutions 
    WHERE name = 'Institución por defecto'
)
WHERE institution_id IS NULL;

-- Final check
SELECT 
    g.id as group_id,
    g.institution_id,
    g.sala,
    g.año,
    g.nombre,
    i.name as institution_name
FROM public.groups g
LEFT JOIN public.institutions i ON i.id = g.institution_id
ORDER BY g.created_at DESC;
