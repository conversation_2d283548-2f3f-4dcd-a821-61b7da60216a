-- Create notification_read_status table
CREATE TABLE IF NOT EXISTS public.notification_read_status (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    announcement_id UUID NOT NULL REFERENCES public.announcements(id) ON DELETE CASCADE,
    read_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(user_id, announcement_id)
);

-- Add RLS policies
ALTER TABLE public.notification_read_status ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select their own read status
DROP POLICY IF EXISTS "Users can view their own read status" ON public.notification_read_status;
CREATE POLICY "Users can view their own read status" ON public.notification_read_status
    FOR SELECT
    USING (user_id = auth.uid());

-- Policy to allow users to insert their own read status
DROP POLICY IF EXISTS "Users can create their own read status" ON public.notification_read_status;
CREATE POLICY "Users can create their own read status" ON public.notification_read_status
    FOR INSERT
    WITH CHECK (user_id = auth.uid());

-- Policy to allow users to update their own read status
DROP POLICY IF EXISTS "Users can update their own read status" ON public.notification_read_status;
CREATE POLICY "Users can update their own read status" ON public.notification_read_status
    FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Policy to allow users to delete their own read status
DROP POLICY IF EXISTS "Users can delete their own read status" ON public.notification_read_status;
CREATE POLICY "Users can delete their own read status" ON public.notification_read_status
    FOR DELETE
    USING (user_id = auth.uid());
