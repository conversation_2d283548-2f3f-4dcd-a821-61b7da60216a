-- Update policies to allow viewing all members and kids in groups the user belongs to

-- First, let's keep the basic policies we already have
-- (No need to run this part if you've already run the simplified script)
/*
ALTER TABLE IF EXISTS public.kids DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.group_memberships DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can insert their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can update their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can delete their own kids" ON public.kids;

DROP POLICY IF EXISTS "Users can view their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can update their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can delete their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Group creators can view all memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create memberships for their kids" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can view group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create group memberships" ON public.group_memberships;

DROP POLICY IF EXISTS "Users can view groups they created" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON public.groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;

-- Now re-enable RLS
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_memberships ENABLE ROW LEVEL SECURITY;

-- Create the most basic policies possible

-- Kids policies
CREATE POLICY "Kids basic policy"
ON public.kids
USING (parent_id = auth.uid());

-- Groups policies
CREATE POLICY "Groups basic policy"
ON public.groups
USING (created_by = auth.uid());

-- Group memberships policies
CREATE POLICY "Group memberships basic policy"
ON public.group_memberships
USING (user_id = auth.uid());
*/

-- Now add additional policies to allow viewing all members in groups the user belongs to

-- 1. Add policy for users to see groups they're members of (not just ones they created)
CREATE POLICY "Groups member policy"
ON public.groups FOR SELECT
USING (
    id IN (
        SELECT group_id 
        FROM public.group_memberships 
        WHERE user_id = auth.uid()
    )
);

-- 2. Add policy for users to see all memberships in groups they belong to
CREATE POLICY "Group memberships in my groups"
ON public.group_memberships FOR SELECT
USING (
    group_id IN (
        SELECT group_id 
        FROM public.group_memberships 
        WHERE user_id = auth.uid()
    )
);

-- 3. Add policy for users to see kids that belong to groups they're in
CREATE POLICY "Kids in my groups"
ON public.kids FOR SELECT
USING (
    id IN (
        SELECT kid_id 
        FROM public.group_memberships 
        WHERE group_id IN (
            SELECT group_id 
            FROM public.group_memberships 
            WHERE user_id = auth.uid()
        )
        AND kid_id IS NOT NULL
    )
);
