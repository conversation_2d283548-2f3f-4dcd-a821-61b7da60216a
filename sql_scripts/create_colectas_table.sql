-- Create colectas table
CREATE TABLE IF NOT EXISTS public.colectas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    motivo TEXT NOT NULL,
    descripcion TEXT NOT NULL,
    fecha_fin TIMESTAMP WITH TIME ZONE NOT NULL,
    base_amount DECIMAL(10, 2) NOT NULL,
    total_collected DECIMAL(10, 2) DEFAULT 0 NOT NULL,
    total_contributions INTEGER DEFAULT 0 NOT NULL,
    account_info TEXT NOT NULL,
    status TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create contributions table
CREATE TABLE IF NOT EXISTS public.contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    colecta_id UUID NOT NULL REFERENCES public.colectas(id) ON DELETE CASCADE,
    kid_id UUID NOT NULL REFERENCES public.kids(id) ON DELETE CASCADE,
    amount DECIMAL(10, 2) NOT NULL,
    payment_method TEXT NOT NULL,
    payment_reference TEXT,
    status TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add RLS policies for colectas
ALTER TABLE public.colectas ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select colectas for groups they are members of
DROP POLICY IF EXISTS "Users can view colectas for their groups" ON public.colectas;
CREATE POLICY "Users can view colectas for their groups" ON public.colectas
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = colectas.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert colectas for groups they are members of
DROP POLICY IF EXISTS "Users can create colectas for their groups" ON public.colectas;
CREATE POLICY "Users can create colectas for their groups" ON public.colectas
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = colectas.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own colectas
DROP POLICY IF EXISTS "Users can update their own colectas" ON public.colectas;
CREATE POLICY "Users can update their own colectas" ON public.colectas
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own colectas
DROP POLICY IF EXISTS "Users can delete their own colectas" ON public.colectas;
CREATE POLICY "Users can delete their own colectas" ON public.colectas
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for contributions
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select contributions for colectas in their groups
DROP POLICY IF EXISTS "Users can view contributions for their groups" ON public.contributions;
CREATE POLICY "Users can view contributions for their groups" ON public.contributions
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.colectas
            JOIN public.group_memberships ON colectas.group_id = group_memberships.group_id
            WHERE colectas.id = contributions.colecta_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert contributions for their kids
DROP POLICY IF EXISTS "Users can create contributions for their kids" ON public.contributions;
CREATE POLICY "Users can create contributions for their kids" ON public.contributions
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to update their own contributions
DROP POLICY IF EXISTS "Users can update their own contributions" ON public.contributions;
CREATE POLICY "Users can update their own contributions" ON public.contributions
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );

-- Policy to allow users to delete their own contributions
DROP POLICY IF EXISTS "Users can delete their own contributions" ON public.contributions;
CREATE POLICY "Users can delete their own contributions" ON public.contributions
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.kids
            WHERE kids.id = contributions.kid_id
            AND kids.parent_id = auth.uid()
        )
    );
