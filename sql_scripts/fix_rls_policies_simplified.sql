-- Completely simplified RLS policies to avoid any circular references

-- First, disable all RLS to start fresh
ALTER TABLE IF EXISTS public.kids DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.groups DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS public.group_memberships DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies
DROP POLICY IF EXISTS "Users can view their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can insert their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can update their own kids" ON public.kids;
DROP POLICY IF EXISTS "Users can delete their own kids" ON public.kids;

DROP POLICY IF EXISTS "Users can view their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can update their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can delete their own group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Group creators can view all memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create memberships for their kids" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can view group memberships" ON public.group_memberships;
DROP POLICY IF EXISTS "Users can create group memberships" ON public.group_memberships;

DROP POLICY IF EXISTS "Users can view groups they created" ON public.groups;
DROP POLICY IF EXISTS "Users can view groups they are members of" ON public.groups;
DROP POLICY IF EXISTS "Users can create groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can update their groups" ON public.groups;
DROP POLICY IF EXISTS "Group creators can delete their groups" ON public.groups;
DROP POLICY IF EXISTS "Users can view their groups" ON public.groups;

-- Now re-enable RLS
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.group_memberships ENABLE ROW LEVEL SECURITY;

-- Create the most basic policies possible

-- Kids policies
CREATE POLICY "Kids basic policy"
ON public.kids
USING (parent_id = auth.uid());

-- Groups policies
CREATE POLICY "Groups basic policy"
ON public.groups
USING (created_by = auth.uid());

-- Group memberships policies
CREATE POLICY "Group memberships basic policy"
ON public.group_memberships
USING (user_id = auth.uid());
