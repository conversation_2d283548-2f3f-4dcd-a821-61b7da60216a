-- WARNING: This script will delete all users and reset the auth system
-- Only use this if you're sure you want to start fresh
-- Execute this in the Supabase SQL Editor

-- First, disable foreign key constraints
SET session_replication_role = 'replica';

-- Delete all records from public.users
TRUNCATE public.users CASCADE;

-- Delete all records from auth.users
TRUNCATE auth.users CASCADE;

-- Re-enable foreign key constraints
SET session_replication_role = 'origin';

-- Verify tables are empty
SELECT COUNT(*) FROM auth.users;
SELECT COUNT(*) FROM public.users;
