-- Delete a problematic user
-- Execute this in the Supabase SQL Editor

-- First, check if the user exists in auth.users
DO $$
DECLARE
    user_id UUID;
    user_exists BOOLEAN;
BEGIN
    -- Check if the user exists
    SELECT id, EXISTS (
        SELECT 1 FROM auth.users WHERE email = '<EMAIL>'
    ) INTO user_id, user_exists FROM auth.users WHERE email = '<EMAIL>';
    
    IF user_exists THEN
        RAISE NOTICE 'User exists with ID: %', user_id;
        
        -- Delete from public.users if exists
        DELETE FROM public.users WHERE id = user_id;
        RAISE NOTICE 'Deleted from public.users';
        
        -- Delete from auth.users
        DELETE FROM auth.users WHERE id = user_id;
        RAISE NOTICE 'Deleted from auth.users';
    ELSE
        RAISE NOTICE 'User <NAME_EMAIL> does not exist';
    END IF;
END $$;
