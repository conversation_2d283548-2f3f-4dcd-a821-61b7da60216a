-- Create polls table
CREATE TABLE IF NOT EXISTS public.polls (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    group_id UUID NOT NULL REFERENCES public.groups(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL,
    created_by UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create poll options table
CREATE TABLE IF NOT EXISTS public.poll_options (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poll_id UUID NOT NULL REFERENCES public.polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER DEFAULT 0 NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create votes table
CREATE TABLE IF NOT EXISTS public.votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poll_id UUID NOT NULL REFERENCES public.polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES public.poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(poll_id, user_id)
);

-- Add RLS policies for polls
ALTER TABLE public.polls ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select polls for groups they are members of
DROP POLICY IF EXISTS "Users can view polls for their groups" ON public.polls;
CREATE POLICY "Users can view polls for their groups" ON public.polls
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = polls.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert polls for groups they are members of
DROP POLICY IF EXISTS "Users can create polls for their groups" ON public.polls;
CREATE POLICY "Users can create polls for their groups" ON public.polls
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.group_memberships
            WHERE group_memberships.group_id = polls.group_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own polls
DROP POLICY IF EXISTS "Users can update their own polls" ON public.polls;
CREATE POLICY "Users can update their own polls" ON public.polls
    FOR UPDATE
    USING (created_by = auth.uid())
    WITH CHECK (created_by = auth.uid());

-- Policy to allow users to delete their own polls
DROP POLICY IF EXISTS "Users can delete their own polls" ON public.polls;
CREATE POLICY "Users can delete their own polls" ON public.polls
    FOR DELETE
    USING (created_by = auth.uid());

-- Add RLS policies for poll options
ALTER TABLE public.poll_options ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select poll options for polls in their groups
DROP POLICY IF EXISTS "Users can view poll options for their groups" ON public.poll_options;
CREATE POLICY "Users can view poll options for their groups" ON public.poll_options
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = poll_options.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert poll options for their own polls
DROP POLICY IF EXISTS "Users can create poll options for their own polls" ON public.poll_options;
CREATE POLICY "Users can create poll options for their own polls" ON public.poll_options
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Policy to allow users to update poll options for their own polls
DROP POLICY IF EXISTS "Users can update poll options for their own polls" ON public.poll_options;
CREATE POLICY "Users can update poll options for their own polls" ON public.poll_options
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    )
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Policy to allow users to delete poll options for their own polls
DROP POLICY IF EXISTS "Users can delete poll options for their own polls" ON public.poll_options;
CREATE POLICY "Users can delete poll options for their own polls" ON public.poll_options
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            WHERE polls.id = poll_options.poll_id
            AND polls.created_by = auth.uid()
        )
    );

-- Add RLS policies for votes
ALTER TABLE public.votes ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to select votes for polls in their groups
DROP POLICY IF EXISTS "Users can view votes for their groups" ON public.votes;
CREATE POLICY "Users can view votes for their groups" ON public.votes
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = votes.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to insert their own votes
DROP POLICY IF EXISTS "Users can create their own votes" ON public.votes;
CREATE POLICY "Users can create their own votes" ON public.votes
    FOR INSERT
    WITH CHECK (
        user_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.polls
            JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
            WHERE polls.id = votes.poll_id
            AND group_memberships.user_id = auth.uid()
        )
    );

-- Policy to allow users to update their own votes
DROP POLICY IF EXISTS "Users can update their own votes" ON public.votes;
CREATE POLICY "Users can update their own votes" ON public.votes
    FOR UPDATE
    USING (user_id = auth.uid())
    WITH CHECK (user_id = auth.uid());

-- Policy to allow users to delete their own votes
DROP POLICY IF EXISTS "Users can delete their own votes" ON public.votes;
CREATE POLICY "Users can delete their own votes" ON public.votes
    FOR DELETE
    USING (user_id = auth.uid());
