-- SQL script to fix user registration issues
-- Execute this in the Supabase SQL Editor

-- First, check if the users table exists and has the correct structure
DO $$
DECLARE
    users_table_exists BOOLEAN;
    users_trigger_exists BOOLEAN;
BEGIN
    -- Check if users table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' AND table_name = 'users'
    ) INTO users_table_exists;

    -- Check if the user creation trigger exists
    SELECT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'on_auth_user_created'
    ) INTO users_trigger_exists;

    -- If users table doesn't exist, create it with the correct structure
    IF NOT users_table_exists THEN
        CREATE TABLE public.users (
            id UUID REFERENCES auth.users(id) PRIMARY KEY,
            email TEXT NOT NULL,
            first_name TEXT NOT NULL DEFAULT 'Usuario',
            last_name TEXT NOT NULL DEFAULT 'Nuevo',
            nickname TEXT,
            full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED,
            phone TEXT,
            profile_picture TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
        );
        
        RAISE NOTICE 'Created users table with correct structure';
    ELSE
        -- Table exists, check if it has the correct columns
        -- If not, alter the table to match the expected structure
        
        -- Check if first_name column exists
        IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'first_name'
        ) THEN
            -- If full_name exists but not first_name/last_name, we need to modify the table
            IF EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name'
                AND is_generated = 'NEVER'  -- Check if it's a regular column, not a generated one
            ) THEN
                -- Backup the full_name values
                ALTER TABLE public.users ADD COLUMN temp_full_name TEXT;
                UPDATE public.users SET temp_full_name = full_name;
                
                -- Drop the full_name column
                ALTER TABLE public.users DROP COLUMN full_name;
                
                -- Add the new columns
                ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
                ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
                
                -- Try to split the full_name into first_name and last_name
                UPDATE public.users 
                SET 
                    first_name = CASE 
                        WHEN temp_full_name IS NULL THEN 'Usuario'
                        WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from 1 for position(' ' in temp_full_name) - 1)
                        ELSE temp_full_name
                    END,
                    last_name = CASE
                        WHEN temp_full_name IS NULL THEN 'Nuevo'
                        WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from position(' ' in temp_full_name) + 1)
                        ELSE 'Nuevo'
                    END;
                
                -- Add the generated full_name column
                ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
                
                -- Drop the temporary column
                ALTER TABLE public.users DROP COLUMN temp_full_name;
                
                RAISE NOTICE 'Modified users table to use first_name, last_name, and generated full_name';
            ELSE
                -- Just add the missing columns
                ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
                ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
                ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
                
                RAISE NOTICE 'Added first_name, last_name, and generated full_name columns to users table';
            END IF;
        END IF;
        
        -- Check if nickname column exists
        IF NOT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'nickname'
        ) THEN
            ALTER TABLE public.users ADD COLUMN nickname TEXT;
            RAISE NOTICE 'Added nickname column to users table';
        END IF;
    END IF;

    -- Create or replace the function to handle new user creation
    CREATE OR REPLACE FUNCTION public.handle_new_user()
    RETURNS TRIGGER AS $$
    BEGIN
        INSERT INTO public.users (id, email, first_name, last_name, nickname)
        VALUES (
            NEW.id, 
            NEW.email, 
            COALESCE(NEW.raw_user_meta_data->>'first_name', 'Usuario'),
            COALESCE(NEW.raw_user_meta_data->>'last_name', 'Nuevo'),
            NEW.raw_user_meta_data->>'nickname'
        );
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Create the trigger if it doesn't exist
    IF NOT users_trigger_exists THEN
        CREATE TRIGGER on_auth_user_created
        AFTER INSERT ON auth.users
        FOR EACH ROW
        EXECUTE FUNCTION public.handle_new_user();
        
        RAISE NOTICE 'Created trigger for automatic user profile creation';
    ELSE
        -- Drop and recreate the trigger to ensure it uses the latest function
        DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
        CREATE TRIGGER on_auth_user_created
        AFTER INSERT ON auth.users
        FOR EACH ROW
        EXECUTE FUNCTION public.handle_new_user();
        
        RAISE NOTICE 'Updated trigger for automatic user profile creation';
    END IF;
END $$;

-- Create or update the function to update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for updated_at if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'update_users_updated_at'
    ) THEN
        CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON public.users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE 'Created trigger for updating updated_at column';
    END IF;
END $$;

-- Confirmation message
SELECT 'User registration system has been fixed.' AS result;
