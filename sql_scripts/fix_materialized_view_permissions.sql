-- Fix permissions for the user_group_memberships materialized view
-- This script addresses the "must be owner of materialized view user_group_memberships" error

-- 1. Grant SELECT permission to authenticated users on the materialized view
GRANT SELECT ON public.user_group_memberships TO authenticated;

-- 2. Update the refresh function to use SECURITY DEFINER
-- This allows the function to run with the privileges of its creator (postgres)
-- rather than the privileges of the calling user (authenticated)
CREATE OR REPLACE FUNCTION public.refresh_user_group_memberships()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
AS $function$
BEGIN
    REFRESH MATERIALIZED VIEW public.user_group_memberships;
    RETURN NULL;
END;
$function$;

-- 3. Manually refresh the materialized view to ensure it's up to date
REFRESH MATERIALIZED VIEW public.user_group_memberships;

-- 4. Output a success message
DO $$
BEGIN
    RAISE NOTICE 'Successfully fixed permissions for user_group_memberships materialized view';
END $$;
