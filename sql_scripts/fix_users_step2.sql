-- Step 2: Check if first_name column exists and add it if needed
DO $$
DECLARE
    first_name_exists BOOLEAN;
    full_name_is_regular BOOLEAN;
BEGIN
    -- Check if first_name column exists
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'first_name'
    ) INTO first_name_exists;
    
    -- Check if full_name is a regular column (not generated)
    SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name'
        AND is_generated = 'NEVER'
    ) INTO full_name_is_regular;
    
    -- If first_name doesn't exist
    IF NOT first_name_exists THEN
        -- If full_name exists as a regular column, we need to modify the table
        IF full_name_is_regular THEN
            -- Backup the full_name values
            ALTER TABLE public.users ADD COLUMN temp_full_name TEXT;
            UPDATE public.users SET temp_full_name = full_name;
            
            -- Drop the full_name column
            ALTER TABLE public.users DROP COLUMN full_name;
            
            -- Add the new columns
            ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
            ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
            
            -- Try to split the full_name into first_name and last_name
            UPDATE public.users 
            SET 
                first_name = CASE 
                    WHEN temp_full_name IS NULL THEN 'Usuario'
                    WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from 1 for position(' ' in temp_full_name) - 1)
                    ELSE temp_full_name
                END,
                last_name = CASE
                    WHEN temp_full_name IS NULL THEN 'Nuevo'
                    WHEN position(' ' in temp_full_name) > 0 THEN substring(temp_full_name from position(' ' in temp_full_name) + 1)
                    ELSE 'Nuevo'
                END;
            
            -- Add the generated full_name column
            ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
            
            -- Drop the temporary column
            ALTER TABLE public.users DROP COLUMN temp_full_name;
            
            RAISE NOTICE 'Modified users table to use first_name, last_name, and generated full_name';
        ELSE
            -- Just add the missing columns
            ALTER TABLE public.users ADD COLUMN first_name TEXT NOT NULL DEFAULT 'Usuario';
            ALTER TABLE public.users ADD COLUMN last_name TEXT NOT NULL DEFAULT 'Nuevo';
            
            -- Check if full_name column exists
            IF NOT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' AND table_name = 'users' AND column_name = 'full_name'
            ) THEN
                ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
            END IF;
            
            RAISE NOTICE 'Added first_name, last_name, and generated full_name columns to users table';
        END IF;
    END IF;
END $$;
