-- Check the current state of groups and institutions tables
-- Execute this in the Supabase SQL Editor

-- Check if institutions table exists and has data
SELECT 'Institutions table' as table_name, count(*) as record_count
FROM public.institutions;

-- Check if groups table exists and has data
SELECT 'Groups table' as table_name, count(*) as record_count
FROM public.groups;

-- Check groups with their institution_id
SELECT 
    g.id,
    g.institution_id,
    g.sala,
    g.año,
    g.nombre,
    g.created_by,
    g.created_at
FROM public.groups g
ORDER BY g.created_at DESC
LIMIT 10;

-- Check institutions
SELECT 
    i.id,
    i.name,
    i.address,
    i.status,
    i.created_at
FROM public.institutions i
ORDER BY i.created_at DESC
LIMIT 10;

-- Check groups with institution names (this is what the app query does)
SELECT 
    g.id,
    g.institution_id,
    i.name as institution_name,
    g.sala,
    g.año,
    g.nombre,
    g.created_by,
    g.created_at
FROM public.groups g
INNER JOIN public.institutions i ON i.id = g.institution_id
ORDER BY g.created_at DESC
LIMIT 10;

-- Check for groups without institution_id (these would be missing from the app)
SELECT 
    g.id,
    g.institution_id,
    g.sala,
    g.año,
    g.nombre,
    g.created_by,
    g.created_at
FROM public.groups g
WHERE g.institution_id IS NULL;

-- Check group memberships
SELECT 
    gm.id,
    gm.group_id,
    gm.user_id,
    gm.kid_id,
    gm.is_referente,
    gm.created_at
FROM public.group_memberships gm
ORDER BY gm.created_at DESC
LIMIT 10;
