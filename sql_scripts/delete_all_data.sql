-- SQL script to delete all data from Parentium tables
-- This script preserves table structure but removes all rows
-- Execute with: supabase db execute --file delete_all_data.sql

-- Disable triggers temporarily to avoid issues with cascading updates
SET session_replication_role = 'replica';

-- Delete data from tables with foreign key dependencies first
-- Start with child tables and work up to parent tables

-- Delete from notifications table
TRUNCATE public.notifications CASCADE;

-- Delete from poll-related tables
TRUNCATE public.poll_votes CASCADE;
TRUNCATE public.poll_options CASCADE;
TRUNCATE public.polls CASCADE;

-- Delete from event-related tables
TRUNCATE public.events CASCADE;

-- Delete from colecta-related tables
TRUNCATE public.contributions CASCADE;
TRUNCATE public.colectas CASCADE;

-- Delete from announcement-related tables
TRUNCATE public.announcements CASCADE;

-- Delete from group-related tables
TRUNCATE public.group_memberships CASCADE;
TRUNCATE public.groups CASCADE;

-- Delete from kid-related tables
TRUNCATE public.kids CASCADE;

-- Note: We're not deleting from users table as it's linked to auth.users
-- If you want to delete users, you should do it through Supabase Auth API
-- or manually with more careful consideration

-- Re-enable triggers
SET session_replication_role = 'origin';

-- Confirmation message (will appear in query results)
SELECT 'All data has been deleted from application tables.' AS result;
