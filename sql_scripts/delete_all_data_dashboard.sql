-- SQL script to delete all data from Parentium tables
-- Execute this in the Supabase SQL Editor

-- Disable triggers temporarily to avoid issues with cascading updates
SET session_replication_role = 'replica';

-- Delete data from tables with foreign key dependencies first
-- Start with child tables and work up to parent tables

-- Delete from notifications table
TRUNCATE public.notifications CASCADE;

-- Delete from poll-related tables
TRUNCATE public.poll_votes CASCADE;
TRUNCATE public.poll_options CASCADE;
TRUNCATE public.polls CASCADE;

-- Delete from event-related tables
TRUNCATE public.events CASCADE;

-- Delete from colecta-related tables
TRUNCATE public.contributions CASCADE;
TRUNCATE public.colectas CASCADE;

-- Delete from announcement-related tables
TRUNCATE public.announcements CASCADE;

-- Delete from group-related tables
TRUNCATE public.group_memberships CASCADE;
TRUNCATE public.groups CASCADE;

-- Delete from kid-related tables
TRUNCATE public.kids CASCADE;

-- Special handling for users table
-- First, identify all foreign key constraints pointing to the users table
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT tc.table_schema, tc.constraint_name, tc.table_name, kcu.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
          ON tc.constraint_catalog = kcu.constraint_catalog 
          AND tc.constraint_schema = kcu.constraint_schema
          AND tc.constraint_name = kcu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND (tc.table_name = 'users' OR 
            (tc.constraint_schema = 'public' AND EXISTS (
                SELECT 1 FROM information_schema.constraint_column_usage ccu
                WHERE ccu.constraint_name = tc.constraint_name
                AND ccu.table_name = 'users'
            )))
    ) LOOP
        -- Drop the foreign key constraint
        EXECUTE 'ALTER TABLE ' || r.table_schema || '.' || r.table_name || ' DROP CONSTRAINT IF EXISTS ' || r.constraint_name || ' CASCADE';
        RAISE NOTICE 'Dropped foreign key constraint: %.%.%', r.table_schema, r.table_name, r.constraint_name;
    END LOOP;
END $$;

-- Now we can safely delete from the users table
TRUNCATE public.users CASCADE;

-- Re-enable triggers
SET session_replication_role = 'origin';

-- Confirmation message
SELECT 'All data has been deleted from application tables.' AS result;

-- Note: This script does NOT delete users from auth.users
-- To delete auth users, you need to use the Supabase dashboard or API
