-- Fix group visibility issues without disabling RLS

-- First, let's drop the problematic policies that are causing infinite recursion
DROP POLICY IF EXISTS "Group memberships in my groups" ON public.group_memberships;
DROP POLICY IF EXISTS "Kids in my groups" ON public.kids;

-- Create a function to check if a user is a member of a group
-- This avoids the circular references that cause infinite recursion
CREATE OR REPLACE FUNCTION public.user_is_member_of_group(group_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.group_memberships 
    WHERE group_memberships.group_id = user_is_member_of_group.group_id
    AND group_memberships.user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user created a group
CREATE OR REPLACE FUNCTION public.user_created_group(group_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 
    FROM public.groups 
    WHERE groups.id = user_created_group.group_id
    AND groups.created_by = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now create policies using these functions

-- 1. Policy for viewing group memberships
CREATE POLICY "View memberships in my groups"
ON public.group_memberships FOR SELECT
USING (
  public.user_is_member_of_group(group_id) OR
  public.user_created_group(group_id)
);

-- 2. Policy for viewing kids in my groups
CREATE POLICY "View kids in my groups"
ON public.kids FOR SELECT
USING (
  parent_id = auth.uid() OR
  id IN (
    SELECT kid_id 
    FROM public.group_memberships 
    WHERE kid_id IS NOT NULL
    AND (
      public.user_is_member_of_group(group_id) OR
      public.user_created_group(group_id)
    )
  )
);

-- 3. Policy for viewing colectas in my groups
CREATE POLICY "View colectas in my groups"
ON public.colectas FOR SELECT
USING (
  public.user_is_member_of_group(group_id) OR
  public.user_created_group(group_id)
);

-- 4. Policy for viewing contributions in colectas in my groups
CREATE POLICY "View contributions in my groups"
ON public.contributions FOR SELECT
USING (
  user_id = auth.uid() OR
  colecta_id IN (
    SELECT id 
    FROM public.colectas 
    WHERE public.user_is_member_of_group(group_id) OR
          public.user_created_group(group_id)
  )
);
