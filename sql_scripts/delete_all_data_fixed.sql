-- SQL script to delete all data from Parentium tables
-- Execute this in the Supabase SQL Editor

-- Disable triggers temporarily to avoid issues with cascading updates
SET session_replication_role = 'replica';

-- Delete data from tables with foreign key dependencies first
-- Start with child tables and work up to parent tables

-- Delete from notifications table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
        EXECUTE 'TRUNCATE public.notifications CASCADE';
    END IF;
END $$;

-- Delete from poll-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'poll_votes') THEN
        EXECUTE 'TRUNCATE public.poll_votes CASCADE';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'poll_options') THEN
        EXECUTE 'TRUNCATE public.poll_options CASCADE';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'polls') THEN
        EXECUTE 'TRUNCATE public.polls CASCADE';
    END IF;
END $$;

-- Delete from event-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'events') THEN
        EXECUTE 'TRUNCATE public.events CASCADE';
    END IF;
END $$;

-- Delete from colecta-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'contributions') THEN
        EXECUTE 'TRUNCATE public.contributions CASCADE';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'colectas') THEN
        EXECUTE 'TRUNCATE public.colectas CASCADE';
    END IF;
END $$;

-- Delete from announcement-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'announcements') THEN
        EXECUTE 'TRUNCATE public.announcements CASCADE';
    END IF;
END $$;

-- Delete from group-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'group_memberships') THEN
        EXECUTE 'TRUNCATE public.group_memberships CASCADE';
    END IF;
    
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'groups') THEN
        EXECUTE 'TRUNCATE public.groups CASCADE';
    END IF;
END $$;

-- Delete from kid-related tables if they exist
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'kids') THEN
        EXECUTE 'TRUNCATE public.kids CASCADE';
    END IF;
END $$;

-- Special handling for users table
-- First, identify all foreign key constraints pointing to the users table
DO $$
DECLARE
    r RECORD;
BEGIN
    FOR r IN (
        SELECT tc.table_schema, tc.constraint_name, tc.table_name, kcu.column_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu 
          ON tc.constraint_catalog = kcu.constraint_catalog 
          AND tc.constraint_schema = kcu.constraint_schema
          AND tc.constraint_name = kcu.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
        AND (tc.table_name = 'users' OR 
            (tc.constraint_schema = 'public' AND EXISTS (
                SELECT 1 FROM information_schema.constraint_column_usage ccu
                WHERE ccu.constraint_name = tc.constraint_name
                AND ccu.table_name = 'users'
            )))
    ) LOOP
        -- Drop the foreign key constraint
        EXECUTE 'ALTER TABLE ' || r.table_schema || '.' || r.table_name || ' DROP CONSTRAINT IF EXISTS ' || r.constraint_name || ' CASCADE';
        RAISE NOTICE 'Dropped foreign key constraint: %.%.%', r.table_schema, r.table_name, r.constraint_name;
    END LOOP;
END $$;

-- Now we can safely delete from the users table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'users') THEN
        EXECUTE 'TRUNCATE public.users CASCADE';
    END IF;
END $$;

-- Re-enable triggers
SET session_replication_role = 'origin';

-- Confirmation message
SELECT 'All data has been deleted from application tables.' AS result;

-- Note: This script does NOT delete users from auth.users
-- To delete auth users, you need to use the Supabase dashboard or API
