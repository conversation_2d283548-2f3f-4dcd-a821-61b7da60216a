-- This script removes the total_contributions counter from the colectas table
-- since we're now calculating the count on demand

-- First, let's create a comment on the column to indicate it's deprecated
COMMENT ON COLUMN public.colectas.total_contributions IS 'DEPRECATED: This counter is no longer used. Contributions are counted on demand.';

-- We're not actually removing the column to avoid breaking existing code,
-- but in a future update, you could use:
-- ALTER TABLE public.colectas DROP COLUMN total_contributions;

-- Return a confirmation message
SELECT 'The total_contributions column has been marked as deprecated.' AS result;
