-- Step 7: Create the trigger for updated_at if it doesn't exist
DO $$
DECLARE
    update_trigger_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT FROM pg_trigger
        WHERE tgname = 'update_users_updated_at'
    ) INTO update_trigger_exists;

    IF NOT update_trigger_exists THEN
        CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON public.users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
        
        RAISE NOTICE 'Created trigger for updating updated_at column';
    END IF;
END $$;
