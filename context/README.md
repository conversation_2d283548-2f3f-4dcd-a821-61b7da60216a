# Parentium

A mobile/web application for parent group management, built with Next.js, TypeScript, and Supabase.

## Features

- User Authentication & Registration
- Group Management
- Payment Processing
- Basic Group Features (Announcements & Events)

## Tech Stack

- **Frontend**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Deployment**: Vercel

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up environment variables:
   ```bash
   cp .env.example .env.local
   ```
4. Run the development server:
   ```bash
   npm run dev
   ```

## Project Structure

```
src/
├── app/              # Next.js app router
├── components/       # Reusable UI components
├── lib/             # Utility functions and configurations
├── styles/          # Global styles
└── types/           # TypeScript type definitions
```

## Development Guidelines

- Follow the style guide in `docs/style_guide.md`
- Use TypeScript for type safety
- Follow the component structure in `docs/screens.md`
- Implement database schema as defined in `docs/database.md`

## License

MIT 