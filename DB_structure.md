# Parentium Database Structure

## Overview

The Parentium application uses a PostgreSQL database managed through Supabase. The database structure consists of several interconnected tables that handle users, children (kids), groups, and various interactive features such as events, announcements, collections (colectas), and polls.

## Core Tables

### Users (`public.users`)
Extends Supabase's built-in auth.users table with additional profile information.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key, references auth.users(id) |
| email | TEXT | User's email address |
| first_name | TEXT | User's first name |
| last_name | TEXT | User's last name |
| nickname | TEXT | Optional nickname |
| full_name | TEXT | Generated field combining first and last name |
| phone | TEXT | User's phone number (optional) |
| profile_picture | TEXT | URL to profile picture (optional) |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Kids (`public.kids`)
Stores information about children linked to parent users.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| parent_id | UUID | Foreign key to users.id |
| first_name | TEXT | Child's first name |
| last_name | TEXT | Child's last name |
| nickname | TEXT | Optional nickname |
| full_name | TEXT | Generated field combining first and last name |
| birth_date | DATE | Child's birth date |
| dni | TEXT | National identification number |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Institutions (`public.institutions`)
Represents educational institutions/schools.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| name | TEXT | Institution name (unique) |
| address | TEXT | Institution address (optional) |
| status | TEXT | Institution status ('active' or 'inactive') |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Groups (`public.groups`)
Represents school groups or classrooms.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| institution_id | UUID | Foreign key to institutions.id |
| sala | TEXT | Classroom identifier |
| año | INTEGER | Year |
| nombre | TEXT | Optional group name |
| created_by | UUID | Foreign key to users.id, indicates who created the group |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Group Memberships (`public.group_memberships`)
Manages many-to-many relationships between users, kids, and groups.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups.id |
| user_id | UUID | Foreign key to users.id |
| kid_id | UUID | Foreign key to kids.id, optional |
| is_referente | BOOLEAN | Indicates if the user is a group referent/admin |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

Constraints: Unique(group_id, user_id, kid_id)

## Feature Tables

### Announcements (`public.announcements`)
Stores group announcements.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups.id |
| title | TEXT | Announcement title |
| message | TEXT | Announcement content |
| type | TEXT | Announcement type |
| date | TIMESTAMP | Announcement date (optional) |
| created_by | UUID | Foreign key to users.id |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Colectas (`public.colectas`)
Manages fundraising/collection campaigns.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups.id |
| motivo | TEXT | Purpose of the collection |
| descripcion | TEXT | Detailed description |
| fecha_fin | TIMESTAMP | End date for the collection |
| base_amount | DECIMAL | Suggested contribution amount |
| total_collected | DECIMAL | Running total of collected funds |
| total_contributions | INTEGER | Count of contributions made |
| account_info | TEXT | Payment account information |
| status | TEXT | Collection status |
| created_by | UUID | Foreign key to users.id |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Contributions (`public.contributions`)
Tracks individual contributions to colectas.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| colecta_id | UUID | Foreign key to colectas.id |
| user_id | UUID | Foreign key to users.id |
| kid_id | UUID | Foreign key to kids.id |
| amount | DECIMAL | Contribution amount |
| payment_method | TEXT | Method of payment |
| payment_reference | TEXT | Payment reference (optional) |
| proof_of_payment_url | TEXT | URL to payment proof (optional) |
| status | TEXT | Contribution status |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Polls (`public.polls`)
Stores group polls/surveys.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups.id |
| title | TEXT | Poll title |
| description | TEXT | Poll description (optional) |
| end_date | TIMESTAMP | End date (optional) |
| status | TEXT | Poll status |
| created_by | UUID | Foreign key to users.id |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Poll Options (`public.poll_options`)
Stores options for polls.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| poll_id | UUID | Foreign key to polls.id |
| text | TEXT | Option text |
| votes | INTEGER | Count of votes for this option |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Poll Votes (`public.poll_votes`)
Tracks individual votes on polls.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| poll_id | UUID | Foreign key to polls.id |
| option_id | UUID | Foreign key to poll_options.id |
| user_id | UUID | Foreign key to users.id |
| created_at | TIMESTAMP | Record creation timestamp |

Constraints: Unique(poll_id, user_id) ensures one vote per user per poll

### Events (`public.events`)
Stores calendar events.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| group_id | UUID | Foreign key to groups.id |
| title | TEXT | Event title |
| description | TEXT | Event description (optional) |
| type | TEXT | Event type |
| date | TIMESTAMP | Event start date/time |
| end_date | TIMESTAMP | Event end date/time (optional) |
| location | TEXT | Event location (optional) |
| created_by | UUID | Foreign key to users.id |
| created_at | TIMESTAMP | Record creation timestamp |
| updated_at | TIMESTAMP | Last update timestamp |

### Notifications (`public.notifications`)
Manages user notifications.

| Column | Type | Description |
|--------|------|-------------|
| id | UUID | Primary key |
| user_id | UUID | Foreign key to users.id |
| type | TEXT | Notification type |
| message | TEXT | Notification message |
| group_id | UUID | Foreign key to groups.id (optional) |
| event_id | UUID | Foreign key to events.id (optional) |
| announcement_id | UUID | Foreign key to announcements.id (optional) |
| colecta_id | UUID | Foreign key to colectas.id (optional) |
| poll_id | UUID | Foreign key to polls.id (optional) |
| is_read | BOOLEAN | Whether notification has been read |
| created_at | TIMESTAMP | Record creation timestamp |

## Database Features

### Triggers and Functions

1. **updated_at Maintenance**
   - A trigger on each table automatically updates the `updated_at` timestamp when records are modified

2. **New User Handler**
   - When a new auth user is created, it automatically creates a corresponding record in the `users` table
   - Implemented via the `handle_new_user()` function and `on_auth_user_created` trigger

3. **Colecta Totals Updater**
   - When a new contribution is added, it automatically updates the totals in the `colectas` table
   - Implemented via the `update_colecta_totals()` function and `on_contribution_created` trigger

4. **Poll Votes Counter**
   - When a new vote is cast, it automatically increments the vote count for the corresponding poll option
   - Implemented via the `update_poll_option_votes()` function and `on_poll_vote_created` trigger

### Row Level Security (RLS)

The database implements Row Level Security to restrict data access at the database level:

1. **Users Table**
   - Users can only view and update their own profiles

2. **Kids Table**
   - Users can only view, insert, update, and delete kids they've created

3. **Groups Table**
   - Anyone (authenticated) can view groups
   - Only the creator can update a group
   - Any authenticated user can create groups

4. **Group Memberships**
   - Anyone can view group memberships
   - Users can only join groups or leave groups for themselves

5. **Announcements**
   - Users can only view announcements for groups they're members of
   - Only group referents can create announcements

Similar policies exist for other tables to ensure data security and proper access control.

## Data Relationships

The database structure follows these key relationships:

1. A user can have multiple kids (one-to-many)
2. A user can be a member of multiple groups (many-to-many through group_memberships)
3. A kid can be part of multiple groups (many-to-many through group_memberships)
4. Groups can have multiple features (announcements, events, colectas, polls)
5. Notifications can reference various entities (polymorphic relationship)