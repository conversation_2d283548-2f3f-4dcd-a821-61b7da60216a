# Qids Database Backup Guide

This guide provides multiple methods to backup your Supabase database for the Qids project.

## 🚀 Quick Start

### Method 1: Supabase Dashboard (Easiest)
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your Qids project
3. Navigate to **Settings** → **Database**
4. Scroll down to **Database Backups**
5. Click **"Download backup"**
6. Save the `.sql` file to your `backups/` folder

### Method 2: Using Our Scripts

#### Option A: Direct Backup (Recommended)
```bash
./scripts/direct_backup.sh
```
This script will:
- Create a timestamped backup file
- Compress it to save space
- Generate backup information

#### Option B: Comprehensive Backup
```bash
./scripts/backup_database.sh
```
This creates multiple backup types:
- Full database dump
- Schema-only dump
- Data-only dump
- Individual table dumps

## 📋 Manual Backup Methods

### Using pg_dump (if you have PostgreSQL installed)
```bash
# Get your database URL from Supabase Dashboard > Settings > Database
pg_dump "postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres" > backup.sql
```

### Using Supabase CLI
```bash
# Make sure you're logged in to Supabase CLI
supabase login

# Create backup
supabase db dump --linked -f backups/backup_$(date +%Y%m%d_%H%M%S).sql
```

## 🔧 Setup Instructions

### Install PostgreSQL Client (for pg_dump)
```bash
# macOS
brew install postgresql

# Ubuntu/Debian
sudo apt-get install postgresql-client

# Windows
# Download from https://www.postgresql.org/download/windows/
```

### Install Supabase CLI
```bash
npm install -g supabase
# or
brew install supabase/tap/supabase
```

## 📁 Backup File Structure

```
backups/
├── qids_backup_20240526_123456.sql      # Full backup
├── qids_backup_20240526_123456.sql.gz   # Compressed backup
├── backup_info_20240526_123456.txt      # Backup information
└── db_backup_20240526_123456/            # Comprehensive backup folder
    ├── full_database_dump.sql
    ├── schema_only.sql
    ├── data_only.sql
    ├── table_users.sql
    ├── table_groups.sql
    └── restore.sh
```

## 🔄 Restore Instructions

### From SQL File
```bash
# Restore full backup
psql "your_database_url" < backups/qids_backup_20240526_123456.sql

# Restore from compressed backup
gunzip -c backups/qids_backup_20240526_123456.sql.gz | psql "your_database_url"
```

### Using Supabase CLI
```bash
# Reset and restore
supabase db reset --linked
psql "your_database_url" < backups/qids_backup_20240526_123456.sql
```

## 🛡️ Best Practices

### Backup Frequency
- **Daily**: For production data
- **Before major changes**: Always backup before migrations or major updates
- **Before deployments**: Backup before deploying new features

### Storage
- Keep at least 7 daily backups
- Keep at least 4 weekly backups
- Store backups in multiple locations (local + cloud)
- Test restore procedures regularly

### Security
- Never commit backup files to git (they're in .gitignore)
- Encrypt backups if storing in cloud
- Limit access to backup files
- Use secure transfer methods

## 🚨 Emergency Restore

If you need to quickly restore your database:

1. **Stop all applications** accessing the database
2. **Create a backup of current state** (if possible)
3. **Run restore command**:
   ```bash
   psql "your_database_url" < backups/latest_backup.sql
   ```
4. **Verify data integrity**
5. **Restart applications**

## 📞 Getting Database Connection Info

### From Supabase Dashboard:
1. Go to **Settings** → **Database**
2. Find **Connection string**
3. Copy the URI format
4. Replace `[YOUR-PASSWORD]` with your actual password

### From Environment Variables:
Your `.env` file should contain:
```
EXPO_PUBLIC_SUPABASE_URL=https://[project-ref].supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
```

## 🔍 Troubleshooting

### "pg_dump: command not found"
Install PostgreSQL client tools (see Setup Instructions above)

### "Connection refused"
- Check your database URL
- Verify your password
- Ensure your IP is whitelisted in Supabase

### "Permission denied"
- Verify your database password
- Check if your user has backup permissions

### Large backup files
- Use compression: `gzip backup.sql`
- Consider data-only backups for regular backups
- Use schema-only backups for structure changes

## 📊 Monitoring Backup Size

```bash
# Check backup sizes
du -sh backups/*

# List backups by date
ls -la backups/ | grep backup
```

## 🔄 Automated Backups

Consider setting up automated backups using:
- **Cron jobs** (Linux/macOS)
- **GitHub Actions** (for CI/CD)
- **Supabase's built-in backup features**

Example cron job (daily at 2 AM):
```bash
0 2 * * * cd /path/to/your/project && ./scripts/direct_backup.sh
```
