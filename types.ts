export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      announcements: {
        Row: {
          created_at: string
          created_by: string
          date: string | null
          group_id: string
          id: string
          message: string
          title: string
          type: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          date?: string | null
          group_id: string
          id?: string
          message: string
          title: string
          type: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          date?: string | null
          group_id?: string
          id?: string
          message?: string
          title?: string
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "announcements_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      colectas: {
        Row: {
          account_info: string
          alias_cbu: string
          base_amount: number
          created_at: string
          created_by: string
          cuit_cuil: string
          descripcion: string
          fecha_fin: string
          group_id: string
          id: string
          motivo: string
          status: string
          titular_cuenta: string
          total_collected: number
          total_contributions: number
          updated_at: string
        }
        Insert: {
          account_info: string
          alias_cbu: string
          base_amount: number
          created_at?: string
          created_by: string
          cuit_cuil: string
          descripcion: string
          fecha_fin: string
          group_id: string
          id?: string
          motivo: string
          status: string
          titular_cuenta: string
          total_collected?: number
          total_contributions?: number
          updated_at?: string
        }
        Update: {
          account_info?: string
          alias_cbu?: string
          base_amount?: number
          created_at?: string
          created_by?: string
          cuit_cuil?: string
          descripcion?: string
          fecha_fin?: string
          group_id?: string
          id?: string
          motivo?: string
          status?: string
          titular_cuenta?: string
          total_collected?: number
          total_contributions?: number
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "colectas_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      contributions: {
        Row: {
          amount: number
          colecta_id: string
          created_at: string
          id: string
          kid_id: string
          payment_method: string
          payment_reference: string | null
          proof_of_payment_url: string | null
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          amount: number
          colecta_id: string
          created_at?: string
          id?: string
          kid_id: string
          payment_method: string
          payment_reference?: string | null
          proof_of_payment_url?: string | null
          status: string
          updated_at?: string
          user_id: string
        }
        Update: {
          amount?: number
          colecta_id?: string
          created_at?: string
          id?: string
          kid_id?: string
          payment_method?: string
          payment_reference?: string | null
          proof_of_payment_url?: string | null
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "contributions_colecta_id_fkey"
            columns: ["colecta_id"]
            isOneToOne: false
            referencedRelation: "colectas"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "contributions_kid_id_fkey"
            columns: ["kid_id"]
            isOneToOne: false
            referencedRelation: "kids"
            referencedColumns: ["id"]
          },
        ]
      }
      event_attendees: {
        Row: {
          created_at: string
          event_id: string
          id: string
          kid_id: string
          status: string
        }
        Insert: {
          created_at?: string
          event_id: string
          id?: string
          kid_id: string
          status: string
        }
        Update: {
          created_at?: string
          event_id?: string
          id?: string
          kid_id?: string
          status?: string
        }
        Relationships: [
          {
            foreignKeyName: "event_attendees_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "event_attendees_kid_id_fkey"
            columns: ["kid_id"]
            isOneToOne: false
            referencedRelation: "kids"
            referencedColumns: ["id"]
          },
        ]
      }
      events: {
        Row: {
          created_at: string
          created_by: string
          date: string
          description: string | null
          end_date: string | null
          group_id: string
          id: string
          location: string | null
          title: string
          type: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          date: string
          description?: string | null
          end_date?: string | null
          group_id: string
          id?: string
          location?: string | null
          title: string
          type: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          date?: string
          description?: string | null
          end_date?: string | null
          group_id?: string
          id?: string
          location?: string | null
          title?: string
          type?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "events_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      group_memberships: {
        Row: {
          created_at: string
          group_id: string
          id: string
          is_referente: boolean
          kid_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          group_id: string
          id?: string
          is_referente?: boolean
          kid_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          group_id?: string
          id?: string
          is_referente?: boolean
          kid_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "group_memberships_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "group_memberships_kid_id_fkey"
            columns: ["kid_id"]
            isOneToOne: false
            referencedRelation: "kids"
            referencedColumns: ["id"]
          },
        ]
      }
      groups: {
        Row: {
          año: number
          institution_id: string
          created_at: string
          created_by: string
          id: string
          nombre: string | null
          sala: string
          updated_at: string
        }
        Insert: {
          año: number
          institution_id: string
          created_at?: string
          created_by: string
          id?: string
          nombre?: string | null
          sala: string
          updated_at?: string
        }
        Update: {
          año?: number
          institution_id?: string
          created_at?: string
          created_by?: string
          id?: string
          nombre?: string | null
          sala?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "groups_institution_id_fkey"
            columns: ["institution_id"]
            isOneToOne: false
            referencedRelation: "institutions"
            referencedColumns: ["id"]
          }
        ]
      }
      institutions: {
        Row: {
          id: string
          name: string
          address: string | null
          status: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          address?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          address?: string | null
          status?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      kids: {
        Row: {
          birth_date: string | null
          created_at: string
          dni: string | null
          first_name: string
          full_name: string | null
          id: string
          last_name: string
          nickname: string | null
          updated_at: string
        }
        Insert: {
          birth_date?: string | null
          created_at?: string
          dni?: string | null
          first_name: string
          full_name?: string | null
          id?: string
          last_name: string
          nickname?: string | null
          updated_at?: string
        }
        Update: {
          birth_date?: string | null
          created_at?: string
          dni?: string | null
          first_name?: string
          full_name?: string | null
          id?: string
          last_name?: string
          nickname?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      notifications: {
        Row: {
          announcement_id: string | null
          colecta_id: string | null
          created_at: string
          event_id: string | null
          group_id: string | null
          id: string
          is_read: boolean
          message: string
          poll_id: string | null
          type: string
          user_id: string
        }
        Insert: {
          announcement_id?: string | null
          colecta_id?: string | null
          created_at?: string
          event_id?: string | null
          group_id?: string | null
          id?: string
          is_read?: boolean
          message: string
          poll_id?: string | null
          type: string
          user_id: string
        }
        Update: {
          announcement_id?: string | null
          colecta_id?: string | null
          created_at?: string
          event_id?: string | null
          group_id?: string | null
          id?: string
          is_read?: boolean
          message?: string
          poll_id?: string | null
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_announcement_id_fkey"
            columns: ["announcement_id"]
            isOneToOne: false
            referencedRelation: "announcements"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_colecta_id_fkey"
            columns: ["colecta_id"]
            isOneToOne: false
            referencedRelation: "colectas"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
        ]
      }
      poll_options: {
        Row: {
          created_at: string
          id: string
          poll_id: string
          text: string
          updated_at: string
          votes: number
        }
        Insert: {
          created_at?: string
          id?: string
          poll_id: string
          text: string
          updated_at?: string
          votes?: number
        }
        Update: {
          created_at?: string
          id?: string
          poll_id?: string
          text?: string
          updated_at?: string
          votes?: number
        }
        Relationships: [
          {
            foreignKeyName: "poll_options_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
        ]
      }
      poll_votes: {
        Row: {
          created_at: string
          id: string
          option_id: string
          poll_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          option_id: string
          poll_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          option_id?: string
          poll_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "poll_votes_option_id_fkey"
            columns: ["option_id"]
            isOneToOne: false
            referencedRelation: "poll_options"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "poll_votes_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
        ]
      }
      polls: {
        Row: {
          created_at: string
          created_by: string
          description: string | null
          end_date: string | null
          group_id: string
          id: string
          status: string
          title: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          created_by: string
          description?: string | null
          end_date?: string | null
          group_id: string
          id?: string
          status: string
          title: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          created_by?: string
          description?: string | null
          end_date?: string | null
          group_id?: string
          id?: string
          status?: string
          title?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "polls_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string
          email: string
          first_name: string
          full_name: string | null
          id: string
          last_name: string
          nickname: string | null
          phone: string | null
          profile_picture: string | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          email: string
          first_name: string
          full_name?: string | null
          id: string
          last_name: string
          nickname?: string | null
          phone?: string | null
          profile_picture?: string | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          email?: string
          first_name?: string
          full_name?: string | null
          id?: string
          last_name?: string
          nickname?: string | null
          phone?: string | null
          profile_picture?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      votes: {
        Row: {
          created_at: string
          id: string
          option_id: string
          poll_id: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          option_id: string
          poll_id: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          option_id?: string
          poll_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "votes_option_id_fkey"
            columns: ["option_id"]
            isOneToOne: false
            referencedRelation: "poll_options"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "votes_poll_id_fkey"
            columns: ["poll_id"]
            isOneToOne: false
            referencedRelation: "polls"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      user_group_memberships: {
        Row: {
          auth_uid: string | null
          group_id: string | null
        }
        Relationships: []
      }
    }
    Functions: {
      exec_sql: {
        Args: { sql: string }
        Returns: undefined
      }
      increment_by: {
        Args: { value: number; increment: number }
        Returns: number
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

// Relación muchos a muchos entre kids y parents
export interface KidParentLink {
  id: string;
  kidId: string;
  parentId: string;
  createdAt: string;
}
