-- This is a rollback script to revert the changes made in the up.sql migration
-- Note: This will lose data if the columns are dropped

-- Revert changes to users table
DO $$
BEGIN
    -- Check if full_name is a generated column
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'full_name'
        AND is_generated = 'ALWAYS'
    ) THEN
        -- Create a temporary column to store the full name
        ALTER TABLE public.users ADD COLUMN temp_full_name TEXT;
        
        -- Copy the generated full_name to the temporary column
        UPDATE public.users SET temp_full_name = full_name;
        
        -- Drop the generated full_name column
        ALTER TABLE public.users DROP COLUMN full_name;
        
        -- Rename the temporary column to full_name
        ALTER TABLE public.users RENAME COLUMN temp_full_name TO full_name;
        
        -- Make full_name NOT NULL
        ALTER TABLE public.users ALTER COLUMN full_name SET NOT NULL;
    END IF;

    -- Drop the nickname column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'nickname'
    ) THEN
        ALTER TABLE public.users DROP COLUMN nickname;
    END IF;

    -- Drop the first_name column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'first_name'
    ) THEN
        ALTER TABLE public.users DROP COLUMN first_name;
    END IF;

    -- Drop the last_name column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'last_name'
    ) THEN
        ALTER TABLE public.users DROP COLUMN last_name;
    END IF;
END $$;

-- Revert changes to kids table
DO $$
BEGIN
    -- Check if full_name is a generated column
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'kids'
        AND column_name = 'full_name'
        AND is_generated = 'ALWAYS'
    ) THEN
        -- Create a temporary column to store the full name
        ALTER TABLE public.kids ADD COLUMN temp_full_name TEXT;
        
        -- Copy the generated full_name to the temporary column
        UPDATE public.kids SET temp_full_name = full_name;
        
        -- Drop the generated full_name column
        ALTER TABLE public.kids DROP COLUMN full_name;
        
        -- Rename the temporary column to full_name
        ALTER TABLE public.kids RENAME COLUMN temp_full_name TO full_name;
        
        -- Make full_name NOT NULL
        ALTER TABLE public.kids ALTER COLUMN full_name SET NOT NULL;
    END IF;

    -- Drop the nickname column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'kids'
        AND column_name = 'nickname'
    ) THEN
        ALTER TABLE public.kids DROP COLUMN nickname;
    END IF;

    -- Drop the first_name column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'kids'
        AND column_name = 'first_name'
    ) THEN
        ALTER TABLE public.kids DROP COLUMN first_name;
    END IF;

    -- Drop the last_name column if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'kids'
        AND column_name = 'last_name'
    ) THEN
        ALTER TABLE public.kids DROP COLUMN last_name;
    END IF;
END $$;
