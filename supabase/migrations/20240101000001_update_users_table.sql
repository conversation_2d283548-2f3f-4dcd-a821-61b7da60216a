-- Update users table to add first_name, last_name, and nickname columns
-- First, check if the columns already exist
DO $$
BEGIN
    -- Check if first_name column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'first_name'
    ) THEN
        -- Add first_name column
        ALTER TABLE public.users ADD COLUMN first_name TEXT;
        
        -- Update first_name from full_name (split on first space)
        UPDATE public.users
        SET first_name = SPLIT_PART(full_name, ' ', 1)
        WHERE full_name IS NOT NULL;
        
        -- Make first_name NOT NULL
        ALTER TABLE public.users ALTER COLUMN first_name SET NOT NULL;
    END IF;

    -- Check if last_name column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'last_name'
    ) THEN
        -- Add last_name column
        ALTER TABLE public.users ADD COLUMN last_name TEXT;
        
        -- Update last_name from full_name (everything after first space)
        UPDATE public.users
        SET last_name = SUBSTRING(full_name FROM POSITION(' ' IN full_name) + 1)
        WHERE full_name IS NOT NULL AND POSITION(' ' IN full_name) > 0;
        
        -- If there's no space in full_name, use full_name as last_name
        UPDATE public.users
        SET last_name = full_name
        WHERE full_name IS NOT NULL AND POSITION(' ' IN full_name) = 0 AND last_name IS NULL;
        
        -- Make last_name NOT NULL
        ALTER TABLE public.users ALTER COLUMN last_name SET NOT NULL;
    END IF;

    -- Check if nickname column exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'nickname'
    ) THEN
        -- Add nickname column
        ALTER TABLE public.users ADD COLUMN nickname TEXT;
    END IF;

    -- Check if full_name is a generated column
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'users'
        AND column_name = 'full_name'
        AND is_generated = 'ALWAYS'
    ) THEN
        -- Drop the full_name column and recreate it as a generated column
        ALTER TABLE public.users DROP COLUMN IF EXISTS full_name;
        ALTER TABLE public.users ADD COLUMN full_name TEXT GENERATED ALWAYS AS (first_name || ' ' || last_name) STORED;
    END IF;
END $$;

-- Placeholder for migration history sync
