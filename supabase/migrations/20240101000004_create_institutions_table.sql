-- Migration: Create institutions table and update groups table
-- This migration creates a new institutions table and migrates existing colegio data

-- Step 1: Create institutions table
CREATE TABLE IF NOT EXISTS public.institutions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  address TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Step 2: Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_institutions_updated_at
    BEFORE UPDATE ON public.institutions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Step 3: Migrate existing colegio data to institutions table
INSERT INTO public.institutions (name)
SELECT DISTINCT colegio
FROM public.groups
WHERE colegio IS NOT NULL
ON CONFLICT (name) DO NOTHING;

-- Step 4: Add institution_id column to groups table
ALTER TABLE public.groups ADD COLUMN institution_id UUID REFERENCES public.institutions(id);

-- Step 5: Update groups table to set institution_id based on existing colegio
UPDATE public.groups
SET institution_id = (
  SELECT id
  FROM public.institutions
  WHERE institutions.name = groups.colegio
)
WHERE colegio IS NOT NULL;

-- Step 6: Make institution_id NOT NULL (after data migration)
ALTER TABLE public.groups ALTER COLUMN institution_id SET NOT NULL;

-- Step 7: Drop the old colegio column
ALTER TABLE public.groups DROP COLUMN colegio;

-- Step 8: Enable RLS on institutions table
ALTER TABLE public.institutions ENABLE ROW LEVEL SECURITY;

-- Step 9: Create RLS policies for institutions table
-- Allow authenticated users to read all institutions
CREATE POLICY "Allow authenticated users to read institutions" ON public.institutions
  FOR SELECT TO authenticated USING (true);

-- Allow authenticated users to insert new institutions
CREATE POLICY "Allow authenticated users to insert institutions" ON public.institutions
  FOR INSERT TO authenticated WITH CHECK (true);

-- Step 10: Update the materialized view if it exists
-- First drop any dependent objects if they exist
DROP MATERIALIZED VIEW IF EXISTS public.user_group_memberships CASCADE;

-- Recreate the materialized view with the new structure
CREATE MATERIALIZED VIEW public.user_group_memberships AS
SELECT
    gm.id,
    gm.group_id,
    gm.user_id,
    gm.kid_id,
    gm.is_referente,
    gm.created_at,
    i.name as colegio,
    g.sala,
    g.año,
    g.nombre as grupo_nombre
FROM
    public.group_memberships gm
    JOIN public.groups g ON g.id = gm.group_id
    JOIN public.institutions i ON i.id = g.institution_id;

-- Step 11: Grant permissions
GRANT SELECT, INSERT ON public.institutions TO authenticated;
