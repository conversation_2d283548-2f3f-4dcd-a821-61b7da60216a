-- Update email templates
UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/confirm-signup.html')::text
)
WHERE template_type = 'confirmation';

UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/invite-user.html')::text
)
WHERE template_type = 'invite';

UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/magic-link.html')::text
)
WHERE template_type = 'magic_link';

UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/change-email.html')::text
)
WHERE template_type = 'change_email';

UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/reset-password.html')::text
)
WHERE template_type = 'recovery';

UPDATE auth.templates
SET template = (
  SELECT content FROM pg_read_file('supabase/email-templates/reauthentication.html')::text
)
WHERE template_type = 'mfa_verify';
