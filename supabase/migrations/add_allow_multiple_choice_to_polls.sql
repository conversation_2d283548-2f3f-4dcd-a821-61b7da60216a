-- Add allow_multiple_choice column to polls table
-- This allows polls to support both single choice and multiple choice voting

ALTER TABLE public.polls
ADD COLUMN allow_multiple_choice BOOLEAN DEFAULT false;

-- Add comment to document the column
COMMENT ON COLUMN public.polls.allow_multiple_choice IS 'Whether this poll allows multiple choice selection (true) or single choice only (false)';

-- Update the create_poll function to include the new parameter
CREATE OR REPLACE FUNCTION create_poll(
  p_group_id UUID,
  p_title TEXT,
  p_created_by UUID,
  p_options TEXT[],
  p_description TEXT DEFAULT NULL,
  p_end_date TIMESTAMP DEFAULT NULL,
  p_allow_multiple_choice BOOLEAN DEFAULT false
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  poll_id UUID;
  option_text TEXT;
  result JSON;
BEGIN
  -- Insert the poll
  INSERT INTO public.polls (
    group_id,
    title,
    description,
    end_date,
    status,
    created_by,
    allow_multiple_choice
  ) VALUES (
    p_group_id,
    p_title,
    p_description,
    p_end_date,
    'DRAFT',
    p_created_by,
    p_allow_multiple_choice
  ) RETURNING id INTO poll_id;

  -- Insert the options
  FOREACH option_text IN ARRAY p_options
  LOOP
    INSERT INTO public.poll_options (poll_id, text)
    VALUES (poll_id, option_text);
  END LOOP;

  -- Return the poll ID
  SELECT json_build_object('poll_id', poll_id) INTO result;
  RETURN result;
END;
$$;
