# Supabase Database Setup

This document provides instructions on how to set up the database schema in your Supabase project.

## Setting Up the Database Schema

1. Log in to your Supabase dashboard at https://app.supabase.com/
2. Select your project
3. Go to the SQL Editor in the left sidebar
4. Create a new query
5. Copy and paste the contents of the `create_tables.sql` file into the SQL editor
6. Click "Run" to execute the SQL commands

## What This Will Create

The SQL script will create the following tables:

- `users`: Stores user profile information, linked to Supabase auth.users
- `kids`: Stores information about children
- `groups`: Stores information about groups (classrooms, etc.)
- `group_memberships`: Links users, kids, and groups together

It will also create:

- Triggers to automatically update the `updated_at` column when records are modified
- A trigger to automatically create a user profile in the `users` table when a new user signs up

## Troubleshooting

If you encounter the error "relation 'public.users' does not exist" when trying to log in, it means the database tables haven't been created yet. Follow the steps above to create the necessary tables.

## Email Templates

To customize the email templates for verification and password reset:

1. Go to Authentication > Email Templates in your Supabase dashboard
2. Customize the following templates:
   - Confirmation Email
   - Recovery (Reset Password) Email
3. Make sure to include the magic link in both templates

Example for Confirmation Email:
```
<h2>Confirm your email</h2>
<p>Follow this link to confirm your email:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm Email</a></p>
```

Example for Recovery Email:
```
<h2>Reset your password</h2>
<p>Follow this link to reset the password for your account:</p>
<p><a href="{{ .ConfirmationURL }}">Reset Password</a></p>
```

## Authentication Settings

To ensure email confirmation works correctly:

1. Go to Authentication > Providers in your Supabase dashboard
2. Under Email, make sure "Enable Email Confirmations" is turned on
3. Set "Redirect URLs" to include your app's URL scheme: `qids://confirm-email`
