-- Create schema for public tables
CREATE SCHEMA IF NOT EXISTS public;

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  full_name TEXT NOT NULL,
  phone TEXT,
  profile_picture TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create kids table
CREATE TABLE IF NOT EXISTS public.kids (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_id UUID REFERENCES public.users(id) NOT NULL,
  full_name TEXT NOT NULL,
  birth_date DATE,
  dni TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- <PERSON><PERSON> groups table
CREATE TABLE IF NOT EXISTS public.groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  colegio TEXT NOT NULL,
  sala TEXT NOT NULL,
  año INTEGER NOT NULL,
  nombre TEXT,
  created_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create group_memberships table (many-to-many relationship between users, kids, and groups)
CREATE TABLE IF NOT EXISTS public.group_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES public.groups(id) NOT NULL,
  user_id UUID REFERENCES public.users(id) NOT NULL,
  kid_id UUID REFERENCES public.kids(id),
  is_referente BOOLEAN DEFAULT false NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(group_id, user_id, kid_id)
);

-- Create announcements table
CREATE TABLE IF NOT EXISTS public.announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES public.groups(id) NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  type TEXT NOT NULL,
  date TIMESTAMP WITH TIME ZONE,
  created_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create colectas (collections) table
CREATE TABLE IF NOT EXISTS public.colectas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES public.groups(id) NOT NULL,
  motivo TEXT NOT NULL,
  descripcion TEXT NOT NULL,
  fecha_fin TIMESTAMP WITH TIME ZONE NOT NULL,
  base_amount DECIMAL(10, 2) NOT NULL,
  total_collected DECIMAL(10, 2) DEFAULT 0 NOT NULL,
  total_contributions INTEGER DEFAULT 0 NOT NULL,
  account_info TEXT NOT NULL,
  status TEXT NOT NULL,
  created_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create contributions table
CREATE TABLE IF NOT EXISTS public.contributions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  colecta_id UUID REFERENCES public.colectas(id) NOT NULL,
  user_id UUID REFERENCES public.users(id) NOT NULL,
  kid_id UUID REFERENCES public.kids(id) NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  payment_method TEXT NOT NULL,
  payment_reference TEXT,
  status TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create polls table
CREATE TABLE IF NOT EXISTS public.polls (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES public.groups(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  end_date TIMESTAMP WITH TIME ZONE,
  status TEXT NOT NULL,
  created_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create poll_options table
CREATE TABLE IF NOT EXISTS public.poll_options (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  poll_id UUID REFERENCES public.polls(id) NOT NULL,
  text TEXT NOT NULL,
  votes INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create poll_votes table
CREATE TABLE IF NOT EXISTS public.poll_votes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  poll_id UUID REFERENCES public.polls(id) NOT NULL,
  option_id UUID REFERENCES public.poll_options(id) NOT NULL,
  user_id UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(poll_id, user_id)
);

-- Create events table
CREATE TABLE IF NOT EXISTS public.events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES public.groups(id) NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE,
  location TEXT,
  created_by UUID REFERENCES public.users(id) NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) NOT NULL,
  type TEXT NOT NULL,
  message TEXT NOT NULL,
  group_id UUID REFERENCES public.groups(id),
  event_id UUID REFERENCES public.events(id),
  announcement_id UUID REFERENCES public.announcements(id),
  colecta_id UUID REFERENCES public.colectas(id),
  poll_id UUID REFERENCES public.polls(id),
  is_read BOOLEAN DEFAULT false NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON public.users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kids_updated_at
  BEFORE UPDATE ON public.kids
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_groups_updated_at
  BEFORE UPDATE ON public.groups
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_group_memberships_updated_at
  BEFORE UPDATE ON public.group_memberships
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_announcements_updated_at
  BEFORE UPDATE ON public.announcements
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_colectas_updated_at
  BEFORE UPDATE ON public.colectas
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_contributions_updated_at
  BEFORE UPDATE ON public.contributions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_polls_updated_at
  BEFORE UPDATE ON public.polls
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_poll_options_updated_at
  BEFORE UPDATE ON public.poll_options
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_events_updated_at
  BEFORE UPDATE ON public.events
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Function to create a user profile after signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Use security definer and set the search_path to avoid SQL injection
  -- This function will run with the privileges of the function owner
  -- The security definer attribute allows the function to bypass RLS policies
  INSERT INTO public.users (id, email, full_name)
  VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email));
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;

-- Trigger to create a user profile after signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Function to update colecta totals after a contribution
CREATE OR REPLACE FUNCTION public.update_colecta_totals()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.colectas
  SET
    total_collected = total_collected + NEW.amount,
    total_contributions = total_contributions + 1
  WHERE id = NEW.colecta_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update colecta totals after a contribution
CREATE TRIGGER on_contribution_created
  AFTER INSERT ON public.contributions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_colecta_totals();

-- Function to update poll option votes
CREATE OR REPLACE FUNCTION public.update_poll_option_votes()
RETURNS TRIGGER AS $$
BEGIN
  UPDATE public.poll_options
  SET votes = votes + 1
  WHERE id = NEW.option_id;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update poll option votes
CREATE TRIGGER on_poll_vote_created
  AFTER INSERT ON public.poll_votes
  FOR EACH ROW
  EXECUTE FUNCTION public.update_poll_option_votes();

-- Row Level Security Policies

-- Users table policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own profile"
  ON public.users FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
  ON public.users FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Service role can manage users"
  ON public.users
  USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Allow insert for authenticated users"
  ON public.users FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' OR auth.uid() = id);

-- Kids table policies
ALTER TABLE public.kids ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own kids"
  ON public.kids FOR SELECT
  USING (auth.uid() = parent_id);

CREATE POLICY "Users can insert their own kids"
  ON public.kids FOR INSERT
  WITH CHECK (auth.uid() = parent_id);

CREATE POLICY "Users can update their own kids"
  ON public.kids FOR UPDATE
  USING (auth.uid() = parent_id);

CREATE POLICY "Users can delete their own kids"
  ON public.kids FOR DELETE
  USING (auth.uid() = parent_id);

-- Groups table policies
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view groups"
  ON public.groups FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can create groups"
  ON public.groups FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Group creators can update their groups"
  ON public.groups FOR UPDATE
  USING (auth.uid() = created_by);

-- Group memberships policies
ALTER TABLE public.group_memberships ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view group memberships"
  ON public.group_memberships FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Users can join groups"
  ON public.group_memberships FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can leave groups"
  ON public.group_memberships FOR DELETE
  USING (auth.uid() = user_id);

-- Announcements policies
ALTER TABLE public.announcements ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view announcements for their groups"
  ON public.announcements FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = announcements.group_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Group referentes can create announcements"
  ON public.announcements FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = announcements.group_id
      AND group_memberships.user_id = auth.uid()
      AND group_memberships.is_referente = true
    )
  );

-- Colectas policies
ALTER TABLE public.colectas ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view colectas for their groups"
  ON public.colectas FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = colectas.group_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Group referentes can create colectas"
  ON public.colectas FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = colectas.group_id
      AND group_memberships.user_id = auth.uid()
      AND group_memberships.is_referente = true
    )
  );

-- Contributions policies
ALTER TABLE public.contributions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view contributions for their groups"
  ON public.contributions FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.colectas
      JOIN public.group_memberships ON colectas.group_id = group_memberships.group_id
      WHERE colectas.id = contributions.colecta_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create their own contributions"
  ON public.contributions FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Polls policies
ALTER TABLE public.polls ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view polls for their groups"
  ON public.polls FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = polls.group_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Group referentes can create polls"
  ON public.polls FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = polls.group_id
      AND group_memberships.user_id = auth.uid()
      AND group_memberships.is_referente = true
    )
  );

-- Poll options policies
ALTER TABLE public.poll_options ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view poll options"
  ON public.poll_options FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.polls
      JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
      WHERE polls.id = poll_options.poll_id
      AND group_memberships.user_id = auth.uid()
    )
  );

-- Poll votes policies
ALTER TABLE public.poll_votes ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view poll votes"
  ON public.poll_votes FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.polls
      JOIN public.group_memberships ON polls.group_id = group_memberships.group_id
      WHERE polls.id = poll_votes.poll_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create their own poll votes"
  ON public.poll_votes FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Events policies
ALTER TABLE public.events ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view events for their groups"
  ON public.events FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = events.group_id
      AND group_memberships.user_id = auth.uid()
    )
  );

CREATE POLICY "Group referentes can create events"
  ON public.events FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.group_memberships
      WHERE group_memberships.group_id = events.group_id
      AND group_memberships.user_id = auth.uid()
      AND group_memberships.is_referente = true
    )
  );

-- Notifications policies
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own notifications"
  ON public.notifications FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications"
  ON public.notifications FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);
