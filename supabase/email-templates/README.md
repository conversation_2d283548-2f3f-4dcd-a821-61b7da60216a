# Supabase Email Templates

Este directorio contiene las plantillas de correo electrónico HTML para Supabase Auth.

## Plantillas disponibles

- **confirm-signup.html**: Correo de confirmación de registro
- **invite-user.html**: Invitación a un nuevo usuario
- **magic-link.html**: Enlace mágico para iniciar sesión sin contraseña
- **change-email.html**: Confirmación de cambio de correo electrónico
- **reset-password.html**: Restablecimiento de contraseña
- **reauthentication.html**: Verificación de identidad para acciones sensibles

## Cómo actualizar las plantillas

Hay tres formas de actualizar las plantillas de correo electrónico:

### 1. Usando el Dashboard de Supabase

1. Ve al [Dashboard de Supabase](https://supabase.com/dashboard)
2. Selecciona tu proyecto
3. Navega a Authentication > Email Templates
4. Selecciona la plantilla que deseas actualizar
5. Pega el contenido HTML de la plantilla
6. Haz clic en "Save"

### 2. Usando el script de actualización

Puedes usar el script `scripts/update_email_templates.js` para actualizar todas las plantillas automáticamente:

```bash
# Asegúrate de tener las variables de entorno necesarias
export SUPABASE_SERVICE_ROLE_KEY=tu_service_role_key
export SUPABASE_PROJECT_ID=tu_project_id

# Ejecuta el script
node scripts/update_email_templates.js
```

### 3. Usando una migración SQL

También puedes aplicar la migración SQL para actualizar todas las plantillas:

```bash
# Ejecuta la migración
supabase db execute --file supabase/migrations/20240525_update_email_templates.sql
```

## Variables disponibles en las plantillas

Supabase proporciona las siguientes variables que puedes usar en tus plantillas:

- `{{ .ConfirmationURL }}`: URL de confirmación/acción
- `{{ .Token }}`: Token de autenticación
- `{{ .TokenHash }}`: Hash del token
- `{{ .SiteURL }}`: URL del sitio
- `{{ .Email }}`: Correo electrónico del usuario
- `{{ .Data }}`: Datos adicionales (JSON)
- `{{ .RedirectTo }}`: URL de redirección después de la acción

## Consejos para las plantillas de correo

1. **Prueba en diferentes clientes de correo**: Asegúrate de que tus plantillas se vean bien en Gmail, Outlook, Apple Mail, etc.
2. **Mantén el diseño simple**: Usa HTML y CSS básicos para mayor compatibilidad.
3. **Incluye siempre el enlace en texto plano**: Algunos clientes de correo bloquean los botones HTML.
4. **Usa imágenes alojadas en Supabase Storage**: Para asegurar que las imágenes siempre estén disponibles.

## Solución de problemas

Si las plantillas no se actualizan correctamente, verifica:

1. Que tienes los permisos necesarios (necesitas ser propietario del proyecto o tener el rol de service_role)
2. Que el HTML es válido y no contiene errores de sintaxis
3. Que las variables de Supabase están correctamente formateadas ({{ .VariableName }})

Para más información, consulta la [documentación oficial de Supabase sobre plantillas de correo](https://supabase.com/docs/guides/auth/auth-email-templates).
