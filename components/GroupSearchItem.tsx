import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card } from './Card';
import { colors } from '@/constants/colors';
import { Group } from '@/types';

interface GroupSearchItemProps {
  group: Group;
  onPress: () => void;
}

export const GroupSearchItem: React.FC<GroupSearchItemProps> = ({
  group,
  onPress,
}) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Card variant="outlined" style={styles.card}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.colegio}>{group.institutionName || 'Institución'}</Text>
            <Text style={styles.sala}>{group.sala} - {group.año}</Text>
            {group.nombre && <Text style={styles.nombre}>{group.nombre}</Text>}
          </View>
        </View>

        <View style={styles.footer}>
          <View style={styles.iconContainer}>
            <Ionicons name="people-outline" size={24} color={colors.primary} />
            <Text style={styles.membersText}>Unirse al grupo</Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
  },
  colegio: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  sala: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  nombre: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  membersText: {
    fontSize: 14,
    color: colors.primary,
    fontWeight: '500',
  },
});