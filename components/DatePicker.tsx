/**
 * DatePicker Component
 *
 * A flexible date picker component that can be used both inline and as a modal.
 * Supports date validation, minimum date constraints, and various display formats.
 *
 * Features:
 * - Input field with date validation
 * - Calendar view for date selection
 * - Year and month selectors
 * - Minimum date validation
 * - Error handling and display
 *
 * @component
 */

import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, TextInput, Modal, ScrollView } from 'react-native';
import { Calendar, ChevronDown } from 'lucide-react-native';
import { colors } from '@/constants/colors';

interface DatePickerProps {
  /** Label text displayed above the input */
  label?: string;
  /** Current date value in ISO format or formatted string */
  value: string;
  /** Callback when date changes, receives ISO date string */
  onChange?: (date: string) => void;
  /** Placeholder text when no date is selected */
  placeholder?: string;
  /** Error message to display */
  error?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** For modal mode: whether the date picker is visible */
  visible?: boolean;
  /** For modal mode: initial date to display */
  date?: Date;
  /** For modal mode: callback when date is confirmed */
  onConfirm?: (date: Date) => void;
  /** For modal mode: callback when selection is canceled */
  onCancel?: () => void;
  /** For modal mode: callback when modal is closed */
  onClose?: () => void;
  /** Minimum selectable date */
  minimumDate?: Date;
}

export const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Seleccionar fecha',
  error,
  disabled = false,
  visible = false,
  date,
  onConfirm,
  onCancel,
  onClose,
  minimumDate,
}) => {
  // State for the component
  const [showCalendar, setShowCalendar] = useState(visible);
  const [inputValue, setInputValue] = useState('');
  const [inputError, setInputError] = useState('');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [showYearSelector, setShowYearSelector] = useState(false);
  const [showMonthSelector, setShowMonthSelector] = useState(false);

  // Month names for localization
  const monthNames = [
    'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
    'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
  ];

  // Initialize input value from prop
  useEffect(() => {
    if (value) {
      // Convert ISO date to dd/mm/yyyy
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          const day = date.getDate().toString().padStart(2, '0');
          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const year = date.getFullYear();
          setInputValue(`${day}/${month}/${year}`);

          // Also set the calendar to this date
          setSelectedMonth(date.getMonth());
          setSelectedYear(date.getFullYear());
        } else {
          setInputValue(value); // Just use the value as is
        }
      } catch (e) {
        setInputValue(value); // Just use the value as is
      }
    } else {
      setInputValue('');
    }
  }, [value]);

  // Handle visible prop changes
  useEffect(() => {
    setShowCalendar(visible);

    // If date is provided, set the calendar to that date
    if (date) {
      setSelectedMonth(date.getMonth());
      setSelectedYear(date.getFullYear());
    }
  }, [visible, date]);

  /**
   * Handles changes to the input field, formatting as needed
   * @param text - The text entered by the user
   */
  const handleInputChange = useCallback((text: string) => {
    // Allow only digits and slashes
    const formattedText = text.replace(/[^\d\/]/g, '');

    // Auto-add slashes
    let formatted = formattedText;
    if (formattedText.length === 2 && !formattedText.includes('/') && inputValue.length < formattedText.length) {
      formatted = formattedText + '/';
    } else if (formattedText.length === 5 && formattedText.split('/').length === 2 && inputValue.length < formattedText.length) {
      formatted = formattedText + '/';
    }

    setInputValue(formatted);
    setInputError('');
  }, [inputValue]);

  /**
   * Validates the input date and calls onChange if valid
   * @returns boolean indicating if the date is valid
   */
  const validateAndSubmitInput = useCallback(() => {
    // Validate date format (dd/mm/yyyy)
    const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
    const match = inputValue.match(dateRegex);

    if (!match) {
      setInputError('Formato inválido. Use dd/mm/yyyy');
      return false;
    }

    const day = parseInt(match[1], 10);
    const month = parseInt(match[2], 10) - 1; // 0-based month
    const year = parseInt(match[3], 10);

    // Validate date values
    if (month < 0 || month > 11) {
      setInputError('Mes inválido (1-12)');
      return false;
    }

    const daysInMonth = new Date(year, month + 1, 0).getDate();
    if (day < 1 || day > daysInMonth) {
      setInputError(`Día inválido (1-${daysInMonth})`);
      return false;
    }

    // Check minimum date if provided
    if (minimumDate) {
      const selectedDate = new Date(year, month, day);
      if (selectedDate < minimumDate) {
        setInputError(`La fecha debe ser posterior a ${minimumDate.toLocaleDateString()}`);
        return false;
      }
    }

    // Valid date, convert to ISO format for the onChange handler
    const date = new Date(year, month, day);
    if (onChange) {
      onChange(date.toISOString());
    }
    return true;
  }, [inputValue, onChange, minimumDate]);

  /**
   * Handles selection of a day from the calendar
   * @param day - The day number selected
   */
  const handleDaySelect = useCallback((day: number) => {
    const date = new Date(selectedYear, selectedMonth, day);

    // Check minimum date if provided
    if (minimumDate && date < minimumDate) {
      setInputError(`La fecha debe ser posterior a ${minimumDate.toLocaleDateString()}`);
      return;
    }

    // Format as dd/mm/yyyy for display
    const formattedDay = day.toString().padStart(2, '0');
    const formattedMonth = (selectedMonth + 1).toString().padStart(2, '0');
    setInputValue(`${formattedDay}/${formattedMonth}/${selectedYear}`);

    // Use onConfirm if provided (for modal mode)
    if (onConfirm) {
      onConfirm(date);
    }
    // Use onChange if provided (for inline mode)
    else if (onChange) {
      onChange(date.toISOString());
    }

    setShowCalendar(false);
  }, [selectedYear, selectedMonth, onChange, onConfirm, minimumDate]);

  /**
   * Handles selection of a year from the year selector
   * @param year - The selected year
   */
  const handleYearSelect = useCallback((year: number) => {
    setSelectedYear(year);
    setShowYearSelector(false);
  }, []);

  /**
   * Handles selection of a month from the month selector
   * @param month - The selected month (0-11)
   */
  const handleMonthSelect = useCallback((month: number) => {
    setSelectedMonth(month);
    setShowMonthSelector(false);
  }, []);

  /**
   * Gets the number of days in the selected month
   * @param year - The year
   * @param month - The month (0-11)
   * @returns The number of days in the month
   */
  const getDaysInMonth = useCallback((year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  }, []);

  /**
   * Gets the day of week for the first day of the month
   * @param year - The year
   * @param month - The month (0-11)
   * @returns The day of week (0-6, where 0 is Sunday)
   */
  const getFirstDayOfMonth = useCallback((year: number, month: number) => {
    return new Date(year, month, 1).getDay();
  }, []);

  /**
   * Handles closing the calendar
   */
  const handleCloseCalendar = useCallback(() => {
    setShowCalendar(false);
    if (onClose) {
      onClose();
    } else if (onCancel) {
      onCancel();
    }
  }, [onClose, onCancel]);

  /**
   * Renders the calendar days grid
   * @returns Array of day cells
   */
  const renderCalendarDays = useCallback(() => {
    const days = [];
    const daysInMonth = getDaysInMonth(selectedYear, selectedMonth);
    const firstDay = getFirstDayOfMonth(selectedYear, selectedMonth);

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<View key={`empty-${i}`} style={styles.dayCell} />);
    }

    // Add cells for each day of the month
    for (let i = 1; i <= daysInMonth; i++) {
      const isToday =
        new Date().getDate() === i &&
        new Date().getMonth() === selectedMonth &&
        new Date().getFullYear() === selectedYear;

      // Check if this day is before minimum date
      let isDisabled = false;
      if (minimumDate) {
        const currentDate = new Date(selectedYear, selectedMonth, i);
        isDisabled = currentDate < minimumDate;
      }

      days.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.dayCell,
            isToday && styles.todayCell,
            isDisabled && styles.disabledDay
          ]}
          onPress={() => !isDisabled && handleDaySelect(i)}
          disabled={isDisabled}
        >
          <Text style={[
            styles.dayText,
            isToday && styles.todayText,
            isDisabled && styles.disabledDayText
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }

    return days;
  }, [selectedYear, selectedMonth, getDaysInMonth, getFirstDayOfMonth, handleDaySelect, minimumDate]);

  /**
   * Renders the year selector
   * @returns Array of year option components
   */
  const renderYearSelector = useCallback(() => {
    const years = [];
    const currentYear = new Date().getFullYear();
    const startYear = currentYear - 20;
    const endYear = currentYear;

    for (let i = startYear; i <= endYear; i++) {
      years.push(
        <TouchableOpacity
          key={i}
          style={[
            styles.yearItem,
            i === selectedYear && styles.selectedYear
          ]}
          onPress={() => handleYearSelect(i)}
        >
          <Text style={[
            styles.yearText,
            i === selectedYear && styles.selectedYearText
          ]}>
            {i}
          </Text>
        </TouchableOpacity>
      );
    }

    return years;
  }, [selectedYear, handleYearSelect]);

  /**
   * Renders the month selector
   * @returns Array of month option components
   */
  const renderMonthSelector = useCallback(() => {
    return monthNames.map((name, index) => (
      <TouchableOpacity
        key={`empty-${index}`}
        style={[
          styles.monthItem,
          index === selectedMonth && styles.selectedMonth
        ]}
        onPress={() => handleMonthSelect(index)}
      >
        <Text style={[
          styles.monthText,
          index === selectedMonth && styles.selectedMonthText
        ]}>
          {name}
        </Text>
      </TouchableOpacity>
    ));
  }, [monthNames, selectedMonth, handleMonthSelect]);

  // If this is being used as a modal (with visible, onConfirm, onCancel props)
  if (visible !== undefined && onConfirm && onCancel) {
    return (
      <Modal
        visible={visible}
        transparent
        animationType="fade"
        onRequestClose={onCancel}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={onCancel}
        >
          <View style={styles.modalContent} onStartShouldSetResponder={() => true}>
            {!showYearSelector && !showMonthSelector ? (
              <>
                <View style={styles.calendarHeader}>
                  <TouchableOpacity
                    style={styles.monthYearSelector}
                    onPress={() => setShowMonthSelector(true)}
                  >
                    <Text style={styles.monthYearText}>{monthNames[selectedMonth]}</Text>
                    <ChevronDown size={16} color={colors.text} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.monthYearSelector}
                    onPress={() => setShowYearSelector(true)}
                  >
                    <Text style={styles.monthYearText}>{selectedYear}</Text>
                    <ChevronDown size={16} color={colors.text} />
                  </TouchableOpacity>
                </View>

                <View style={styles.weekdayHeader}>
                  {['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'].map((day, index) => (
                    <Text key={`weekday-inline-${index}`} style={styles.weekdayText}>{day}</Text>
                  ))}
                </View>

                <View style={styles.calendarGrid}>
                  {renderCalendarDays()}
                </View>

                <View style={styles.modalFooter}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={onCancel}
                  >
                    <Text style={styles.cancelButtonText}>Cancelar</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : showYearSelector ? (
              <>
                <View style={styles.selectorHeader}>
                  <Text style={styles.selectorTitle}>Seleccionar Año</Text>
                </View>
                <ScrollView style={styles.selectorList}>
                  <View style={styles.yearSelectorGrid}>
                    {renderYearSelector()}
                  </View>
                </ScrollView>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowYearSelector(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <View style={styles.selectorHeader}>
                  <Text style={styles.selectorTitle}>Seleccionar Mes</Text>
                </View>
                <ScrollView style={styles.selectorList}>
                  {renderMonthSelector()}
                </ScrollView>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowMonthSelector(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    );
  }

  // Standard inline date picker
  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TouchableOpacity
        onPress={() => !disabled && setShowCalendar(true)}
        activeOpacity={0.7}
        style={[styles.inputContainer, disabled && styles.inputDisabled]}
      >
        <TextInput
          style={[styles.input, error && styles.inputError]}
          value={inputValue}
          placeholder={placeholder}
          editable={false}
          pointerEvents="none"
        />
        <TouchableOpacity
          onPress={() => !disabled && setShowCalendar(true)}
          activeOpacity={0.7}
          style={styles.calendarIconButton}
        >
          <View style={styles.calendarIconWrapper}>
            <Calendar size={20} color={colors.primary} style={styles.icon} />
          </View>
        </TouchableOpacity>
      </TouchableOpacity>
      {error && <Text style={styles.error}>{error}</Text>}
      <Modal
        visible={showCalendar}
        transparent
        animationType="fade"
        onRequestClose={() => setShowCalendar(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowCalendar(false)}
        >
          <View style={styles.modalContent} onStartShouldSetResponder={() => true}>
            {!showYearSelector && !showMonthSelector ? (
              <>
                <View style={styles.calendarHeader}>
                  <TouchableOpacity
                    style={styles.monthYearSelector}
                    onPress={() => setShowMonthSelector(true)}
                  >
                    <Text style={styles.monthYearText}>{monthNames[selectedMonth]}</Text>
                    <ChevronDown size={16} color={colors.text} />
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.monthYearSelector}
                    onPress={() => setShowYearSelector(true)}
                  >
                    <Text style={styles.monthYearText}>{selectedYear}</Text>
                    <ChevronDown size={16} color={colors.text} />
                  </TouchableOpacity>
                </View>

                <View style={styles.weekdayHeader}>
                  {['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'].map((day, index) => (
                    <Text key={`weekday-modal-${index}`} style={styles.weekdayText}>{day}</Text>
                  ))}
                </View>

                <View style={styles.calendarGrid}>
                  {renderCalendarDays()}
                </View>

                <View style={styles.modalFooter}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowCalendar(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancelar</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : showYearSelector ? (
              <>
                <View style={styles.selectorHeader}>
                  <Text style={styles.selectorTitle}>Seleccionar Año</Text>
                </View>
                <ScrollView style={styles.selectorList}>
                  <View style={styles.yearSelectorGrid}>
                    {renderYearSelector()}
                  </View>
                </ScrollView>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowYearSelector(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
              </>
            ) : (
              <>
                <View style={styles.selectorHeader}>
                  <Text style={styles.selectorTitle}>Seleccionar Mes</Text>
                </View>
                <ScrollView style={styles.selectorList}>
                  {renderMonthSelector()}
                </ScrollView>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={() => setShowMonthSelector(false)}
                >
                  <Text style={styles.cancelButtonText}>Cancelar</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.card,
  },
  input: {
    flex: 1,
    height: 48,
    paddingHorizontal: 12,
    fontSize: 16,
    color: colors.text,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputDisabled: {
    backgroundColor: colors.secondary,
    color: colors.textSecondary,
  },
  icon: {
    padding: 0,
  },
  error: {
    fontSize: 12,
    color: colors.error,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 350,
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 16,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    paddingHorizontal: 8,
    paddingTop: 12,
  },
  monthYearSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.secondary,
    gap: 4,
    minWidth: 100,
    justifyContent: 'center',
  },
  monthYearText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  weekdayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  weekdayText: {
    fontSize: 12,
    fontWeight: '500',
    color: colors.textSecondary,
    width: 40,
    textAlign: 'center',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  dayCell: {
    width: '14.28%',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 2,
  },
  todayCell: {
    backgroundColor: colors.primaryLight,
    borderRadius: 20,
  },
  disabledDay: {
    opacity: 0.4,
  },
  dayText: {
    fontSize: 14,
    color: colors.text,
  },
  disabledDayText: {
    color: colors.textSecondary,
  },
  todayText: {
    fontWeight: '500',
    color: colors.primary,
  },
  modalFooter: {
    marginTop: 16,
    alignItems: 'flex-end',
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: colors.primary,
    fontWeight: '500',
  },
  selectorHeader: {
    marginBottom: 16,
  },
  selectorTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    textAlign: 'center',
  },
  selectorList: {
    maxHeight: 300,
  },
  yearSelectorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  yearItem: {
    width: '33%',
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
  selectedYear: {
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  yearText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedYearText: {
    color: 'white',
    fontWeight: 'bold',
  },
  monthItem: {
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4,
  },
  selectedMonth: {
    backgroundColor: colors.primary,
    borderRadius: 8,
  },
  monthText: {
    fontSize: 16,
    color: colors.text,
  },
  selectedMonthText: {
    color: 'white',
    fontWeight: 'bold',
  },
  calendarIconButton: {
    marginRight: 8,
  },
  calendarIconWrapper: {
    backgroundColor: colors.primaryLight,
    borderRadius: 20,
    padding: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
});