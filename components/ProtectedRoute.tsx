import React, { useEffect, useState, useRef } from 'react';
import { View, ActivityIndicator, Text } from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { colors } from '@/constants/colors';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  console.log('ProtectedRoute: Render');
  const { isAuthenticated, currentUser, checkAuth, isLoading } = useAuthStore();
  const [authCheckComplete, setAuthCheckComplete] = useState(false);
  
  // Use a ref to track if we've already started a navigation
  const isNavigatingRef = useRef(false);
  // Track how many times the component rendered
  const renderCountRef = useRef(0);
  
  renderCountRef.current++;
  
  console.log(`ProtectedRoute: Render #${renderCountRef.current}`, {
    isAuthenticated,
    hasUser: !!currentUser,
    userEmail: currentUser?.email,
    isLoading,
    authCheckComplete,
    isNavigating: isNavigatingRef.current
  });

  // Only run the auth check once when component mounts
  useEffect(() => {
    console.log('ProtectedRoute: Auth check effect running');
    
    // Skip if we've already done this
    if (authCheckComplete) {
      console.log('ProtectedRoute: Auth check already complete, skipping');
      return;
    }
    
    const checkAuthentication = async () => {
      try {
        console.log('ProtectedRoute: Checking authentication...');
        // Only check auth if we're not already authenticated
        if (!isAuthenticated || !currentUser) {
          await checkAuth();
        }
        console.log('ProtectedRoute: Auth check complete', { isAuthenticated, hasUser: !!currentUser });
      } catch (err) {
        console.log('ProtectedRoute: Auth check error', err);
      } finally {
        setAuthCheckComplete(true);
      }
    };

    checkAuthentication();
  }, []);  // Empty dependency array - run only once

  // Handle navigation separately after auth is checked
  useEffect(() => {
    // Skip if navigation is already in progress or auth check isn't complete
    if (isNavigatingRef.current || !authCheckComplete) {
      return;
    }
    
    if (!isLoading && !isAuthenticated && !currentUser) {
      console.log('ProtectedRoute: Not authenticated, navigating to login');
      isNavigatingRef.current = true;
      
      // Use setTimeout to avoid navigation during render
      setTimeout(() => {
        router.replace('/(auth)/login');
      }, 0);
    }
  }, [isLoading, isAuthenticated, currentUser, authCheckComplete]);

  // Show loading state until the auth check is complete
  if (isLoading || !authCheckComplete) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={{ marginTop: 10, color: colors.textSecondary }}>
          {isLoading ? 'Verificando sesión...' : 'Cargando...'}
        </Text>
      </View>
    );
  }

  // If authenticated, render the children
  if (isAuthenticated && currentUser) {
    return <>{children}</>;
  }

  // Show loading while redirect happens
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" color={colors.primary} />
      <Text style={{ marginTop: 10, color: colors.textSecondary }}>
        Redireccionando...
      </Text>
    </View>
  );
};
