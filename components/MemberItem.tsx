import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { UserCheck, UserX, Cake } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { Avatar } from './Avatar';
import { colors } from '@/constants/colors';

interface MemberItemProps {
  kids?: any[];
  parent?: any;
  parents?: any[];
  isReferente: boolean;
  canManage?: boolean;
  onToggleReferente?: () => void;
  onRemove?: () => void;
}

export const MemberItem: React.FC<MemberItemProps> = ({
  kids = [],
  parent,
  parents = [],
  isReferente,
  canManage = false,
  onToggleReferente,
  onRemove,
}) => {
  // Use parents array if provided, otherwise use single parent in an array if available
  const allParents = parents.length > 0 ? parents : (parent ? [parent] : []);
  // Format birth date
  const formatBirthDate = (dateString?: string) => {
    if (!dateString) return 'Fecha no disponible';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
    } catch (e) {
      return 'Fecha inválida';
    }
  };

  // Calculate age
  const calculateAge = (birthDate?: string) => {
    if (!birthDate) return null;

    try {
      const birth = new Date(birthDate);
      const today = new Date();
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }

      return age;
    } catch (e) {
      return null;
    }
  };

  const kidAge = calculateAge(kids[0]?.birthDate);

  // Debug logs removed to prevent excessive console output

  // Safely get initials (use first kid if available, else fallback)
  const getInitials = () => {
    try {
      if (kids.length > 0 && kids[0]?.fullName) {
        return kids[0].fullName.split(' ').map((n: string) => n[0]).join('').substring(0, 2);
      }
      return '??';
    } catch (e) {
      console.error('Error getting initials:', e);
      return '??';
    }
  };

  // Safely get parent names
  const getParentNames = () => {
    if (allParents.length === 0) return 'No disponible';
    try {
      return allParents.map((p, index) => {
        if (p.fullName && typeof p.fullName === 'string') {
          return p.fullName;
        }
        if (p.firstName || p.lastName) {
          return [p.firstName, p.lastName].filter(Boolean).join(' ');
        }
        if (p.email) {
          return p.email.split('@')[0];
        }
        return 'Nombre no disponible';
      }).join(', ');
    } catch (e) {
      console.error('Error getting parent names:', e);
      return 'Error al obtener nombres';
    }
  };

  // Get all kid names as a comma-separated list
  const getKidNames = () => {
    if (!kids || kids.length === 0) return 'Sin hijos en este grupo';
    return kids.map(kid => kid?.fullName || 'Nombre no disponible').join(', ');
  };

  return (
    <Card variant="outlined" style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Avatar
            initials={getInitials()}
            size={40}
            backgroundColor={colors.primaryLight}
          />
          <View style={styles.nameContainer}>
            <Text style={styles.parentName}>{getParentNames()}</Text>
            <Text style={styles.parentNickname}>
              {allParents[0]?.nickname || 'Sin apodo'}
            </Text>
            <Text style={styles.kidName}>
              Familiar de: {getKidNames()}
            </Text>
          </View>
        </View>

        {isReferente && (
          <View style={styles.badgeContainer}>
            <Badge
              label="Referente"
              variant="primary"
              size="small"
            />
          </View>
        )}
      </View>

      {canManage && (
        <View style={styles.actionsContainer}>
          {onToggleReferente && (
            <TouchableOpacity
              style={[styles.actionButton, isReferente ? styles.removeReferenteButton : styles.makeReferenteButton]}
              onPress={onToggleReferente}
            >
              <UserCheck size={16} color={isReferente ? colors.error : colors.success} />
              <Text style={[styles.actionText, isReferente ? styles.removeReferenteText : styles.makeReferenteText]}>
                {isReferente ? 'Quitar referente' : 'Hacer referente'}
              </Text>
            </TouchableOpacity>
          )}

          {onRemove && (
            <TouchableOpacity
              style={[styles.actionButton, styles.removeButton]}
              onPress={onRemove}
            >
              <UserX size={16} color={colors.error} />
              <Text style={[styles.actionText, styles.removeText]}>
                Eliminar
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  nameContainer: {
    flex: 1,
    marginRight: 8,
  },
  parentName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  parentNickname: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  kidName: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 12,
    marginTop: 12,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 4,
    gap: 6,
  },
  makeReferenteButton: {
    backgroundColor: colors.primaryLight,
  },
  removeReferenteButton: {
    backgroundColor: colors.error,
  },
  removeButton: {
    backgroundColor: colors.error,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  makeReferenteText: {
    color: colors.success,
  },
  removeReferenteText: {
    color: colors.error,
  },
  removeText: {
    color: colors.error,
  },
  badgeContainer: {
    marginLeft: 8,
    alignSelf: 'center',
  },
});