import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { DollarSign, Calendar, CheckCircle, XCircle, AlertCircle, Landmark } from 'lucide-react-native';
import { Card } from './Card';
import { colors } from '@/constants/colors';

interface ColectaCardProps {
  motivo: string;
  descripcion?: string;
  fechaFin: string;
  baseAmount: number;
  targetAmount: number;
  totalCollected: number;
  actualContributions: number; // Only use actual contributions count
  status?: 'ACTIVA' | 'FINALIZADA' | 'CANCELADA';
  aliasCbu?: string;
  onPress?: () => void;
}

export const ColectaCard: React.FC<ColectaCardProps> = ({
  motivo,
  descripcion,
  fechaFin,
  baseAmount,
  targetAmount,
  totalCollected,
  actualContributions,
  status = 'ACTIVA',
  aliasCbu,
  onPress,
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Fecha inválida';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-AR', {
      style: 'currency',
      currency: 'ARS',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'FINALIZADA':
        return <CheckCircle size={16} color={colors.success} />;
      case 'CANCELADA':
        return <XCircle size={16} color={colors.error} />;
      default: // ACTIVA
        return <AlertCircle size={16} color={colors.warning} />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'FINALIZADA':
        return 'Finalizada';
      case 'CANCELADA':
        return 'Cancelada';
      default: // ACTIVA
        return 'Activa';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'FINALIZADA':
        return colors.success;
      case 'CANCELADA':
        return colors.error;
      default: // ACTIVA
        return colors.warning;
    }
  };

  const getProgressPercentage = () => {
    if (!targetAmount || targetAmount === 0) return 0;
    const percentage = (totalCollected / targetAmount) * 100;
    return Math.min(percentage, 100);
  };

  return (
    <Card variant="outlined" style={styles.card}>
      <TouchableOpacity
        style={styles.container}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{motivo}</Text>
          <View style={styles.statusContainer}>
            {getStatusIcon()}
            <Text style={[styles.statusText, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
          </View>
        </View>

        {descripcion && (
          <Text style={styles.description} numberOfLines={2}>
            {descripcion}
          </Text>
        )}

        <View style={styles.details}>
          <View style={styles.detailItem}>
            <Calendar size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>
              Fecha límite: {formatDate(fechaFin)}
            </Text>
          </View>

          <View style={styles.detailItem}>
            <DollarSign size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>
              Monto sugerido: {formatCurrency(baseAmount)}
            </Text>
          </View>

          {aliasCbu && (
            <View style={styles.detailItem}>
              <Landmark size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>
                Alias/CBU: {aliasCbu}
              </Text>
            </View>
          )}
        </View>

        {status !== 'DISMISSED' && (
          <View style={styles.progressSection}>
            <Text style={styles.progressText}>
              Recaudado: {formatCurrency(totalCollected)} de {formatCurrency(targetAmount)}
            </Text>

            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${getProgressPercentage()}%` }
                ]}
              />
            </View>

            <Text style={styles.contributionsText}>
              {actualContributions} contribuciones
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
  },
  container: {
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
    marginRight: 8,
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 8,
    lineHeight: 20,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  details: {
    marginBottom: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  progressSection: {
    marginTop: 4,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  contributionsText: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: 'center',
  },
  progressBarContainer: {
    height: 6,
    backgroundColor: colors.secondary,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 3,
  },
});