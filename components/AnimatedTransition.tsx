import React, { ReactNode, useEffect, useRef } from 'react';
import { View, Animated } from 'react-native';

interface AnimatedTransitionProps {
  children: ReactNode;
}

export const AnimatedTransition: React.FC<AnimatedTransitionProps> = ({ children }) => {
  const opacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Fade in animation
    Animated.timing(opacity, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    return () => {
      // Fade out animation on unmount
      Animated.timing(opacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    };
  }, []);

  return (
    <Animated.View style={{ opacity }}>
      {children}
    </Animated.View>
  );
};