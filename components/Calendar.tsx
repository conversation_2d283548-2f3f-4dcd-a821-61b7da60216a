import React, { useState, useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity, Text, ScrollView } from 'react-native';
import { Calendar as RNCalendar } from 'react-native-calendars';
import { useThemeColor } from '@/components/Themed';
import { router } from 'expo-router';

export type EventType = 'birthday' | 'reunion' | 'actividad' | 'feriado' | 'examen' | 'otro' | 'colecta' | 'votacion';

export interface Event {
  id: string;
  title: string;
  date: string;
  type: EventType;
  kidId?: string;
  groupId?: string;
  startTime?: string;
  endTime?: string;
  isFullDay: boolean;
}

interface CalendarProps {
  selectedDate: Date;
  onDateSelect: (date: Date) => void;
  viewMode: 'week' | 'month';
  onViewModeChange: (mode: 'week' | 'month') => void;
  filters: {
    kids: string[];
    groups: string[];
    eventTypes: string[];
  };
  events?: Event[];
}

const EVENT_TYPE_LABELS: Record<EventType, string> = {
  birthday: 'Cumpleaños',
  reunion: 'Reuni<PERSON>',
  actividad: 'Actividad',
  feriado: 'Feriado',
  examen: 'Examen',
  otro: 'Otro',
  colecta: 'Colecta',
  votacion: 'Votación',
};

const EVENT_COLORS: Record<EventType, string> = {
  birthday: '#FF69B4',
  reunion: '#4CAF50',
  actividad: '#2196F3',
  feriado: '#FF9800',
  examen: '#F44336',
  otro: '#9C27B0',
  colecta: '#00BCD4',
  votacion: '#795548',
};

// Event type definitions:
// - birthday, votacion, colecta, feriado: All-day events (date-only, use UTC date part for comparison and marking)
// - reunion, actividad, examen, otro: Date+time events (compare using both date and time)
//
// Why? For all-day events, only the day matters (e.g. a holiday is all of April 11, regardless of time or timezone).
// If you use local time, timezone conversion can shift the event to the previous day for some users.
// Always use the UTC date part for all-day events to avoid off-by-one errors.
//
// For date+time events, compare using the full timestamp (date and time).
//
// See also: https://github.com/your-repo/issues/123 (example issue for reference)

// Helper: get the correct date string for event comparison/marking
const ALL_DAY_TYPES = ['birthday', 'votacion', 'colecta', 'feriado'];
function getEventDateString(event: Event): string {
  const d = new Date(event.date);
  // Always use UTC date part for ALL event types
  const year = d.getUTCFullYear();
  const month = (d.getUTCMonth() + 1).toString().padStart(2, '0');
  const day = d.getUTCDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

export function Calendar({
  selectedDate,
  onDateSelect,
  viewMode,
  onViewModeChange,
  filters,
  events = [],
}: CalendarProps) {
  const backgroundColor = useThemeColor({ light: '#fff', dark: '#000' }, 'background');
  const textColor = useThemeColor({ light: '#000', dark: '#fff' }, 'text');

  const getEventColor = (type: EventType): string => {
    return EVENT_COLORS[type] || '#9E9E9E'; // Default color if type not found
  };
  
  const markedDates = useMemo(() => {
    const marked: { [key: string]: any } = {};
    
    console.log("Building marked dates from events:", events.length);
    if (events.length > 0) {
      console.log("Event types:", events.map(e => `${e.title}: ${e.type}`).join(", "));
      console.log("Event filter types:", filters.eventTypes);
    }
    
    events.forEach(event => {
      try {
        if (!event.date) {
          console.log(`Skipping event with no date: ${event.title}`);
          return;
        }
        
        // Normalize event type for comparison
        const eventType = typeof event.type === 'string' ? event.type.toLowerCase() : event.type;
        const isBirthday = eventType === 'birthday';
        
        // Kid filter: only apply to birthdays
        const kidFilter = isBirthday
          ? (filters.kids.length === 0 || (event.kidId && filters.kids.includes(event.kidId)))
          : true;
        // Group filter: only apply if event has a groupId and filter is not empty
        const groupFilter = event.groupId
          ? (filters.groups.length === 0 || filters.groups.includes(event.groupId))
          : true;
        // Type filter: as before
        const typeFilter = filters.eventTypes.length === 0 || 
                       filters.eventTypes.map(t => t.toLowerCase()).includes(eventType);
        
        console.log(`Marking date check for ${event.title}: kidFilter=${kidFilter}, groupFilter=${groupFilter}, typeFilter=${typeFilter}`);
        console.log(`  -> event date: ${event.date}, type: ${event.type}, normalized type: ${eventType}, groupId: ${event.groupId}, kidId: ${event.kidId}`);
        
        const eventDateStr = getEventDateString(event);
        
        if (kidFilter && groupFilter && typeFilter) {
          // Mark the date - supporting multiple events per day
          console.log(`MARKING DATE for: ${event.title}, date: ${eventDateStr}, type: ${eventType}`);
          
          if (!marked[eventDateStr]) {
            // First event on this date
            marked[eventDateStr] = {
              dots: [{
                key: event.id,
                color: getEventColor(eventType as EventType),
              }],
            };
          } else {
            // Already have events on this date, add another dot
            if (!marked[eventDateStr].dots) {
              // Convert from single mark to dots array if needed
              marked[eventDateStr].dots = [{
                key: 'existing',
                color: marked[eventDateStr].dotColor || '#cccccc',
              }];
            }
            // Add the new event dot, avoiding duplicates
            const hasDotForEvent = marked[eventDateStr].dots.some((dot: any) => dot.key === event.id);
            if (!hasDotForEvent) {
              marked[eventDateStr].dots.push({
                key: event.id,
                color: getEventColor(eventType as EventType),
              });
            }
          }
        } else {
          console.log(`Event filtered out from calendar dots: ${event.title}, type: ${eventType}`);
        }
      } catch (e) {
        console.error(`Error marking date for event ${event.title}:`, e);
      }
    });

    console.log("Final marked dates:", Object.keys(marked));
    return marked;
  }, [events, filters]);

  const selectedDateEvents = useMemo(() => {
    // Use UTC date part for selected date
    const selectedDateStr = selectedDate.toISOString().split('T')[0];
    
    return events.filter(event => {
      // Normalize event type for comparison
      const eventType = typeof event.type === 'string' ? event.type.toLowerCase() : event.type;
      
      // For birthdays, always show them if they're in the selected groups
      const isBirthday = eventType === 'birthday';
      
      // Group filter: only apply if event has a groupId and filter is not empty
      const groupFilter = event.groupId
        ? (filters.groups.length === 0 || filters.groups.includes(event.groupId))
        : true;
      
      // Type filter: check if event type is in selected filters
      const typeFilter = filters.eventTypes.length === 0 || 
                     filters.eventTypes.map(t => t.toLowerCase()).includes(eventType);
      
      // Date filter: check if event date matches selected date
      const dateFilter = event.date === selectedDateStr;
      
      return groupFilter && typeFilter && dateFilter;
    });
  }, [events, selectedDate, filters]);

  const formatTime = (time?: string) => {
    if (!time) return '';
    try {
      const date = new Date(time);
      if (isNaN(date.getTime())) return '';
      return date.toLocaleTimeString('es-ES', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
      });
    } catch (e) {
      console.error('Error formatting time:', e);
      return '';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {viewMode === 'month' ? (
        <RNCalendar
          current={(() => {
            // Use local date string for current
            const y = selectedDate.getFullYear();
            const m = selectedDate.getMonth() + 1;
            const d = selectedDate.getDate();
            return `${y}-${m.toString().padStart(2, '0')}-${d.toString().padStart(2, '0')}`;
          })()}
          onDayPress={(day) => onDateSelect(new Date(day.year, day.month - 1, day.day))}
          markedDates={markedDates}
          markingType="multi-dot"
          theme={{
            backgroundColor,
            calendarBackground: backgroundColor,
            textSectionTitleColor: textColor,
            selectedDayBackgroundColor: '#00adf5',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#00adf5',
            dayTextColor: textColor,
            textDisabledColor: '#d9e1e8',
            monthTextColor: textColor,
            arrowColor: textColor,
          }}
        />
      ) : (
        <RNCalendar
          current={(() => {
            const y = selectedDate.getFullYear();
            const m = selectedDate.getMonth() + 1;
            const d = selectedDate.getDate();
            return `${y}-${m.toString().padStart(2, '0')}-${d.toString().padStart(2, '0')}`;
          })()}
          onDayPress={(day) => onDateSelect(new Date(day.year, day.month - 1, day.day))}
          markedDates={markedDates}
          markingType="multi-dot"
          theme={{
            backgroundColor,
            calendarBackground: backgroundColor,
            textSectionTitleColor: textColor,
            selectedDayBackgroundColor: '#00adf5',
            selectedDayTextColor: '#ffffff',
            todayTextColor: '#00adf5',
            dayTextColor: textColor,
            textDisabledColor: '#d9e1e8',
            monthTextColor: textColor,
            arrowColor: textColor,
          }}
          pastScrollRange={0}
          futureScrollRange={0}
        />
      )}
      <Text style={[styles.eventsTitle, { color: textColor }]}> 
        Eventos para {selectedDate.toLocaleDateString('es-ES', { dateStyle: 'long' })}
      </Text>
      <ScrollView style={styles.eventsContainer} contentContainerStyle={{ flexGrow: 1 }}>
        {selectedDateEvents.length === 0 ? (
          <Text style={[styles.noEventsText, { color: textColor, fontStyle: 'italic', textAlign: 'center' }]}> 
            Sin eventos por ahi ✨
          </Text>
        ) : (
          selectedDateEvents.map((event, index) => {
            // Prepare event title with emoji for feriado
            let eventTitle = event.title;
            if (event.type === 'feriado' && !eventTitle.startsWith('🤩')) {
              eventTitle = `🤩 ${eventTitle}`;
            }
            // Birthday emoji and nickname logic is already handled upstream
            // If event is 'reunion', make the card clickable and navigate to detail
            const isReunion = event.type === 'reunion';
            const CardComponent = isReunion ? TouchableOpacity : View;
            const handlePress = isReunion && event.groupId && event.id
              ? () => router.push(`/group/${event.groupId}/event/${event.id}`)
              : undefined;
            return (
              <CardComponent
                key={`event-${event.id || index}`}
                style={[styles.eventItem, { borderLeftColor: getEventColor(event.type) }]}
                onPress={handlePress}
                activeOpacity={isReunion ? 0.7 : undefined}
              >
                <Text style={[styles.eventType, { color: textColor }]}> 
                  {EVENT_TYPE_LABELS[event.type]}
                </Text>
                <Text style={[styles.eventTitle, { color: textColor }]}> 
                  {eventTitle}
                </Text>
                {!event.isFullDay && (
                  <Text style={[styles.eventTime, { color: textColor }]}> 
                    {event.startTime ? formatTime(event.startTime) : ''} 
                    {event.startTime && event.endTime ? ' - ' : ''}
                    {event.endTime ? formatTime(event.endTime) : ''}
                  </Text>
                )}
              </CardComponent>
            );
          })
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  eventsContainer: {
    marginTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
    maxHeight: 350,
  },
  eventsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
    marginTop: 0,
    textAlign: 'center',
  },
  noEventsText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  eventItem: {
    padding: 12,
    marginBottom: 8,
    borderRadius: 8,
    backgroundColor: '#f8f8f8',
    borderLeftWidth: 4,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  eventType: {
    fontSize: 14,
    marginBottom: 2,
  },
  eventTime: {
    fontSize: 12,
    opacity: 0.7,
  },
}); 