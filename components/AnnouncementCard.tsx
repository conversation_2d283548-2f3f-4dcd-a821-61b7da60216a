import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Calendar, Bell, AlertTriangle } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { colors } from '@/constants/colors';
import { AnnouncementType } from '@/types';

interface AnnouncementCardProps {
  title: string;
  message: string;
  type: AnnouncementType;
  date?: string;
  createdAt: string;
}

export const AnnouncementCard: React.FC<AnnouncementCardProps> = ({
  title,
  message,
  type,
  date,
  createdAt,
}) => {
  const getTypeIcon = () => {
    switch (type) {
      case 'ANNOUNCEMENT':
        return <Bell size={18} color={colors.primary} />;
      case 'REMINDER':
        return <Bell size={18} color={colors.warning} />;
      case 'IMPORTANT_DATE':
        return <Calendar size={18} color={colors.error} />;
      default:
        return <AlertTriangle size={18} color={colors.primary} />;
    }
  };

  const getTypeLabel = () => {
    switch (type) {
      case 'ANNOUNCEMENT':
        return 'Anuncio';
      case 'REMINDER':
        return 'Recordatorio';
      case 'IMPORTANT_DATE':
        return 'Fecha Importante';
      default:
        return 'Anuncio';
    }
  };

  const getTypeVariant = (): 'primary' | 'warning' | 'error' => {
    switch (type) {
      case 'ANNOUNCEMENT':
        return 'primary';
      case 'REMINDER':
        return 'warning';
      case 'IMPORTANT_DATE':
        return 'error';
      default:
        return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  return (
    <Card variant="outlined" style={styles.card}>
      <View style={styles.header}>
        <View style={styles.typeContainer}>
          {getTypeIcon()}
          <Badge 
            label={getTypeLabel()} 
            variant={getTypeVariant()} 
            style={styles.badge}
          />
        </View>
        <Text style={styles.date}>
          {formatDate(createdAt)}
        </Text>
      </View>
      
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.message}>{message}</Text>
      
      {date && type === 'IMPORTANT_DATE' && (
        <View style={styles.dateContainer}>
          <Calendar size={16} color={colors.textSecondary} />
          <Text style={styles.dateText}>{formatDate(date)}</Text>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  typeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  badge: {
    marginLeft: 4,
  },
  date: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  message: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 12,
    lineHeight: 20,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 6,
  },
  dateText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
});