import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { UserCheck, UserX } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { Avatar } from './Avatar';
import { colors } from '@/constants/colors';

interface AdultItemProps {
  parent: any;
  kids: any[];
  isReferente: boolean;
  canManage?: boolean;
  onToggleReferente?: () => void;
  onRemove?: () => void;
}

export const AdultItem: React.FC<AdultItemProps> = ({
  parent,
  kids = [],
  isReferente,
  canManage = false,
  onToggleReferente,
  onRemove,
}) => {
  // Get parent initials
  const getInitials = () => {
    if (!parent?.fullName) return 'U';
    return parent.fullName.split(' ').map((n: string) => n[0]).join('').substring(0, 2);
  };

  // Get kids names
  const getKidNames = () => {
    if (!kids || kids.length === 0) return 'Sin hijos asignados';
    return kids.map(kid => kid.fullName || kid.firstName + ' ' + kid.lastName).join(', ');
  };

  return (
    <Card variant="outlined" style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Avatar
            initials={getInitials()}
            size={40}
            backgroundColor={colors.primaryLight}
          />
          <View style={styles.nameContainer}>
            <View style={styles.nameRow}>
              <Text style={styles.parentName}>
                {parent?.fullName || `${parent?.firstName || ''} ${parent?.lastName || ''}`.trim()}
              </Text>
              {isReferente && (
                <Badge
                  label="Referente"
                  variant="primary"
                  size="small"
                />
              )}
            </View>
            <Text style={styles.kidNames}>
              Hijos: {getKidNames()}
            </Text>
          </View>
        </View>
      </View>

      {canManage && (
        <View style={styles.actionsContainer}>
          {onToggleReferente && (
            <TouchableOpacity
              style={[styles.actionButton, isReferente ? styles.removeReferenteButton : styles.makeReferenteButton]}
              onPress={onToggleReferente}
            >
              <UserCheck size={16} color={isReferente ? colors.error : colors.success} />
              <Text style={[styles.actionText, isReferente ? styles.removeReferenteText : styles.makeReferenteText]}>
                {isReferente ? 'Quitar referente' : 'Hacer referente'}
              </Text>
            </TouchableOpacity>
          )}

          {onRemove && (
            <TouchableOpacity
              style={[styles.actionButton, styles.removeButton]}
              onPress={onRemove}
            >
              <UserX size={16} color={colors.error} />
              <Text style={[styles.actionText, styles.removeText]}>
                Eliminar
              </Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  nameContainer: {
    marginLeft: 12,
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  parentName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    flex: 1,
  },
  kidNames: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  actionsContainer: {
    flexDirection: 'row',
    marginTop: 12,
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 6,
    gap: 4,
  },
  makeReferenteButton: {
    backgroundColor: colors.successLight,
  },
  removeReferenteButton: {
    backgroundColor: colors.errorLight,
  },
  removeButton: {
    backgroundColor: colors.errorLight,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  makeReferenteText: {
    color: colors.success,
  },
  removeReferenteText: {
    color: colors.error,
  },
  removeText: {
    color: colors.error,
  },
});
