import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Card } from './Card';
import { Avatar } from './Avatar';
import { colors } from '@/constants/colors';
import { Kid } from '@/types';

interface KidSelectionItemProps {
  kid: Kid;
  isSelected: boolean;
  onToggle: () => void;
}

export const KidSelectionItem: React.FC<KidSelectionItemProps> = ({
  kid,
  isSelected,
  onToggle,
}) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  return (
    <TouchableOpacity onPress={onToggle} activeOpacity={0.7}>
      <Card 
        variant="outlined" 
        style={[
          styles.card,
          isSelected && styles.selectedCard
        ]}
      >
        <View style={styles.content}>
          <Avatar 
            initials={getInitials(kid.fullName)} 
            size={40} 
            backgroundColor={isSelected ? colors.primary : colors.primaryLight}
          />
          
          <View style={styles.details}>
            <Text style={styles.name}>{kid.fullName}</Text>
            <Text style={styles.info}>DNI: {kid.dni}</Text>
            <Text style={styles.info}>Nacimiento: {formatDate(kid.birthDate)}</Text>
          </View>
          
          <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
            {isSelected && <Ionicons name="checkmark-circle" size={16} color={colors.primary} />}
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    padding: 12,
  },
  selectedCard: {
    borderColor: colors.primary,
    borderWidth: 2,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  details: {
    marginLeft: 12,
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 2,
  },
  info: {
    fontSize: 12,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
});