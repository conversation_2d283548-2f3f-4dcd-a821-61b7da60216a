import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Linking } from 'react-native';
import { Cake, MessageCircle } from 'lucide-react-native';
import { Card } from './Card';
import { Avatar } from './Avatar';
import { colors } from '@/constants/colors';

interface KidItemProps {
  kid: any;
  parents: any[];
}

export const KidItem: React.FC<KidItemProps> = ({
  kid,
  parents = [],
}) => {
  // Get kid initials
  const getInitials = () => {
    if (!kid?.fullName) return 'K';
    return kid.fullName.split(' ').map((n: string) => n[0]).join('').substring(0, 2);
  };

  // Format birth date
  const formatBirthDate = (dateString?: string) => {
    if (!dateString) return 'Fecha no disponible';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      });
    } catch (e) {
      return 'Fecha inválida';
    }
  };

  // Handle WhatsApp link
  const openWhatsApp = (phoneNumber: string, parentName: string) => {
    // Clean phone number (remove spaces, dashes, etc.)
    const cleanPhone = phoneNumber.replace(/[^\d+]/g, '');
    // Add country code if not present (assuming Argentina +54)
    const formattedPhone = cleanPhone.startsWith('+') ? cleanPhone : `+54${cleanPhone}`;
    const message = `Hola ${parentName}, te escribo desde la app Qids.`;
    const whatsappUrl = `whatsapp://send?phone=${formattedPhone}&text=${encodeURIComponent(message)}`;

    Linking.openURL(whatsappUrl).catch(() => {
      // Fallback to web WhatsApp if app is not installed
      const webWhatsappUrl = `https://wa.me/${formattedPhone}?text=${encodeURIComponent(message)}`;
      Linking.openURL(webWhatsappUrl);
    });
  };

  return (
    <Card variant="outlined" style={styles.container}>
      <View style={styles.header}>
        <View style={styles.avatarContainer}>
          <Avatar
            initials={getInitials()}
            size={40}
            backgroundColor={colors.primaryLight}
          />
          <View style={styles.nameContainer}>
            <View style={styles.nameRow}>
              <Text style={styles.kidName}>
                {kid?.fullName || `${kid?.firstName || ''} ${kid?.lastName || ''}`.trim()}
              </Text>
              {kid?.nickname && (
                <Text style={styles.nickname}>
                  "{kid.nickname}"
                </Text>
              )}
            </View>
            <View style={styles.birthDateRow}>
              <Cake size={14} color={colors.textSecondary} />
              <Text style={styles.birthDate}>
                {formatBirthDate(kid?.birthDate)}
              </Text>
            </View>
            <View style={styles.adultsSection}>
              <Text style={styles.adultsTitle}>Adulto responsable:</Text>
              {parents && parents.length > 0 ? (
                parents.map((parent, index) => {
                  const parentName = parent.fullName || `${parent.firstName || ''} ${parent.lastName || ''}`.trim();
                  const hasPhone = parent.phone && parent.phone.trim().length > 0;

                  return (
                    <View key={`parent-${parent.id || index}`} style={styles.adultItem}>
                      <Text style={styles.bulletPoint}>•</Text>
                      <Text style={styles.adultName}>{parentName}</Text>
                      {hasPhone && (
                        <TouchableOpacity
                          style={styles.whatsappButton}
                          onPress={() => openWhatsApp(parent.phone, parentName)}
                        >
                          <MessageCircle size={16} color="#25D366" />
                        </TouchableOpacity>
                      )}
                    </View>
                  );
                })
              ) : (
                <View style={styles.adultItem}>
                  <Text style={styles.bulletPoint}>•</Text>
                  <Text style={styles.noAdults}>Sin adultos asignados</Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  avatarContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flex: 1,
  },
  nameContainer: {
    marginLeft: 12,
    flex: 1,
  },
  nameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 8,
  },
  kidName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
  },
  nickname: {
    fontSize: 14,
    fontStyle: 'italic',
    color: colors.textSecondary,
  },
  birthDateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
    gap: 4,
  },
  birthDate: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  adultsSection: {
    marginTop: 4,
  },
  adultsTitle: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 4,
  },
  adultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  bulletPoint: {
    fontSize: 14,
    color: colors.textSecondary,
    marginRight: 6,
    lineHeight: 20,
  },
  adultName: {
    fontSize: 14,
    color: colors.textSecondary,
    flex: 1,
    lineHeight: 20,
  },
  noAdults: {
    fontSize: 14,
    color: colors.textSecondary,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  whatsappButton: {
    marginLeft: 8,
    padding: 4,
  },
});
