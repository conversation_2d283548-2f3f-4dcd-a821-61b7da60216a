import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Text, Animated } from 'react-native';
import { useThemeColor } from '@/components/Themed';
import { EventType } from '@/components/Calendar';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/store/authStore';
import { Funnel } from 'lucide-react-native';

interface FilterBarProps {
  onFiltersChange: (filters: {
    kids: string[];
    groups: string[];
    eventTypes: string[];
  }) => void;
  selectedFilters: {
    kids: string[];
    groups: string[];
    eventTypes: string[];
  };
  showEventTypeFilters?: boolean;
  showKidsFilter?: boolean;
}

const EVENT_TYPES: EventType[] = [
  'birthday',
  'reunion',
  'actividad',
  'feriado',
  'examen',
  'otro',
  'colecta',
  'votacion',
];

const EVENT_TYPE_LABELS: Record<EventType, string> = {
  birthday: 'Cumpleaños',
  reunion: 'Reunión',
  actividad: 'Actividad',
  feriado: '<PERSON>riado',
  examen: 'Examen',
  otro: 'Otro',
  colecta: 'Colecta',
  votacion: 'Votación',
};

export function FilterBar({ 
  onFiltersChange, 
  selectedFilters, 
  showEventTypeFilters = true,
  showKidsFilter = true
}: FilterBarProps) {
  // Set initial tab to kids if event types are not shown
  const [activeTab, setActiveTab] = useState<'kids' | 'groups' | 'types'>(
    showEventTypeFilters ? 'types' : 'kids'
  );
  const [kids, setKids] = useState<Array<{ id: string; full_name: string }>>([]);
  const [groups, setGroups] = useState<Array<{ id: string; name: string }>>([]);
  const currentUser = useAuthStore(state => state.currentUser);
  const backgroundColor = useThemeColor({ light: '#fff', dark: '#000' }, 'background');
  const textColor = useThemeColor({ light: '#000', dark: '#fff' }, 'text');
  const dataLoaded = useRef(false);

  // Collapsible state for filters
  const [filtersCollapsed, setFiltersCollapsed] = useState(false);
  const [typesCollapsed, setTypesCollapsed] = useState(false);

  // Reset active tab if event types are toggled
  useEffect(() => {
    if (!showEventTypeFilters && activeTab === 'types') {
      setActiveTab('kids');
    }
  }, [showEventTypeFilters, activeTab]);

  // Only load data once when component mounts
  useEffect(() => {
    // Only fetch data once on mount
    if (currentUser && !dataLoaded.current) {
      const loadData = async () => {
        // 1. Obtener los kid_id asociados al usuario actual desde kid_parents
        const { data: kidParentLinks, error: kidParentLinksError } = await supabase
          .from('kid_parents')
          .select('kid_id')
          .eq('parent_id', currentUser.id);
        if (kidParentLinksError) {
          setKids([]);
          setGroups([]);
          return;
        }
        const kidIds = (kidParentLinks || []).map(link => link.kid_id);
        // 2. Obtener los datos de esos niños
        let kidsData: { id: string; full_name: string }[] = [];
        if (kidIds.length > 0) {
          const { data: kidsResult, error: kidsError } = await supabase
            .from('kids')
            .select('id, full_name')
            .in('id', kidIds);
          if (kidsError) {
            console.error('Error fetching kids:', kidsError);
            setKids([]);
            setGroups([]);
            return;
          }
          kidsData = kidsResult || [];
        }
        setKids(kidsData);
        // Obtener grupos del usuario y de los niños
        const { data: userGroups, error: userGroupsError } = await supabase
          .from('group_memberships')
          .select('group_id')
          .eq('user_id', currentUser.id);
        if (userGroupsError) {
          console.error('Error fetching user groups:', userGroupsError);
          setGroups([]);
          return;
        }
        const { data: kidsGroups, error: kidsGroupsError } = await supabase
          .from('group_memberships')
          .select('group_id')
          .in('kid_id', kidIds.length > 0 ? kidIds : ['00000000-0000-0000-0000-000000000000']);
        if (kidsGroupsError) {
          console.error('Error fetching kids groups:', kidsGroupsError);
          setGroups([]);
          return;
        }
        const groupIds = [...new Set([
          ...(userGroups?.map(g => g.group_id) || []),
          ...(kidsGroups?.map(g => g.group_id) || [])
        ])];
        if (groupIds.length > 0) {
          const { data: groupsData, error: groupsError } = await supabase
            .from('groups')
            .select('id, nombre')
            .in('id', groupIds);
          if (groupsError) {
            console.error('Error fetching groups:', groupsError);
            setGroups([]);
          } else {
            setGroups(groupsData ? groupsData.map(g => ({ id: g.id, name: g.nombre })) : []);
          }
        } else {
          setGroups([]);
        }
      };
      
      loadData();
      dataLoaded.current = true;
    }
  }, [currentUser]);

  // Memoize toggleFilter to prevent recreation on render
  const toggleFilter = useCallback((type: 'kids' | 'groups' | 'eventTypes', value: string) => {
    const currentFilters = selectedFilters[type];
    const newFilters = currentFilters.includes(value)
      ? currentFilters.filter(f => f !== value)
      : [...currentFilters, value];

    onFiltersChange({
      ...selectedFilters,
      [type]: newFilters,
    });
  }, [selectedFilters, onFiltersChange]);

  // Memoize the renderFilterChips function to prevent unnecessary recalculation
  const renderFilterChips = useCallback((type: 'kids' | 'groups' | 'eventTypes') => {
    switch (type) {
      case 'eventTypes':
        return EVENT_TYPES.map(type => (
          <TouchableOpacity
            key={type}
            style={[
              styles.chip,
              selectedFilters.eventTypes.includes(type) && styles.selectedChip,
            ]}
            onPress={() => toggleFilter('eventTypes', type)}
          >
            <Text style={[
              styles.chipText,
              { color: textColor },
              selectedFilters.eventTypes.includes(type) && styles.selectedChipText,
            ]}>
              {EVENT_TYPE_LABELS[type]}
            </Text>
          </TouchableOpacity>
        ));
      case 'kids':
        return kids.map((kid, index) => {
          // Only show the first name (nombre), not apellido
          const firstName = kid.full_name.split(' ')[0];
          return (
            <TouchableOpacity
              key={`kid-${kid.id}-${index}`}
              style={[
                styles.chip,
                selectedFilters.kids.includes(kid.id) && styles.selectedChip,
              ]}
              onPress={() => toggleFilter('kids', kid.id)}
            >
              <Text style={[
                styles.chipText,
                { color: textColor },
                selectedFilters.kids.includes(kid.id) && styles.selectedChipText,
              ]}>
                {firstName}
              </Text>
            </TouchableOpacity>
          );
        });
      case 'groups':
        return groups.map(group => (
          <TouchableOpacity
            key={group.id}
            style={[
              styles.chip,
              selectedFilters.groups.includes(group.id) && styles.selectedChip,
            ]}
            onPress={() => toggleFilter('groups', group.id)}
          >
            <Text style={[
              styles.chipText,
              { color: textColor },
              selectedFilters.groups.includes(group.id) && styles.selectedChipText,
            ]}>
              {group.name}
            </Text>
          </TouchableOpacity>
        ));
      default:
        return null;
    }
  }, [kids, groups, selectedFilters, textColor, toggleFilter]);

  // Memoize the chips to prevent re-renders
  const filterChips = useMemo(() => renderFilterChips('kids'), [renderFilterChips]);

  return (
    <View style={[styles.container, { backgroundColor }]}>  
      {/* Groups Filter */}
      <View style={styles.filterBlock}>
        <View style={styles.filterHeaderRow}>
          <Text style={[styles.filterBlockTitle, { color: textColor }]}>Grupos</Text>
          <Funnel size={16} color={textColor} />
          <View style={styles.groupsChipsRow}>
            {renderFilterChips('groups')}
          </View>
        </View>
      </View>

      {/* Event Types Block (optional) */}
      {showEventTypeFilters && (
        <View style={styles.filterBlock}>
          <TouchableOpacity style={styles.filterBlockHeader} onPress={() => setTypesCollapsed(!typesCollapsed)}>
            <Text style={[styles.filterBlockTitle, { color: textColor }]}>Tipos</Text>
            <Text style={styles.chevron}>{typesCollapsed ? '▼' : '▲'}</Text>
          </TouchableOpacity>
          {!typesCollapsed && (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
              {renderFilterChips('eventTypes')}
            </ScrollView>
          )}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 8,
  },
  filterBlock: {
    marginBottom: 6,
    backgroundColor: '#fff',
    overflow: 'hidden',
    padding: 8,
  },
  filterHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flexWrap: 'wrap',
  },
  groupsChipsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 8,
    flexWrap: 'wrap',
  },
  filterBlockHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#f0f4f8',
  },
  filterBlockTitle: {
    fontWeight: 'bold',
    fontSize: 14,
  },
  chevron: {
    fontSize: 16,
  },
  filtersContainer: {
    flexDirection: 'row',
    paddingHorizontal: 4,
    paddingBottom: 2,
  },
  chip: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 4,
  },
  selectedChip: {
    backgroundColor: '#00adf5',
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  selectedChipText: {
    color: '#fff',
  },
}); 