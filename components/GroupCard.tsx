/**
 * GroupCard Component
 *
 * A card component that displays information about a school group.
 * Shows school name, class, year, membership status, and quick stats.
 *
 * Features:
 * - Displays group information (school, class, year, name)
 * - Shows payment status with lock/unlock icon
 * - Displays member and kid counts
 * - Shows badges for referent status and access level
 * - Touchable to navigate to group details
 *
 * @component
 */

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Users, Lock, Unlock, Baby } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { colors } from '@/constants/colors';

interface GroupCardProps {
  /** School name */
  institutionName: string;
  /** Class/room identifier */
  sala: string;
  /** School year */
  año: number;
  /** Optional group name */
  nombre?: string;
  /** Whether the user has paid for access */
  isPaid?: boolean;
  /** Whether the user is a referent (admin) */
  isReferente?: boolean;
  /** Number of members in the group */
  memberCount: number;
  /** Number of kids in the group */
  kidCount: number;
  /** Function to call when card is pressed */
  onPress: () => void;
  /** Additional styles to apply */
  style?: ViewStyle;
}

export const GroupCard: React.FC<GroupCardProps> = ({
  institutionName,
  sala,
  año,
  nombre,
  isPaid = true,
  isReferente = false,
  memberCount,
  kidCount,
  onPress,
  style,
}) => {
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7} style={style}>
      <Card variant="elevated" style={styles.card}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.colegio}>{institutionName}</Text>
            <Text style={styles.sala}>{sala} - {año}</Text>
            {nombre && <Text style={styles.nombre}>{nombre}</Text>}
          </View>
          <View style={styles.statusContainer}>
            {isPaid ? (
              <Unlock size={20} color={colors.success} />
            ) : (
              <Lock size={20} color={colors.warning} />
            )}
          </View>
        </View>

        <View style={styles.footer}>
          <View style={styles.statsContainer}>
            <View style={styles.iconContainer}>
              <Users size={16} color={colors.textSecondary} />
              <Text style={styles.memberCount}>{memberCount} miembros</Text>
            </View>
            <View style={styles.iconContainer}>
              <Baby size={16} color={colors.textSecondary} />
              <Text style={styles.memberCount}>{kidCount} niños</Text>
            </View>
          </View>

          <View style={styles.badgeContainer}>
            {isReferente && (
              <Badge
                label="Referente"
                variant="primary"
                size="small"
              />
            )}
            <Badge
              label={isPaid ? "Acceso Completo" : "Acceso Pendiente"}
              variant={isPaid ? "success" : "warning"}
              size="small"
            />
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    height: 180, // Fixed height for all cards
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
  },
  statusContainer: {
    marginLeft: 8,
  },
  colegio: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  sala: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 2,
  },
  nombre: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  statsContainer: {
    gap: 4,
  },
  iconContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  memberCount: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
  },
});