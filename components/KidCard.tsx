import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Card } from './Card';
import { Avatar } from './Avatar';
import { colors } from '@/constants/colors';
import { AuthService } from '@/services/authService';
import type { User } from '@/types/index';
import type { Group } from '@/types/index';

interface KidCardProps {
  fullName: string;
  name?: string;
  nickname?: string;
  dni: string;
  birthDate: string;
  groupCount?: number;
  parents?: User[];
  groups?: Group[];
  onPress?: () => void;
  style?: ViewStyle;
}

export const KidCard: React.FC<KidCardProps> = ({
  fullName,
  name,
  nickname,
  dni,
  birthDate,
  groupCount = 0,
  parents = [],
  groups = [],
  onPress,
  style,
}) => {
  const [familyMembers, setFamilyMembers] = useState<User[]>([]);
  const [isLoadingFamily, setIsLoadingFamily] = useState(false);

  useEffect(() => {
    const fetchFamilyMembers = async () => {
      if (!parents.length) return;

      setIsLoadingFamily(true);
      try {
        // Get the parent's information
        const { data: users, error } = await AuthService.getUsersByIds(parents.map(parent => parent.id));
        if (error) {
          console.error('Error fetching family members:', error);
          return;
        }
        if (users) {
          setFamilyMembers(users);
        }
      } catch (error) {
        console.error('Error fetching family members:', error);
      } finally {
        setIsLoadingFamily(false);
      }
    };

    fetchFamilyMembers();
  }, [parents]);

  // Use name as fallback for fullName
  const displayName = fullName || name || '';

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const getAge = (birthDateString: string) => {
    const birthDate = new Date(birthDateString);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      onPress={onPress}
      activeOpacity={0.7}
      style={[styles.touchable, style]}
    >
      <Card variant="outlined" style={styles.card}>
        <View style={styles.content}>
          <Avatar
            initials={getInitials(displayName)}
            size={50}
            backgroundColor={colors.primaryLight}
          />

          <View style={styles.details}>
            <Text style={styles.name}>{displayName}</Text>
            {nickname ? (
              <Text style={styles.nickname}>{nickname}</Text>
            ) : null}
            <Text style={styles.info}>DNI: {dni}</Text>
            <Text style={styles.info}>
              {formatDate(birthDate)} ({getAge(birthDate)} años)
            </Text>
            {groupCount > 0 && (
              <Text style={styles.info}>
                {groupCount} {groupCount === 1 ? 'grupo' : 'grupos'}
              </Text>
            )}
            {!isLoadingFamily && familyMembers.length > 0 && (
              <View style={styles.familyContainer}>
                <Text style={styles.familyTitle}>
                  Vinculado con {familyMembers.length} {familyMembers.length === 1 ? 'familiar' : 'familiares'}:
                </Text>
                {familyMembers.map((member, index) => (
                  <Text key={index} style={styles.familyName}>
                    • {member.fullName}
                  </Text>
                ))}
              </View>
            )}
            {groups.length > 0 && (
              <View style={styles.groupsContainer}>
                <Text style={styles.groupsTitle}>
                  Agregado a {groups.length} {groups.length === 1 ? 'grupo' : 'grupos'}:
                </Text>
                {groups.map((group, idx) => (
                  <Text key={group.id} style={styles.groupDetail}>
                    • {group.nombre ? group.nombre + ' - ' : ''}{group.sala} - {group.institutionName} - {group.año}
                  </Text>
                ))}
              </View>
            )}
          </View>
        </View>
      </Card>
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  touchable: {
    marginBottom: 16,
    width: '100%',
  },
  card: {
    padding: 12,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  details: {
    marginLeft: 16,
    flex: 1,
  },
  name: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  nickname: {
    fontSize: 15,
    color: colors.textSecondary,
    fontStyle: 'italic',
    marginBottom: 4,
  },
  info: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  familyContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  familyTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  familyName: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 8,
    marginBottom: 2,
  },
  groupsContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  groupsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 8,
    marginBottom: 2,
  },
});