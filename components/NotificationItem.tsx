import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons, FontAwesome } from '@expo/vector-icons';
import { colors } from '@/constants/colors';
import { NotificationType } from '@/types';

interface NotificationItemProps {
  message: string;
  type: NotificationType;
  createdAt: string;
  isRead: boolean;
  groupName?: string;
  onPress: () => void;
}

export const NotificationItem: React.FC<NotificationItemProps> = ({
  message,
  type,
  createdAt,
  isRead,
  groupName,
  onPress,
}) => {
  const getTypeIcon = () => {
    switch (type) {
      case 'NEW_ANNOUNCEMENT':
        return <Ionicons name="notifications-outline" size={20} color={colors.primary} />;
      case 'NEW_COLECTA':
        return <FontAwesome name="dollar" size={20} color={colors.accent} />;
      case 'PAYMENT_CONFIRMATION':
        return <FontAwesome name="dollar" size={20} color={colors.success} />;
      case 'PAYMENT_REMINDER':
        return <Ionicons name="alert-circle-outline" size={20} color={colors.warning} />;
      default:
        return <Ionicons name="notifications-outline" size={20} color={colors.primary} />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);
    const diffHours = Math.round(diffMs / 3600000);
    const diffDays = Math.round(diffMs / 86400000);
    
    if (diffMins < 60) {
      return `Hace ${diffMins} ${diffMins === 1 ? 'minuto' : 'minutos'}`;
    } else if (diffHours < 24) {
      return `Hace ${diffHours} ${diffHours === 1 ? 'hora' : 'horas'}`;
    } else if (diffDays < 7) {
      return `Hace ${diffDays} ${diffDays === 1 ? 'día' : 'días'}`;
    } else {
      return date.toLocaleDateString('es-ES', {
        day: '2-digit',
        month: 'short',
      });
    }
  };

  return (
    <TouchableOpacity 
      style={[styles.container, isRead && styles.read]} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.iconContainer}>
        {getTypeIcon()}
        {!isRead && <View style={styles.unreadDot} />}
      </View>
      
      <View style={styles.content}>
        <Text style={[styles.message, !isRead && styles.unreadText]}>
          {message}
        </Text>
        
        <View style={styles.footer}>
          <Text style={styles.time}>{formatDate(createdAt)}</Text>
          {groupName && (
            <Text style={styles.groupName}>{groupName}</Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    backgroundColor: colors.secondary,
  },
  read: {
    backgroundColor: colors.background,
  },
  iconContainer: {
    marginRight: 16,
    position: 'relative',
  },
  unreadDot: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.error,
  },
  content: {
    flex: 1,
  },
  message: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
    lineHeight: 20,
  },
  unreadText: {
    fontWeight: '500',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  time: {
    fontSize: 12,
    color: colors.textSecondary,
  },
  groupName: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
});