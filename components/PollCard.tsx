import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { VoteIcon, Clock, CheckCircle, AlertCircle } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { colors } from '@/constants/colors';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { Poll, PollStatus } from '@/types';

interface PollCardProps {
  poll: Poll;
  hasVoted?: boolean;
  onPress: () => void;
}

export const PollCard: React.FC<PollCardProps> = ({
  poll,
  hasVoted = false,
  onPress,
}) => {
  const { title, options, endDate, status } = poll;
  
  // Calculate total votes
  const totalVotes = options.reduce((sum, option) => sum + option.votes, 0);
  
  // Format end date
  const formattedEndDate = formatDistanceToNow(new Date(endDate), { 
    addSuffix: true,
    locale: es 
  });
  
  // Get status info
  const getStatusInfo = (status: PollStatus) => {
    switch (status) {
      case 'DRAFT':
        return {
          icon: <AlertCircle size={16} color={colors.pollDraft} />,
          label: 'Borrador',
          color: colors.pollDraft,
        };
      case 'ACTIVE':
        return {
          icon: <VoteIcon size={16} color={colors.pollActive} />,
          label: 'Activa',
          color: colors.pollActive,
        };
      case 'CLOSED':
        return {
          icon: <CheckCircle size={16} color={colors.pollClosed} />,
          label: 'Cerrada',
          color: colors.pollClosed,
        };
    }
  };
  
  const statusInfo = getStatusInfo(status);
  
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Card variant="elevated" style={styles.card}>
        <View style={styles.header}>
          <View style={styles.statusBadge}>
            {statusInfo.icon}
            <Text style={[styles.statusText, { color: statusInfo.color }]}>
              {statusInfo.label}
            </Text>
          </View>
          
          {hasVoted && status === 'ACTIVE' && (
            <Badge label="Votado" variant="success" size="small" />
          )}
        </View>
        
        <Text style={styles.title}>{title}</Text>
        
        <View style={styles.footer}>
          <View style={styles.statsContainer}>
            <Text style={styles.statsText}>
              {totalVotes} {totalVotes === 1 ? 'voto' : 'votos'}
            </Text>
            
            <View style={styles.dateContainer}>
              <Clock size={14} color={colors.textSecondary} />
              <Text style={styles.dateText}>
                {status === 'CLOSED' ? 'Cerrada' : 'Cierra'} {formattedEndDate}
              </Text>
            </View>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statsContainer: {
    gap: 8,
  },
  statsText: {
    fontSize: 14,
    color: colors.text,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  dateText: {
    fontSize: 12,
    color: colors.textSecondary,
  },
});