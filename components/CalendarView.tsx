import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Bell, DollarSign, Cake } from 'lucide-react-native';
import { Card } from './Card';
import { colors } from '@/constants/colors';

// import { Announcement, Colecta, Kid, Group } from '@/types';

interface CalendarViewProps {
  groupId: string;
  announcements: any[];
  colectas: any[];
  kids: any[];
  groups?: any[];
  events?: any[];
}

interface CalendarEvent {
  id: string;
  title: string;
  date: Date;
  type: 'announcement' | 'colecta' | 'birthday' | 'feriado' | 'holiday';
  color: string;
  groupId?: string;
}

export const CalendarView: React.FC<CalendarViewProps> = ({
  groupId,
  announcements,
  colectas,
  kids,
  groups = [],
  events = [],
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Get current month and year
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  // Get first day of the month and total days in month
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1).getDay();
  const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();

  // Previous and next month navigation
  const goToPreviousMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth - 1, 1));
    setSelectedDate(null);
  };

  const goToNextMonth = () => {
    setCurrentDate(new Date(currentYear, currentMonth + 1, 1));
    setSelectedDate(null);
  };

  // Format date to YYYY-MM-DD for comparison
  const formatDateForComparison = (date: Date): string => {
    try {
      // Check if date is valid before calling toISOString
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        return '';
      }
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error("Invalid date:", date);
      return '';
    }
  };

  // Get group name by ID
  const getGroupName = (groupId?: string): string => {
    if (!groupId) return '';
    const group = groups.find(g => g.id === groupId);
    return group ? (group.nombre || `${group.sala} - ${group.año}`) : '';
  };

  // Get all events for the calendar
  const getEvents = useCallback((): CalendarEvent[] => {
    const calendarEvents: CalendarEvent[] = [];

    // Add announcements with dates
    announcements.forEach(announcement => {
      if (announcement.date) {
        try {
          const announcementDate = new Date(announcement.date);
          if (!isNaN(announcementDate.getTime())) {
            calendarEvents.push({
              id: announcement.id,
              title: announcement.title,
              date: announcementDate,
              type: 'announcement',
              color: colors.primary,
              groupId: announcement.groupId,
            });
          }
        } catch (error) {
          console.error("Invalid announcement date:", announcement.date);
        }
      }
    });

    // Add colectas
    colectas.forEach(colecta => {
      try {
        const colectaDate = new Date(colecta.fechaFin);
        if (!isNaN(colectaDate.getTime())) {
          calendarEvents.push({
            id: colecta.id,
            title: colecta.motivo,
            date: colectaDate,
            type: 'colecta',
            color: colors.warning,
            groupId: colecta.groupId,
          });
        }
      } catch (error) {
        console.error("Invalid colecta date:", colecta.fechaFin);
      }
    });

    // Add events from the events array
    events.forEach(event => {
      try {
        const eventDate = new Date(event.date);
        if (!isNaN(eventDate.getTime())) {
          // Map event.type if available, otherwise skip
          let mappedType: 'announcement' | 'colecta' | 'birthday' | 'feriado' | 'holiday' | undefined = undefined;
          if (event.type === 'feriado' || event.type === 'holiday') mappedType = 'feriado';
          else if (event.type === 'birthday') mappedType = 'birthday';
          else if (event.type === 'announcement') mappedType = 'announcement';
          else if (event.type === 'colecta') mappedType = 'colecta';
          if (mappedType) {
            calendarEvents.push({
              id: event.id,
              title: event.title,
              date: eventDate,
              type: mappedType,
              color: colors.success,
              groupId: event.groupId,
            });
          }
        }
      } catch (error) {
        console.error("Invalid event date:", event.date);
      }
    });

    // Add birthdays
    kids.forEach(kid => {
      if (kid.birthDate) {
        try {
          const birthDate = new Date(kid.birthDate);
          if (!isNaN(birthDate.getTime())) {
            // Set year to current year for annual celebration
            const birthdayThisYear = new Date(
              currentYear,
              birthDate.getMonth(),
              birthDate.getDate()
            );

            calendarEvents.push({
              id: `birthday-${kid.id}`,  // Add prefix to make the ID unique
              title: `Cumpleaños de ${kid.fullName}`,
              date: birthdayThisYear,
              type: 'birthday',
              color: colors.error,
            });
          }
        } catch (error) {
          console.error("Invalid birth date:", kid.birthDate);
        }
      }
    });

    return calendarEvents;
  }, [announcements, colectas, kids, events, currentYear]);

  // Check if a day has events
  const hasEvents = (day: number): boolean => {
    try {
      const dateToCheck = new Date(currentYear, currentMonth, day);
      const dateString = formatDateForComparison(dateToCheck);
      if (!dateString) return false;

      return getEvents().some(event => {
        const eventDateString = formatDateForComparison(event.date);
        return eventDateString && eventDateString === dateString;
      });
    } catch (error) {
      console.error("Error checking events for day:", day, error);
      return false;
    }
  };

  // Get events for selected date
  const getEventsForSelectedDate = (): CalendarEvent[] => {
    if (!selectedDate) return [];

    try {
      const dateString = formatDateForComparison(selectedDate);
      if (!dateString) return [];

      return getEvents().filter(event => {
        const eventDateString = formatDateForComparison(event.date);
        return eventDateString && eventDateString === dateString;
      });
    } catch (error) {
      console.error("Error getting events for selected date:", error);
      return [];
    }
  };

  // Get event icon based on type
  const getEventIcon = (type: 'announcement' | 'colecta' | 'birthday' | 'feriado' | 'holiday') => {
    switch (type) {
      case 'announcement':
        return <Bell size={16} color={colors.primary} />;
      case 'colecta':
        return <DollarSign size={16} color={colors.warning} />;
      case 'birthday':
        return <Cake size={16} color={colors.error} />;
      case 'feriado':
      case 'holiday':
        return <Cake size={16} color={colors.success} />;
    }
  };

  // Render calendar days
  const renderCalendarDays = () => {
    const days = [];
    const today = new Date();
    const isCurrentMonth =
      today.getMonth() === currentMonth &&
      today.getFullYear() === currentYear;

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(
        <View key={`empty-${i}`} style={styles.calendarDay} />
      );
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentYear, currentMonth, day);
      const isToday =
        isCurrentMonth && today.getDate() === day;
      const isSelected =
        selectedDate &&
        selectedDate.getDate() === day &&
        selectedDate.getMonth() === currentMonth &&
        selectedDate.getFullYear() === currentYear;
      const dayHasEvents = hasEvents(day);

      days.push(
        <TouchableOpacity
          key={`day-${day}`}
          style={[
            styles.calendarDay,
            isToday && styles.today,
            isSelected && styles.selectedDay,
          ]}
          onPress={() => setSelectedDate(date)}
        >
          <Text
            style={[
              styles.calendarDayText,
              isToday && styles.todayText,
              isSelected && styles.selectedDayText,
            ]}
          >
            {day}
          </Text>
          {dayHasEvents && <View style={styles.eventIndicator} />}
        </TouchableOpacity>
      );
    }

    return days;
  };

  // Format date for display (DD/MM/YYYY format)
  const formatDate = (date: Date): string => {
    try {
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        return 'Fecha inválida';
      }

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (error) {
      console.error("Error formatting date:", error);
      return 'Fecha inválida';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.calendarHeader}>
        <TouchableOpacity onPress={goToPreviousMonth}>
          <ChevronLeft size={24} color={colors.text} />
        </TouchableOpacity>

        <Text style={styles.monthYearText}>
          {currentDate.toLocaleDateString('es-ES', { month: 'long', year: 'numeric' })}
        </Text>

        <TouchableOpacity onPress={goToNextMonth}>
          <ChevronRight size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <View style={styles.weekdaysContainer}>
        {['D', 'L', 'M', 'M', 'J', 'V', 'S'].map((day, index) => (
          <Text key={`weekday-${index}`} style={styles.weekdayText}>{day}</Text>
        ))}
      </View>

      <View style={styles.calendarGrid}>
        {renderCalendarDays()}
      </View>

      {selectedDate && (
        <View style={styles.eventsContainer}>
          <View style={styles.selectedDateHeader}>
            <CalendarIcon size={18} color={colors.primary} />
            <Text style={styles.selectedDateText}>
              {formatDate(selectedDate)}
            </Text>
          </View>

          <ScrollView style={styles.eventsList}>
            {getEventsForSelectedDate().length > 0 ? (
              getEventsForSelectedDate().map((event, index) => {
                // Determine the label for the event type
                let eventTypeLabel = '';
                let eventTitle = event.title;
                if (event.type === 'announcement') eventTypeLabel = 'Anuncio';
                else if (event.type === 'colecta') eventTypeLabel = 'Colecta';
                else if (event.type === 'birthday') eventTypeLabel = 'Cumpleaños';
                else if (event.type === 'feriado' || event.type === 'holiday') eventTypeLabel = 'Feriado';

                // Add emoji for feriado/holiday
                if (event.type === 'feriado' || event.type === 'holiday') {
                  if (!eventTitle.startsWith('🤩')) {
                    eventTitle = `🤩 ${eventTitle}`;
                  }
                }

                // Birthday emoji and nickname logic is handled in the event title upstream

                return (
                  <Card key={`event-${event.id}`} variant="outlined" style={styles.eventCard}>
                    <View style={styles.eventHeader}>
                      {getEventIcon(event.type)}
                      <Text style={[styles.eventType, { color: event.color }]}> {eventTypeLabel} </Text>
                    </View>
                    <Text style={styles.eventTitle}>{eventTitle}</Text>
                    {event.groupId && (
                      <Text style={styles.eventGroup}>
                        {getGroupName(event.groupId)}
                      </Text>
                    )}
                  </Card>
                );
              })
            ) : (
              <Text style={styles.noEventsText}>
                No hay eventos para esta fecha
              </Text>
            )}
          </ScrollView>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  calendarHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  monthYearText: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    textTransform: 'capitalize',
  },
  weekdaysContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  weekdayText: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: '500',
    color: colors.textSecondary,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    overflow: 'hidden',
  },
  calendarDay: {
    width: '14.28%',
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: colors.border,
  },
  calendarDayText: {
    fontSize: 14,
    color: colors.text,
  },
  today: {
    backgroundColor: colors.secondary,
  },
  todayText: {
    fontWeight: '600',
  },
  selectedDay: {
    backgroundColor: colors.primary,
  },
  selectedDayText: {
    color: colors.background,
    fontWeight: '600',
  },
  eventIndicator: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    position: 'absolute',
    bottom: 6,
  },
  eventsContainer: {
    marginTop: 24,
  },
  selectedDateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  selectedDateText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    textTransform: 'capitalize',
  },
  eventsList: {
    maxHeight: 300,
  },
  eventCard: {
    marginBottom: 8,
    padding: 12,
  },
  eventHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  eventType: {
    fontSize: 14,
    fontWeight: '500',
  },
  eventTitle: {
    fontSize: 16,
    color: colors.text,
    marginBottom: 4,
  },
  eventGroup: {
    fontSize: 12,
    color: colors.textSecondary,
    marginTop: 4,
  },
  noEventsText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: 16,
  },
});