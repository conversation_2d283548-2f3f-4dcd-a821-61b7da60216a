/**
 * DateTimePicker Component
 *
 * A component for selecting both date and time.
 * Extends the functionality of DatePicker to include time selection.
 *
 * @component
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { Calendar, Clock, ChevronDown, ChevronUp } from 'lucide-react-native';
import { colors } from '@/constants/colors';

interface DateTimePickerProps {
  /** Label text displayed above the input */
  label?: string;
  /** Current date value */
  value: Date;
  /** Callback when date changes */
  onChange: (date: Date) => void;
  /** Error message to display */
  error?: string;
  /** Whether the input is disabled */
  disabled?: boolean;
  /** Minimum selectable date */
  minimumDate?: Date;
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  label,
  value,
  onChange,
  error,
  disabled = false,
  minimumDate,
}) => {
  // State for the component
  const [showPicker, setShowPicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(value || new Date());
  const [showDatePicker, setShowDatePicker] = useState(true);
  const [showTimePicker, setShowTimePicker] = useState(false);
  
  // Refs for scrolling to the selected hour and minute
  const hoursScrollViewRef = useRef<ScrollView>(null);
  const minutesScrollViewRef = useRef<ScrollView>(null);

  // Update internal state when value prop changes
  useEffect(() => {
    if (value) {
      const newDate = new Date(value);
      
      // Round minutes to nearest 5-minute interval
      const minutes = newDate.getMinutes();
      const roundedMinutes = Math.round(minutes / 5) * 5 % 60;
      if (minutes !== roundedMinutes) {
        newDate.setMinutes(roundedMinutes);
      }
      
      setSelectedDate(newDate);
    }
  }, [value]);
  
  // When the time picker is shown, scroll to the selected hour and minute
  useEffect(() => {
    if (showTimePicker) {
      // Add a small delay to ensure the ScrollView is rendered
      setTimeout(() => {
        // Scroll to the selected hour
        if (hoursScrollViewRef.current) {
          const hourHeight = 44; // Approximate height of each hour item
          const scrollToHour = selectedDate.getHours() * hourHeight;
          hoursScrollViewRef.current.scrollTo({ y: scrollToHour, animated: true });
        }
        
        // Scroll to the selected minute (nearest 5-minute interval)
        if (minutesScrollViewRef.current) {
          const minuteHeight = 44; // Approximate height of each minute item
          const currentMinutes = selectedDate.getMinutes();
          const nearestFiveMinutes = Math.round(currentMinutes / 5) * 5;
          const scrollToMinute = (nearestFiveMinutes / 5) * minuteHeight;
          minutesScrollViewRef.current.scrollTo({ y: scrollToMinute, animated: true });
        }
      }, 100);
    }
  }, [showTimePicker, selectedDate]);

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: 'long',
      year: 'numeric',
    });
  };

  // Format time for display (HH:MM)
  const formatTime = (date: Date) => {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = roundToNearest5Minutes(date.getMinutes()).toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  };

  // Round minutes to nearest 5-minute interval
  const roundToNearest5Minutes = (minutes: number) => {
    return Math.round(minutes / 5) * 5 % 60;
  };

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    const newDate = new Date(selectedDate);
    newDate.setFullYear(date.getFullYear());
    newDate.setMonth(date.getMonth());
    newDate.setDate(date.getDate());
    setSelectedDate(newDate);
    setShowDatePicker(false);
    setShowTimePicker(true);
  };

  // Handle time selection
  const handleTimeSelect = (hours: number, minutes: number) => {
    const newDate = new Date(selectedDate);
    newDate.setHours(hours);
    newDate.setMinutes(minutes);
    setSelectedDate(newDate);
  };

  // Handle confirmation of time selection
  const handleConfirm = () => {
    // When confirming, update the original value and close the picker
    onChange(selectedDate);
    setShowPicker(false);
  };

  // Generate hours for time picker
  const renderHours = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      hours.push(
        <TouchableOpacity
          key={`hour-${i}`}
          style={[
            styles.timeItem,
            selectedDate.getHours() === i && styles.selectedTimeItem
          ]}
          onPress={() => handleTimeSelect(i, selectedDate.getMinutes())}
        >
          <Text style={[
            styles.timeText,
            selectedDate.getHours() === i && styles.selectedTimeText
          ]}>
            {i.toString().padStart(2, '0')}
          </Text>
        </TouchableOpacity>
      );
    }
    return hours;
  };

  // Generate minutes for time picker
  const renderMinutes = () => {
    const minutes = [];
    const currentMinutes = selectedDate.getMinutes();
    // Round the current minutes to the nearest 5-minute interval
    const nearestFiveMinutes = Math.round(currentMinutes / 5) * 5;
    
    for (let i = 0; i < 60; i += 5) {
      minutes.push(
        <TouchableOpacity
          key={`minute-${i}`}
          style={[
            styles.timeItem,
            // Check against the rounded value for highlighting the selected minute
            (i === nearestFiveMinutes || 
             (i === 0 && nearestFiveMinutes === 60) || 
             (i === 55 && nearestFiveMinutes === 60)) && 
            styles.selectedTimeItem
          ]}
          onPress={() => handleTimeSelect(selectedDate.getHours(), i)}
        >
          <Text style={[
            styles.timeText,
            // Same check for text highlighting
            (i === nearestFiveMinutes || 
             (i === 0 && nearestFiveMinutes === 60) || 
             (i === 55 && nearestFiveMinutes === 60)) && 
            styles.selectedTimeText
          ]}>
            {i.toString().padStart(2, '0')}
          </Text>
        </TouchableOpacity>
      );
    }
    return minutes;
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity
        style={[
          styles.inputContainer,
          error ? styles.inputError : null,
          disabled ? styles.inputDisabled : null
        ]}
        onPress={() => !disabled && setShowPicker(true)}
        disabled={disabled}
      >
        <View style={styles.dateTimeDisplay}>
          <View style={styles.dateSection}>
            <Calendar size={20} color={colors.textSecondary} />
            <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
          </View>
          <View style={styles.timeSection}>
            <Clock size={20} color={colors.textSecondary} />
            <Text style={styles.timeText}>{formatTime(selectedDate)}</Text>
          </View>
        </View>
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={showPicker}
        transparent
        animationType="fade"
        onRequestClose={() => setShowPicker(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setShowPicker(false)}
        >
          <View style={styles.modalContent} onStartShouldSetResponder={() => true}>
            <View style={styles.pickerHeader}>
              <TouchableOpacity
                style={[
                  styles.pickerTab,
                  showDatePicker && styles.activePickerTab
                ]}
                onPress={() => {
                  setShowDatePicker(true);
                  setShowTimePicker(false);
                }}
              >
                <Calendar size={18} color={showDatePicker ? colors.primary : colors.text} />
                <Text style={[
                  styles.pickerTabText,
                  showDatePicker && styles.activePickerTabText
                ]}>Fecha</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.pickerTab,
                  showTimePicker && styles.activePickerTab
                ]}
                onPress={() => {
                  setShowDatePicker(false);
                  setShowTimePicker(true);
                }}
              >
                <Clock size={18} color={showTimePicker ? colors.primary : colors.text} />
                <Text style={[
                  styles.pickerTabText,
                  showTimePicker && styles.activePickerTabText
                ]}>Hora</Text>
              </TouchableOpacity>
            </View>

            {showDatePicker && (
              <View style={styles.datePickerContainer}>
                {/* Use the existing DatePicker component */}
                <DatePicker
                  visible={true}
                  value={selectedDate.toISOString()}
                  date={selectedDate}
                  onConfirm={handleDateSelect}
                  onCancel={() => setShowPicker(false)}
                />
              </View>
            )}

            {showTimePicker && (
              <View style={styles.timePickerContainer}>
                <View style={styles.timePickerHeader}>
                  <Text style={styles.timePickerTitle}>Seleccionar Hora</Text>
                </View>

                <View style={styles.timePickerContent}>
                  <View style={styles.timeColumn}>
                    <Text style={styles.timeColumnHeader}>Hora</Text>
                    <ScrollView style={styles.timeList} ref={hoursScrollViewRef}>
                      {renderHours()}
                    </ScrollView>
                  </View>

                  <View style={styles.timeColumn}>
                    <Text style={styles.timeColumnHeader}>Minuto</Text>
                    <ScrollView style={styles.timeList} ref={minutesScrollViewRef}>
                      {renderMinutes()}
                    </ScrollView>
                  </View>
                </View>

                <View style={styles.timePickerFooter}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowPicker(false)}
                  >
                    <Text style={styles.cancelButtonText}>Cancelar</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={handleConfirm}
                  >
                    <Text style={styles.confirmButtonText}>Confirmar</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  );
};

// Import the DatePicker component for date selection
import { DatePicker } from './DatePicker';

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  inputContainer: {
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    backgroundColor: colors.card,
    padding: 12,
  },
  inputError: {
    borderColor: colors.error,
  },
  inputDisabled: {
    backgroundColor: colors.background,
    borderColor: colors.border,
  },
  dateTimeDisplay: {
    flexDirection: 'column',
    gap: 8,
  },
  dateSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  timeSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    fontSize: 16,
    color: colors.text,
  },
  timeText: {
    fontSize: 16,
    color: colors.text,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxWidth: 400,
    backgroundColor: colors.card,
    borderRadius: 12,
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  pickerHeader: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    justifyContent: 'space-around',
    backgroundColor: colors.background,
  },
  pickerTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    gap: 8,
    marginHorizontal: 4,
  },
  activePickerTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
    backgroundColor: colors.primaryLight,
    borderRadius: 8,
  },
  pickerTabText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
  },
  activePickerTabText: {
    color: colors.primary,
    fontWeight: '600',
  },
  datePickerContainer: {
    // The DatePicker component will handle its own styling
    padding: 8,
  },
  timePickerContainer: {
    padding: 16,
  },
  timePickerHeader: {
    alignItems: 'center',
    marginBottom: 16,
  },
  timePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  timePickerContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  timeColumn: {
    flex: 1,
    alignItems: 'center',
  },
  timeColumnHeader: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  timeList: {
    maxHeight: 200,
  },
  timeItem: {
    paddingVertical: 10,
    paddingHorizontal: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginVertical: 2,
  },
  selectedTimeItem: {
    backgroundColor: colors.primaryLight,
  },
  selectedTimeText: {
    color: colors.primary,
    fontWeight: '600',
  },
  timePickerFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 16,
  },
  cancelButtonText: {
    color: colors.textSecondary,
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  confirmButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
});
