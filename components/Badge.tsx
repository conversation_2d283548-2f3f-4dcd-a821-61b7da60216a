/**
 * Badge Component
 *
 * A versatile badge component that can display counts or labels with different
 * variants and sizes. Used for notifications, status indicators, and labels.
 *
 * Features:
 * - Count badges (circular with numbers)
 * - Label badges (rounded rectangles with text)
 * - Multiple variants (primary, secondary, success, warning, error)
 * - Multiple sizes (small, medium, large)
 * - Auto-formatting for large numbers (99+)
 *
 * @component
 */

import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { colors } from '@/constants/colors';

type BadgeVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error';
type BadgeSize = 'small' | 'medium' | 'large';

interface BadgeProps {
  /** Numeric value to display (for count badges) */
  count?: number;
  /** Text to display (for label badges) */
  label?: string;
  /** Visual style variant */
  variant?: BadgeVariant;
  /** Size of the badge */
  size?: BadgeSize;
  /** Additional styles to apply */
  style?: ViewStyle;
}

export const Badge: React.FC<BadgeProps> = ({
  count,
  label,
  variant = 'primary',
  size = 'small',
  style
}) => {
  // If count is 0 and no label, don't render anything
  if (count === 0 && !label) return null;

  // Determine background color based on variant
  const backgroundColor = colors[variant] || colors.primary;

  // Determine if we're showing a count or a label
  const isCountBadge = count !== undefined;

  // Format count for display (if > 99, show 99+)
  const displayCount = count && count > 99 ? '99+' : count?.toString();

  return (
    <View style={[
      styles.badge,
      isCountBadge ? styles.countBadge : styles.labelBadge,
      styles[`${size}Badge`],
      { backgroundColor },
      style
    ]}>
      <Text style={[
        styles.text,
        styles[`${size}Text`]
      ]}>
        {isCountBadge ? displayCount : label}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  countBadge: {
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    paddingHorizontal: 4,
  },
  labelBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 60,
    alignItems: 'center',
  },
  smallBadge: {
    // Default size
  },
  mediumBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 6,
  },
  largeBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  text: {
    color: '#fff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  smallText: {
    // Default size
  },
  mediumText: {
    fontSize: 12,
  },
  largeText: {
    fontSize: 14,
  },
});