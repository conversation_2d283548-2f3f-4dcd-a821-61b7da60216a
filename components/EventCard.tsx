import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Calendar, MapPin, Clock } from 'lucide-react-native';
import { Card } from './Card';
import { Badge } from './Badge';
import { colors } from '@/constants/colors';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Event, EventType } from '@/types';

interface EventCardProps {
  event: Event;
  onPress: () => void;
}

export const EventCard: React.FC<EventCardProps> = ({
  event,
  onPress,
}) => {
  const { title, description, type, date, location } = event;
  
  // Format date
  const formattedDate = format(new Date(date), 'EEEE d MMMM, yyyy', { locale: es });
  const formattedTime = format(new Date(date), 'HH:mm', { locale: es });
  
  // Get event type info
  const getEventTypeInfo = (type: EventType) => {
    switch (type) {
      case 'MEETING':
        return {
          label: 'Reunión',
          color: colors.eventMeeting,
        };
      case 'ACTIVITY':
        return {
          label: 'Actividad',
          color: colors.eventActivity,
        };
      case 'HOLIDAY':
        return {
          label: 'Feriado',
          color: colors.eventHoliday,
        };
      case 'EXAM':
        return {
          label: 'Examen',
          color: colors.eventExam,
        };
      case 'OTHER':
        return {
          label: 'Otro',
          color: colors.eventOther,
        };
    }
  };
  
  const eventTypeInfo = getEventTypeInfo(type);
  
  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Card variant="elevated" style={styles.card}>
        <View style={styles.header}>
          <Badge 
            label={eventTypeInfo.label} 
            variant="primary" 
            size="small" 
            style={{ backgroundColor: eventTypeInfo.color }}
          />
        </View>
        
        <Text style={styles.title}>{title}</Text>
        
        {description && (
          <Text style={styles.description} numberOfLines={2}>
            {description}
          </Text>
        )}
        
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Calendar size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>{formattedDate}</Text>
          </View>
          
          <View style={styles.detailItem}>
            <Clock size={16} color={colors.textSecondary} />
            <Text style={styles.detailText}>{formattedTime}</Text>
          </View>
          
          {location && (
            <View style={styles.detailItem}>
              <MapPin size={16} color={colors.textSecondary} />
              <Text style={styles.detailText}>{location}</Text>
            </View>
          )}
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    marginBottom: 16,
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
  },
  detailsContainer: {
    gap: 8,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: colors.text,
  },
});