import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { Image } from 'expo-image';
import { colors } from '@/constants/colors';

interface AvatarProps {
  source?: string;
  initials?: string;
  size?: number;
  style?: ViewStyle;
  backgroundColor?: string;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  initials,
  size = 40,
  style,
  backgroundColor = colors.primary,
}) => {
  const avatarStyles = [
    styles.avatar,
    { width: size, height: size, borderRadius: size / 2 },
    !source && { backgroundColor },
    style,
  ];

  const textSize = size * 0.4;

  return (
    <View style={avatarStyles}>
      {source ? (
        <Image
          source={{ uri: source }}
          style={styles.image}
          contentFit="cover"
          transition={200}
        />
      ) : initials ? (
        <Text style={[styles.initials, { fontSize: textSize }]}>{initials}</Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  avatar: {
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  initials: {
    color: colors.background,
    fontWeight: '600',
  },
});