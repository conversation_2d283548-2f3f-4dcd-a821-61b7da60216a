-- Create a function to get users from auth.users
CREATE OR R<PERSON>LACE FUNCTION get_auth_users()
RETURNS TABLE (
  id uuid,
  email text,
  first_name text,
  last_name text
) 
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    au.id,
    au.email,
    COALESCE(au.raw_user_meta_data->>'first_name', 'Unknown') as first_name,
    COALESCE(au.raw_user_meta_data->>'last_name', 'Unknown') as last_name
  FROM auth.users au;
END;
$$ LANGUAGE plpgsql;
