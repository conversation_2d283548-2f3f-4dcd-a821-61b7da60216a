// Test script to debug the groups query
// Run this with: node test_groups_query.js

const { createClient } = require('@supabase/supabase-js');

// You'll need to replace these with your actual Supabase URL and anon key
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testGroupsQuery() {
  console.log('Testing users RLS policy...');

  // Test users table access - should show current user + shared parents
  const { data: users, error: usersError } = await supabase
    .from('users')
    .select('*');

  if (usersError) {
    console.error('Error fetching users:', usersError);
  } else {
    console.log('Users fetched:', users?.length || 0);
    users?.forEach((user, index) => {
      console.log(`User ${index + 1}:`, {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        email: user.email
      });
    });
  }

  console.log('\nTesting kid_parents relationships...');

  // Test kid_parents table to see the relationships
  const { data: kidParents, error: kidParentsError } = await supabase
    .from('kid_parents')
    .select('*');

  if (kidParentsError) {
    console.error('Error fetching kid_parents:', kidParentsError);
  } else {
    console.log('Kid-parent relationships:', kidParents?.length || 0);
    kidParents?.forEach((rel, index) => {
      console.log(`Relationship ${index + 1}:`, rel);
    });
  }

  console.log('\nTesting institutions query...');

  // Test institutions table access
  const { data: institutions, error: instError } = await supabase
    .from('institutions')
    .select('*');

  if (instError) {
    console.error('Error fetching institutions:', instError);
  } else {
    console.log('Institutions fetched:', institutions?.length || 0);
    institutions?.forEach((inst, index) => {
      console.log(`Institution ${index + 1}:`, inst);
    });
  }

  console.log('\nTesting groups query...');

  // Test the exact query from the app
  const { data: allGroupsData, error } = await supabase
    .from('groups')
    .select(`
      id,
      institution_id,
      institutions(
        id,
        name,
        address,
        status
      ),
      sala,
      año,
      nombre,
      created_by,
      created_at
    `)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('Error fetching groups:', error);
    return;
  }

  console.log('Groups fetched:', allGroupsData?.length || 0);

  allGroupsData?.forEach((group, index) => {
    console.log(`Group ${index + 1}:`, {
      id: group.id,
      institution_id: group.institution_id,
      institution_name: group.institutions?.name || 'NO INSTITUTION',
      sala: group.sala,
      año: group.año,
      nombre: group.nombre,
      created_by: group.created_by
    });
  });

  // Test memberships query
  console.log('\nTesting memberships query...');
  const { data: memberships, error: membershipError } = await supabase
    .from('group_memberships')
    .select('group_id, user_id, kid_id');

  if (membershipError) {
    console.error('Error fetching memberships:', membershipError);
    return;
  }

  console.log('Memberships fetched:', memberships?.length || 0);
  memberships?.forEach((membership, index) => {
    console.log(`Membership ${index + 1}:`, membership);
  });
}

testGroupsQuery().catch(console.error);
