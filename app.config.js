module.exports = {
  name: "Qids",
  slug: "qids",
  version: "1.0.2",
  orientation: "portrait",
  icon: "./assets/android/icon.png",
  userInterfaceStyle: "light",
  newArchEnabled: true,
  scheme: "qids",
  splash: {
    image: "./assets/qids-logo-full.png",
    resizeMode: "contain",
    backgroundColor: "#ffffff"
  },
  ios: {
    supportsTablet: true,
    bundleIdentifier: "com.qids.app"
  },
  android: {
    adaptiveIcon: {
      foregroundImage: "./assets/android/adaptive-icon.png",
      backgroundColor: "#ffffff"
    },
    package: "com.qids.app"
  },
  web: {
    favicon: "./assets/favicon.png"
  },
  experiments: {
    tsconfigPaths: true
  },
  plugins: [
    "expo-font",
    "expo-router"
  ],
  extra: {
    supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
    supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
    eas: {
      projectId: "0117e90d-5095-4945-933c-1e44d43e9f7d"
    }
  }
};
