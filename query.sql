-- Count users in the auth.users table
SELECT COUNT(*) AS auth_users_count FROM auth.users;

-- Count users in the public.users table
SELECT COUNT(*) AS public_users_count FROM public.users;

-- Get a list of users with their details (limited to 10)
SELECT 
  au.id, 
  au.email, 
  pu.first_name, 
  pu.last_name, 
  au.created_at
FROM 
  auth.users au
LEFT JOIN 
  public.users pu ON au.id = pu.id
LIMIT 10;
