# Qids Authentication Redirect

This directory contains the `auth-redirect.html` file that is used to handle authentication redirects from Supabase to the Qids mobile app.

## Hosting Instructions

To make the authentication flow work properly, you need to host the `auth-redirect.html` file on a web server. Here are some options:

### Option 1: GitHub Pages

1. Create a new GitHub repository or use an existing one
2. Upload the `auth-redirect.html` file to the repository
3. Enable GitHub Pages in the repository settings
4. Note the URL where the file is hosted (e.g., `https://yourusername.github.io/repository/auth-redirect.html`)

### Option 2: Netlify, Vercel, or other static hosting

1. Upload the `auth-redirect.html` file to your preferred static hosting service
2. Note the URL where the file is hosted

### Option 3: Your own web server

1. Upload the `auth-redirect.html` file to your web server
2. Make sure the file is accessible via HTTPS
3. Note the URL where the file is hosted

## Configuring Supabase

After hosting the file, you need to update the Supabase configuration:

1. Go to the Supabase dashboard
2. Navigate to Authentication > URL Configuration
3. Set the Site URL to the base URL of your hosted file (e.g., `https://yourusername.github.io/repository`)
4. Add `qids://**` to the Redirect URLs
5. Save the changes

## How It Works

The `auth-redirect.html` file acts as a bridge between Supabase's web-based authentication flow and the Qids mobile app. When a user clicks on an authentication link (like password reset or email confirmation), they are first directed to this web page, which then redirects them to the mobile app using the `qids://` URL scheme.

The file handles:
- Password reset links
- Email confirmation links
- Magic links
- Email change confirmation links
- Invitation links

## Troubleshooting

If you're having issues with the authentication flow:

1. Make sure the `auth-redirect.html` file is properly hosted and accessible
2. Verify that the Supabase configuration is correct
3. Check that the URL scheme `qids://` is properly registered in your mobile app
4. Test the redirect by manually navigating to your hosted `auth-redirect.html` file with a test hash fragment
