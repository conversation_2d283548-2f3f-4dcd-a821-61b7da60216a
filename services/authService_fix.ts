// Modified AuthService.signUp function to fix the registration issue

/**
 * Sign up a new user with direct user creation
 */
signUp: async (data: SignUpData): Promise<User> => {
  const { email, password, firstName, lastName, nickname, phone } = data;

  // Ensure email is a string
  const emailStr = typeof email === 'string' ? email : String(email);
  const passwordStr = typeof password === 'string' ? password : String(password);
  const firstNameStr = typeof firstName === 'string' ? firstName : String(firstName);
  const lastNameStr = typeof lastName === 'string' ? lastName : String(lastName);
  const nicknameStr = nickname ? (typeof nickname === 'string' ? nickname : String(nickname)) : undefined;
  const phoneStr = phone ? (typeof phone === 'string' ? phone : String(phone)) : undefined;

  console.log('Signing up with email:', emailStr);

  try {
    // First, sign up the user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: emailStr,
      password: passwordStr,
      options: {
        data: {
          first_name: firstNameStr,
          last_name: lastNameStr,
          nickname: nicknameStr,
          phone: phoneStr,
        },
        emailRedirectTo: 'parentium://confirm-email',
      },
    });

    if (authError) {
      console.error('Auth signup error:', authError);
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error('No user data returned from signup');
    }

    // Now, manually create the user record in the public.users table
    // This is a fallback in case the database trigger doesn't work
    try {
      console.log('Manually creating user record in public.users table');
      const { error: insertError } = await supabase
        .from('users')
        .insert([
          {
            id: authData.user.id,
            email: emailStr,
            first_name: firstNameStr,
            last_name: lastNameStr,
            nickname: nicknameStr,
            phone: phoneStr,
          },
        ])
        .select()
        .single();

      if (insertError) {
        // If the error is because the user already exists, that's fine
        if (insertError.code === '23505') { // Unique violation
          console.log('User already exists in public.users table, continuing');
        } else {
          console.warn('Error creating user record:', insertError);
          // We'll continue anyway since the auth user was created
        }
      } else {
        console.log('User record created successfully');
      }
    } catch (dbError) {
      console.warn('Exception creating user record:', dbError);
      // Continue anyway since the auth user was created
    }

    // Return user data
    return {
      id: authData.user.id,
      email: authData.user.email || '',
      firstName: firstNameStr,
      lastName: lastNameStr,
      nickname: nicknameStr,
      fullName: `${firstNameStr} ${lastNameStr}`,
      phone: phoneStr,
      createdAt: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Signup process error:', error);
    throw error;
  }
},
