import { supabase } from '@/lib/supabase';
import { Event } from '@/types';

export interface EventData {
  title: string;
  description?: string;
  type: string;
  date: string;
  endDate?: string;
  location?: string;
}

export const EventsService = {
  /**
   * Get events for a group
   */
  getEventsByGroupId: async (groupId: string): Promise<Event[]> => {
    const { data, error } = await supabase
      .from('events')
      .select(`
        id,
        group_id,
        title,
        description,
        type,
        date,
        end_date,
        location,
        created_by,
        created_at
      `)
      .eq('group_id', groupId)
      .order('date', { ascending: true });

    if (error) {
      throw new Error(error.message);
    }

    // Map the data to the Event type
    return data.map(event => ({
      id: event.id,
      groupId: event.group_id,
      title: event.title,
      description: event.description,
      type: event.type,
      date: event.date,
      endDate: event.end_date,
      location: event.location,
      createdBy: event.created_by,
      createdAt: event.created_at,
    }));
  },

  /**
   * Get an event by ID
   */
  getEventById: async (eventId: string): Promise<Event | null> => {
    const { data, error } = await supabase
      .from('events')
      .select(`
        id,
        group_id,
        title,
        description,
        type,
        date,
        end_date,
        location,
        created_by,
        created_at
      `)
      .eq('id', eventId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Event not found
      }
      throw new Error(error.message);
    }

    // Map the data to the Event type
    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      description: data.description,
      type: data.type,
      date: data.date,
      endDate: data.end_date,
      location: data.location,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Create a new event
   */
  createEvent: async (
    groupId: string,
    userId: string,
    eventData: EventData
  ): Promise<Event> => {
    const { title, description, type, date, endDate, location } = eventData;

    // Insert new event
    const { data, error } = await supabase
      .from('events')
      .insert({
        group_id: groupId,
        title,
        description,
        type,
        date,
        end_date: endDate,
        location,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the new event
    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      description: data.description,
      type: data.type,
      date: data.date,
      endDate: data.end_date,
      location: data.location,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Update an event
   */
  updateEvent: async (
    eventId: string,
    eventData: EventData
  ): Promise<Event> => {
    const { title, description, type, date, endDate, location } = eventData;

    // Update event
    const { data, error } = await supabase
      .from('events')
      .update({
        title,
        description,
        type,
        date,
        end_date: endDate,
        location,
      })
      .eq('id', eventId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the updated event
    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      description: data.description,
      type: data.type,
      date: data.date,
      endDate: data.end_date,
      location: data.location,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Delete an event
   */
  deleteEvent: async (eventId: string): Promise<void> => {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', eventId);

    if (error) {
      throw new Error(error.message);
    }
  },
};
