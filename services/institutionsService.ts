import { supabase } from '@/lib/supabase';
import { Institution } from '@/types';

export interface InstitutionData {
  name: string;
  address?: string;
  status?: 'active' | 'inactive';
}

export const InstitutionsService = {
  /**
   * Get all institutions
   */
  getInstitutions: async (): Promise<Institution[]> => {
    const { data, error } = await supabase
      .from('institutions')
      .select('*')
      .eq('status', 'active')
      .order('name');

    if (error) {
      throw new Error(error.message);
    }

    return data.map(institution => ({
      id: institution.id,
      name: institution.name,
      address: institution.address,
      status: institution.status,
      createdAt: institution.created_at,
      updatedAt: institution.updated_at,
    }));
  },

  /**
   * Get institution by ID
   */
  getInstitutionById: async (id: string): Promise<Institution | null> => {
    const { data, error } = await supabase
      .from('institutions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Institution not found
      }
      throw new Error(error.message);
    }

    return {
      id: data.id,
      name: data.name,
      address: data.address,
      status: data.status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Search institutions by name
   */
  searchInstitutions: async (query: string): Promise<Institution[]> => {
    const { data, error } = await supabase
      .from('institutions')
      .select('*')
      .ilike('name', `%${query}%`)
      .eq('status', 'active')
      .order('name')
      .limit(10);

    if (error) {
      throw new Error(error.message);
    }

    return data.map(institution => ({
      id: institution.id,
      name: institution.name,
      address: institution.address,
      status: institution.status,
      createdAt: institution.created_at,
      updatedAt: institution.updated_at,
    }));
  },

  /**
   * Create a new institution
   */
  createInstitution: async (institutionData: InstitutionData): Promise<Institution> => {
    const { data, error } = await supabase
      .from('institutions')
      .insert({
        name: institutionData.name,
        address: institutionData.address,
        status: institutionData.status || 'active',
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return {
      id: data.id,
      name: data.name,
      address: data.address,
      status: data.status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Update an institution
   */
  updateInstitution: async (id: string, institutionData: Partial<InstitutionData>): Promise<Institution> => {
    const { data, error } = await supabase
      .from('institutions')
      .update({
        name: institutionData.name,
        address: institutionData.address,
        status: institutionData.status,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    return {
      id: data.id,
      name: data.name,
      address: data.address,
      status: data.status,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    };
  },

  /**
   * Get or create institution by name
   * This is useful when creating groups with new institution names
   */
  getOrCreateInstitution: async (name: string, address?: string): Promise<Institution> => {
    try {
      // First try to find existing institution
      const { data: existing, error: searchError } = await supabase
        .from('institutions')
        .select('*')
        .eq('name', name)
        .single();

      if (existing && !searchError) {
        return {
          id: existing.id,
          name: existing.name,
          address: existing.address,
          status: existing.status,
          createdAt: existing.created_at,
          updatedAt: existing.updated_at,
        };
      }

      // If not found, create new institution
      return await InstitutionsService.createInstitution({ name, address });
    } catch (error) {
      // If institutions table doesn't exist yet, return a mock institution
      console.warn('Institutions table not found, using fallback. Please run the migration.');
      return {
        id: 'temp-institution-id',
        name: name,
        address: address,
        status: 'active' as const,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }
  },
};
