import { supabase } from '@/lib/supabase';
import { Poll, PollOption, PollVote } from '@/types';

export interface PollData {
  title: string;
  description?: string;
  endDate?: string;
  options: string[];
  allowMultipleChoice?: boolean;
}

export const PollsService = {
  /**
   * Get polls for a group
   */
  getPollsByGroupId: async (groupId: string): Promise<Poll[]> => {
    const { data, error } = await supabase
      .from('polls')
      .select(`
        id,
        group_id,
        title,
        description,
        end_date,
        status,
        allow_multiple_choice,
        created_by,
        created_at
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Get poll options for each poll
    const pollIds = data.map(poll => poll.id);
    const { data: optionsData, error: optionsError } = await supabase
      .from('poll_options')
      .select('id, poll_id, text, votes')
      .in('poll_id', pollIds);

    if (optionsError) {
      throw new Error(optionsError.message);
    }

    // Map the data to the Poll type
    return data.map(poll => {
      // Get options for this poll
      const options = optionsData
        ? optionsData
            .filter(option => option.poll_id === poll.id)
            .map(option => ({
              id: option.id,
              pollId: option.poll_id,
              text: option.text,
              votes: option.votes,
            }))
        : [];

      return {
        id: poll.id,
        groupId: poll.group_id,
        title: poll.title,
        description: poll.description,
        endDate: poll.end_date,
        status: poll.status,
        allowMultipleChoice: poll.allow_multiple_choice || false,
        createdBy: poll.created_by,
        createdAt: poll.created_at,
        options,
      };
    });
  },

  /**
   * Get a poll by ID
   */
  getPollById: async (pollId: string): Promise<Poll | null> => {
    const { data, error } = await supabase
      .from('polls')
      .select(`
        id,
        group_id,
        title,
        description,
        end_date,
        status,
        allow_multiple_choice,
        created_by,
        created_at
      `)
      .eq('id', pollId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Poll not found
      }
      throw new Error(error.message);
    }

    // Get poll options
    const { data: optionsData, error: optionsError } = await supabase
      .from('poll_options')
      .select('id, poll_id, text, votes')
      .eq('poll_id', pollId);

    if (optionsError) {
      throw new Error(optionsError.message);
    }

    // Map the data to the Poll type
    const options = optionsData.map(option => ({
      id: option.id,
      pollId: option.poll_id,
      text: option.text,
      votes: option.votes,
    }));

    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      description: data.description,
      endDate: data.end_date,
      status: data.status,
      allowMultipleChoice: data.allow_multiple_choice || false,
      createdBy: data.created_by,
      createdAt: data.created_at,
      options,
    };
  },

  /**
   * Create a new poll
   */
  createPoll: async (
    groupId: string,
    userId: string,
    pollData: PollData
  ): Promise<Poll> => {
    const { title, description, endDate, options, allowMultipleChoice } = pollData;

    // Start a transaction
    const { data, error } = await supabase.rpc('create_poll', {
      p_group_id: groupId,
      p_title: title,
      p_created_by: userId,
      p_options: options,
      p_description: description,
      p_end_date: endDate,
      p_allow_multiple_choice: allowMultipleChoice || false,
    });

    if (error) {
      throw new Error(error.message);
    }

    // Get the created poll
    return PollsService.getPollById(data.poll_id);
  },

  /**
   * Update a poll's status
   */
  updatePollStatus: async (pollId: string, status: string): Promise<void> => {
    const { error } = await supabase
      .from('polls')
      .update({ status })
      .eq('id', pollId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Get votes for multiple polls
   */
  getVotesByPollIds: async (pollIds: string[]): Promise<PollVote[]> => {
    if (pollIds.length === 0) {
      return [];
    }

    console.log('Fetching votes for polls:', pollIds);

    try {
      const { data, error } = await supabase
        .from('votes') // Changed from poll_votes to votes to match the table name in the SQL script
        .select(`
          id,
          poll_id,
          option_id,
          user_id,
          created_at
        `)
        .in('poll_id', pollIds);

      if (error) {
        console.error('Error fetching votes:', error);
        throw new Error(error.message);
      }

      console.log(`Fetched ${data?.length || 0} votes`);

      // Map the data to the PollVote type
      return (data || []).map(vote => ({
        id: vote.id,
        pollId: vote.poll_id,
        optionId: vote.option_id,
        userId: vote.user_id,
        createdAt: vote.created_at,
      }));
    } catch (error) {
      console.error('Error in getVotesByPollIds:', error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  },

  /**
   * Get votes for a poll
   */
  getVotesByPollId: async (pollId: string): Promise<PollVote[]> => {
    try {
      const { data, error } = await supabase
        .from('votes') // Changed from poll_votes to votes to match the table name in the SQL script
        .select(`
          id,
          poll_id,
          option_id,
          user_id,
          created_at
        `)
        .eq('poll_id', pollId);

      if (error) {
        console.error('Error fetching votes for poll:', error);
        throw new Error(error.message);
      }

      console.log(`Fetched ${data?.length || 0} votes for poll ${pollId}`);

      // Map the data to the PollVote type
      return (data || []).map(vote => ({
        id: vote.id,
        pollId: vote.poll_id,
        optionId: vote.option_id,
        userId: vote.user_id,
        createdAt: vote.created_at,
      }));
    } catch (error) {
      console.error('Error in getVotesByPollId:', error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  },

  /**
   * Get a user's vote for a poll
   */
  getUserVote: async (pollId: string, userId: string): Promise<PollVote | null> => {
    const { data, error } = await supabase
      .from('votes') // Changed from poll_votes to votes to match the table name in the SQL script
      .select(`
        id,
        poll_id,
        option_id,
        user_id,
        created_at
      `)
      .eq('poll_id', pollId)
      .eq('user_id', userId)
      .maybeSingle();

    if (error) {
      throw new Error(error.message);
    }

    if (!data) {
      return null;
    }

    // Map the data to the PollVote type
    return {
      id: data.id,
      pollId: data.poll_id,
      optionId: data.option_id,
      userId: data.user_id,
      createdAt: data.created_at,
    };
  },

  /**
   * Vote on a poll
   */
  voteOnPoll: async (
    pollId: string,
    optionId: string,
    userId: string
  ): Promise<PollVote> => {
    // Check if user has already voted
    const existingVote = await PollsService.getUserVote(pollId, userId);
    if (existingVote) {
      throw new Error('Ya has votado en esta encuesta');
    }

    // Insert new vote
    const { data, error } = await supabase
      .from('votes') // Changed from poll_votes to votes to match the table name in the SQL script
      .insert({
        poll_id: pollId,
        option_id: optionId,
        user_id: userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the new vote
    return {
      id: data.id,
      pollId: data.poll_id,
      optionId: data.option_id,
      userId: data.user_id,
      createdAt: data.created_at,
    };
  },
};
