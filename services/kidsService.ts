import { supabase } from '@/lib/supabase';
import type { Kid } from '@/types/index';

// Cache for schema check to avoid repeated queries
let schemaChecked = false;
let useFullNameCache: boolean | null = null;

/**
 * Check if the database schema has been updated
 * This function is cached after the first call
 */
async function checkSchema(): Promise<boolean> {
  if (schemaChecked) {
    console.log('Using cached schema check result:', useFullNameCache);
    return useFullNameCache as boolean;
  }

  console.log('Performing schema check...');
  try {
    // Instead of making a database query, assume we're using the new schema
    // since we've already migrated the database
    const useFullName = false;

    // Cache the result
    useFullNameCache = useFullName;
    schemaChecked = true;
    console.log('Schema check completed and cached:', useFullName);
    return useFullName;
  } catch (error) {
    console.error('Error checking schema:', error);
    // Default to new schema if check fails
    return false;
  }
}

export interface KidData {
  dni: string;
  firstName: string;
  lastName: string;
  nickname?: string;
  birthDate: string;
}

export const KidsService = {
  /**
   * Get all kids for the current user (ahora usando kid_parents)
   */
  getKids: async (parentId: string): Promise<Kid[]> => {
    try {
      // 1. Buscar todos los kid_id asociados al parentId en kid_parents
      const { data: kidParentLinks, error: linkError } = await supabase
        .from('kid_parents')
        .select('kid_id')
        .eq('parent_id', parentId);
      if (linkError) {
        throw new Error(linkError.message);
      }
      if (!kidParentLinks || kidParentLinks.length === 0) {
        return [];
      }
      const kidIds = kidParentLinks.map((link: any) => link.kid_id);
      // 2. Traer los datos de esos niños desde kids
      const { data, error } = await supabase
        .from('kids')
        .select('*')
        .in('id', kidIds);
      if (error) {
        throw new Error(error.message);
      }
      if (!data || data.length === 0) {
        return [];
      }
      // 3. Obtener memberships de grupos
      const { data: memberships, error: membershipError } = await supabase
        .from('group_memberships')
        .select('kid_id, group_id')
        .in('kid_id', kidIds);
      if (membershipError) {
        throw new Error(membershipError.message);
      }
      // 4. Mapear los datos al tipo Kid
      return data.map((kid: any) => {
        const kidGroups = memberships
          ? memberships.filter(m => m.kid_id === kid.id).map(m => m.group_id)
          : [];
        return {
          id: kid.id,
          firstName: kid.first_name,
          lastName: kid.last_name,
          nickname: kid.nickname,
          fullName: kid.full_name,
          birthDate: kid.birth_date,
          dni: kid.dni,
          groups: kidGroups,
          createdAt: kid.created_at,
          parentId: '',
        };
      });
    } catch (error) {
      console.error('Error in getKids:', error);
      throw error;
    }
  },

  /**
   * Add a new kid
   */
  addKid: async (parentId: string, kidData: KidData): Promise<Kid> => {
    const { dni, firstName, lastName, nickname, birthDate } = kidData;

    // Check if the database schema has been updated
    const useFullName = await checkSchema();

    // Check if DNI already exists
    const { data: existingKid, error: checkError } = await supabase
      .from('kids')
      .select('id')
      .eq('dni', dni)
      .maybeSingle();

    if (checkError) {
      throw new Error(checkError.message);
    }

    if (existingKid) {
      throw new Error('Ya existe un niño con este DNI');
    }

    // Insert new kid (sin parent_id)
    let insertData;
    if (useFullName) {
      insertData = {
        full_name: `${firstName} ${lastName}`,
        birth_date: birthDate,
        dni,
      };
    } else {
      insertData = {
        first_name: firstName,
        last_name: lastName,
        nickname: nickname,
        birth_date: birthDate,
        dni,
      };
    }

    const { data, error } = await supabase
      .from('kids')
      .insert(insertData)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Crear la relación en kid_parents
    const { error: linkError } = await supabase
      .from('kid_parents')
      .insert({ kid_id: data.id, parent_id: parentId });
    if (linkError) {
      throw new Error(linkError.message);
    }

    // Return the new kid (solo un parentId: '')
    if (useFullName) {
      const nameParts = data.full_name.split(' ');
      const firstNamePart = nameParts[0] || '';
      const lastNamePart = nameParts.slice(1).join(' ') || '';
      return {
        id: data.id,
        firstName: firstNamePart,
        lastName: lastNamePart,
        nickname: '',
        fullName: data.full_name,
        birthDate: data.birth_date,
        dni: data.dni,
        groups: [],
        createdAt: data.created_at,
        parentId: '',
      };
    } else {
      return {
        id: data.id,
        firstName: data.first_name,
        lastName: data.last_name,
        nickname: data.nickname,
        fullName: data.full_name,
        birthDate: data.birth_date,
        dni: data.dni,
        groups: [],
        createdAt: data.created_at,
        parentId: '',
      };
    }
  },

  /**
   * Update a kid
   */
  updateKid: async (
    kidId: string,
    kidData: KidData
  ): Promise<Kid> => {
    const { dni, firstName, lastName, nickname, birthDate } = kidData;

    // Check if the database schema has been updated
    const useFullName = await checkSchema();

    // Check if DNI already exists (excluding this kid)
    const { data: existingKid, error: checkError } = await supabase
      .from('kids')
      .select('id')
      .eq('dni', dni)
      .neq('id', kidId)
      .maybeSingle();

    if (checkError) {
      throw new Error(checkError.message);
    }

    if (existingKid) {
      throw new Error('Ya existe un niño con este DNI');
    }

    // Update kid
    let updateData;
    let selectQuery;

    if (useFullName) {
      // Old schema version - use full_name
      updateData = {
        full_name: `${firstName} ${lastName}`,
        birth_date: birthDate,
        dni,
      };
      selectQuery = `
        id,
        full_name,
        birth_date,
        dni,
        parent_id,
        created_at
      `;
    } else {
      // New schema version - use first_name, last_name, nickname
      updateData = {
        first_name: firstName,
        last_name: lastName,
        nickname: nickname,
        birth_date: birthDate,
        dni,
      };
      selectQuery = `
        id,
        first_name,
        last_name,
        nickname,
        full_name,
        birth_date,
        dni,
        parent_id,
        created_at
      `;
    }

    const { data, error } = await supabase
      .from('kids')
      .update(updateData)
      .eq('id', kidId)
      .select(selectQuery)
      .single();

    // Type assertion to help TypeScript understand the data structure
    const kidResult = data as any;

    if (error) {
      throw new Error(error.message);
    }

    // Get groups for this kid
    const { data: memberships, error: membershipError } = await supabase
      .from('group_memberships')
      .select('group_id')
      .eq('kid_id', kidId);

    if (membershipError) {
      throw new Error(membershipError.message);
    }

    // Return the updated kid
    if (useFullName) {
      // Old schema version - derive firstName and lastName from full_name
      const nameParts = (kidResult.full_name || '').split(' ');
      const firstNamePart = nameParts[0] || '';
      const lastNamePart = nameParts.slice(1).join(' ') || '';

      return {
        id: kidResult.id,
        firstName: firstNamePart,
        lastName: lastNamePart,
        nickname: '',
        fullName: kidResult.full_name,
        birthDate: kidResult.birth_date,
        dni: kidResult.dni,
        parentId: '',
        groups: memberships ? memberships.map(m => m.group_id) : [],
        createdAt: kidResult.created_at,
        parentId: '',
      };
    } else {
      // New schema version
      return {
        id: kidResult.id,
        firstName: kidResult.first_name,
        lastName: kidResult.last_name,
        nickname: kidResult.nickname,
        fullName: kidResult.full_name,
        birthDate: kidResult.birth_date,
        dni: kidResult.dni,
        parentId: '',
        groups: memberships ? memberships.map(m => m.group_id) : [],
        createdAt: kidResult.created_at,
        parentId: '',
      };
    }
  },

  /**
   * Delete a kid
   */
  deleteKid: async (kidId: string): Promise<void> => {
    // First delete all group memberships
    const { error: membershipError } = await supabase
      .from('group_memberships')
      .delete()
      .eq('kid_id', kidId);

    if (membershipError) {
      throw new Error(membershipError.message);
    }

    // Then delete the kid
    const { error } = await supabase
      .from('kids')
      .delete()
      .eq('id', kidId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Add a kid to a group
   */
  addKidToGroup: async (kidId: string, groupId: string, userId: string): Promise<void> => {
    const { error } = await supabase
      .from('group_memberships')
      .insert({
        group_id: groupId,
        kid_id: kidId,
        user_id: userId,
      });

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Remove a kid from a group
   */
  removeKidFromGroup: async (kidId: string, groupId: string): Promise<void> => {
    const { error } = await supabase
      .from('group_memberships')
      .delete()
      .eq('kid_id', kidId)
      .eq('group_id', groupId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Get kids by their IDs (for group members)
   * This function fetches kids regardless of who their parent is
   */
  getKidsByIds: async (kidIds: string[]): Promise<Kid[]> => {
    try {
      if (!kidIds || kidIds.length === 0) {
        return [];
      }

      // Check if the database schema has been updated
      const useFullName = await checkSchema();

      let query;
      if (useFullName) {
        // Old schema version
        query = supabase
          .from('kids')
          .select(`
            id,
            full_name,
            birth_date,
            dni,
            parent_id,
            created_at
          `)
          .in('id', kidIds);
      } else {
        // New schema version
        query = supabase
          .from('kids')
          .select(`
            id,
            first_name,
            last_name,
            nickname,
            full_name,
            birth_date,
            dni,
            parent_id,
            created_at
          `)
          .in('id', kidIds);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching kids by IDs:', error.message);
        throw new Error(error.message);
      }

      if (!data || data.length === 0) {
        return [];
      }

      // Get group memberships for each kid
      const { data: memberships, error: membershipError } = await supabase
        .from('group_memberships')
        .select('kid_id, group_id')
        .in('kid_id', kidIds);

      if (membershipError) {
        throw new Error(membershipError.message);
      }

      // Map the data to the Kid type
      return data.map(kid => {
        // Get groups for this kid
        const kidGroups = memberships
          ? memberships
              .filter(m => m.kid_id === kid.id)
              .map(m => m.group_id)
          : [];

        // Use type assertion to help TypeScript understand the data structure
        const kidData = kid as any;

        if (useFullName) {
          // Old schema version - derive firstName and lastName from full_name
          const nameParts = (kidData.full_name || '').split(' ');
          const firstNamePart = nameParts[0] || '';
          const lastNamePart = nameParts.slice(1).join(' ') || '';

          return {
            id: kidData.id,
            firstName: firstNamePart,
            lastName: lastNamePart,
            nickname: '',
            fullName: kidData.full_name,
            birthDate: kidData.birth_date,
            dni: kidData.dni,
            parentId: '',
            groups: kidGroups,
            createdAt: kidData.created_at,
            parentId: '',
          };
        } else {
          // New schema version
          return {
            id: kidData.id,
            firstName: kidData.first_name,
            lastName: kidData.last_name,
            nickname: kidData.nickname,
            fullName: kidData.full_name,
            birthDate: kidData.birth_date,
            dni: kidData.dni,
            parentId: '',
            groups: kidGroups,
            createdAt: kidData.created_at,
            parentId: '',
          };
        }
      });
    } catch (error) {
      console.error('Error in getKidsByIds:', error);
      throw error;
    }
  },

  /**
   * Find a kid by DNI and birth date
   */
  findKidByDniAndBirthDate: async (dni: string, birthDate: string): Promise<Kid[]> => {
    try {
      console.log('[KidsService] findKidByDniAndBirthDate', { dni, birthDate });
      const { data, error } = await supabase
        .from('kids')
        .select('*')
        .eq('dni', dni)
        .eq('birth_date', birthDate);
      if (error) {
        console.error('[KidsService] Error in findKidByDniAndBirthDate', error, { dni, birthDate });
        throw new Error(error.message);
      }
      return data || [];
    } catch (err) {
      console.error('[KidsService] Exception in findKidByDniAndBirthDate', err, { dni, birthDate });
      throw err;
    }
  },

  /**
   * Link a kid to a parent (insertar en kid_parents)
   */
  linkKidToParent: async (kidId: string, parentId: string): Promise<void> => {
    try {
      console.log('[KidsService] linkKidToParent', { kidId, parentId });
      // Verificar si ya existe la relación
      const { data: existing, error: checkError } = await supabase
        .from('kid_parents')
        .select('id')
        .eq('kid_id', kidId)
        .eq('parent_id', parentId)
        .maybeSingle();
      if (checkError) throw new Error(checkError.message);
      if (existing) return; // Ya existe la relación
      // Insertar la relación
      const { error } = await supabase
        .from('kid_parents')
        .insert({ kid_id: kidId, parent_id: parentId });
      if (error) throw new Error(error.message);
    } catch (err) {
      console.error('[KidsService] Exception in linkKidToParent', err, { kidId, parentId });
      throw err;
    }
  },

  /**
   * Obtener todos los padres asociados a un niño
   */
  getParentsForKid: async (kidId: string): Promise<string[]> => {
    const { data, error } = await supabase
      .from('kid_parents')
      .select('parent_id')
      .eq('kid_id', kidId);
    if (error) throw new Error(error.message);
    return data ? data.map((row: any) => row.parent_id) : [];
  },
};
