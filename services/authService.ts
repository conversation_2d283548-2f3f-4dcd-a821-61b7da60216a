import { supabase } from '@/lib/supabase';
import { User } from '@/types';

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface SignUpData extends AuthCredentials {
  firstName: string;
  lastName: string;
  nickname?: string;
  phone?: string;
}

export const AuthService = {
  /**
   * Sign up a new user with direct user creation
   */
  signUp: async (data: SignUpData): Promise<User> => {
    const { email, password, firstName, lastName, nickname, phone } = data;

    // Ensure email is a string
    const emailStr = typeof email === 'string' ? email : String(email);
    const passwordStr = typeof password === 'string' ? password : String(password);
    const firstNameStr = typeof firstName === 'string' ? firstName : String(firstName);
    const lastNameStr = typeof lastName === 'string' ? lastName : String(lastName);
    const nicknameStr = nickname ? (typeof nickname === 'string' ? nickname : String(nickname)) : undefined;
    const phoneStr = phone ? (typeof phone === 'string' ? phone : String(phone)) : undefined;

    console.log('Signing up with email:', emailStr);

    try {
      // First, sign up the user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: emailStr,
        password: passwordStr,
        options: {
          data: {
            first_name: firstNameStr,
            last_name: lastNameStr,
            nickname: nicknameStr,
            phone: phoneStr,
          },
          emailRedirectTo: 'parentium://confirm-email',
        },
      });

      if (authError) {
        console.error('Auth signup error:', authError);
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error('No user data returned from signup');
      }

      // Now, manually create the user record in the public.users table
      // This is a fallback in case the database trigger doesn't work
      try {
        console.log('Manually creating user record in public.users table');
        const { error: insertError } = await supabase
          .from('users')
          .insert([
            {
              id: authData.user.id,
              email: emailStr,
              first_name: firstNameStr,
              last_name: lastNameStr,
              nickname: nicknameStr,
              phone: phoneStr,
            },
          ])
          .select()
          .single();

        if (insertError) {
          // If the error is because the user already exists, that's fine
          if (insertError.code === '23505') { // Unique violation
            console.log('User already exists in public.users table, continuing');
          } else {
            console.warn('Error creating user record:', insertError);
            // We'll continue anyway since the auth user was created
          }
        } else {
          console.log('User record created successfully');
        }
      } catch (dbError) {
        console.warn('Exception creating user record:', dbError);
        // Continue anyway since the auth user was created
      }

      // Return user data
      return {
        id: authData.user.id,
        email: authData.user.email || '',
        firstName: firstNameStr,
        lastName: lastNameStr,
        nickname: nicknameStr,
        fullName: `${firstNameStr} ${lastNameStr}`,
        phone: phoneStr,
        createdAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Signup process error:', error);
      throw error;
    }
  },

  /**
   * Sign in a user
   */
  signIn: async (credentials: AuthCredentials): Promise<User> => {
    const { email, password } = credentials;

    // Ensure email is a string
    const emailStr = typeof email === 'string' ? email : String(email);
    const passwordStr = typeof password === 'string' ? password : String(password);

    console.log('Signing in with email:', emailStr);

    let authData;
    try {
      const response = await supabase.auth.signInWithPassword({
        email: emailStr,
        password: passwordStr,
      });

      if (response.error) {
        throw new Error(response.error.message);
      }

      if (!response.data.user) {
        throw new Error('No user data returned from signin');
      }

      authData = response.data;
    } catch (err) {
      // Handle JSON parse errors which might occur if the API returns HTML instead of JSON
      if (err instanceof Error && err.message.includes('JSON Parse error')) {
        console.error('Network or API error:', err.message);
        throw new Error('Error de conexión con el servidor. Por favor verifica tu conexión a internet e intenta nuevamente.');
      }
      // Re-throw other errors
      throw err;
    }

    try {
      // Get user profile data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      console.log('signIn: Database query result:', { userData, userError });

      if (userError) {
        // If the error is because the table doesn't exist or the user doesn't exist in the table,
        // we can still return basic user data from auth and try to create the user record
        if (userError.code === '42P01' || userError.code === 'PGRST116') {
          console.warn('Users table not found or user not in table, using auth data');

          const firstName = authData.user.user_metadata?.first_name || 'Usuario';
          const lastName = authData.user.user_metadata?.last_name || 'Temporal';

          // Return user data from auth
          return {
            id: authData.user.id,
            email: authData.user.email || '',
            firstName: firstName,
            lastName: lastName,
            nickname: authData.user.user_metadata?.nickname,
            fullName: `${firstName} ${lastName}`,
            phone: authData.user.user_metadata?.phone,
            createdAt: authData.user.created_at,
          };
        }

        throw new Error(userError.message);
      }

      // Check if userData has valid ID (handle empty array case)
      if (!userData || !userData.id) {
        console.warn('signIn: Database record has no ID or is empty, using auth data as fallback');
        const firstName = authData.user.user_metadata?.first_name || 'Usuario';
        const lastName = authData.user.user_metadata?.last_name || 'Temporal';

        return {
          id: authData.user.id,
          email: authData.user.email || '',
          firstName: firstName,
          lastName: lastName,
          nickname: authData.user.user_metadata?.nickname,
          fullName: `${firstName} ${lastName}`,
          phone: authData.user.user_metadata?.phone,
          createdAt: authData.user.created_at,
        };
      }

      // Return user data from the database
      return {
        id: userData.id,
        email: userData.email,
        firstName: userData.first_name,
        lastName: userData.last_name,
        nickname: userData.nickname,
        fullName: userData.full_name,
        phone: userData.phone || undefined,
        profilePicture: userData.profile_picture,
        createdAt: userData.created_at,
      };
    } catch (error) {
      // If there's any other error, still allow login with basic auth data
      console.error('Error fetching user profile:', error);

      const firstName = authData.user.user_metadata?.first_name || 'Usuario';
      const lastName = authData.user.user_metadata?.last_name || 'Temporal';

      return {
        id: authData.user.id,
        email: authData.user.email || '',
        firstName: firstName,
        lastName: lastName,
        nickname: authData.user.user_metadata?.nickname,
        fullName: `${firstName} ${lastName}`,
        phone: authData.user.user_metadata?.phone,
        createdAt: authData.user.created_at,
      };
    }
  },

  /**
   * Sign out the current user
   */
  signOut: async (): Promise<void> => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Get the current session
   */
  getSession: async () => {
    return await supabase.auth.getSession();
  },

  /**
   * Refresh the current session
   */
  refreshSession: async () => {
    return await supabase.auth.refreshSession();
  },

  /**
   * Get the current user
   */
  getCurrentUser: async (): Promise<User | null> => {
    try {
      console.log('getCurrentUser: Starting...');
      const { data: authData } = await supabase.auth.getUser();

      if (!authData.user) {
        console.log('getCurrentUser: No auth user found');
        return null;
      }

      console.log('getCurrentUser: Auth user found:', authData.user.id, authData.user.email);

      // Get user profile data
      console.log('getCurrentUser: Fetching user profile data...');
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', authData.user.id)
        .single();

      console.log('getCurrentUser: Database query result:', { userData, userError });

      if (userError) {
        // If the error is because the table doesn't exist or the user doesn't exist in the table,
        // we can still return basic user data from auth
        if (userError.code === '42P01' || userError.code === 'PGRST116') {
          console.warn('Users table not found or user not in table, using auth data');
          const firstName = authData.user.user_metadata?.first_name || 'Usuario';
          const lastName = authData.user.user_metadata?.last_name || 'Temporal';

          return {
            id: authData.user.id,
            email: authData.user.email || '',
            firstName: firstName,
            lastName: lastName,
            nickname: authData.user.user_metadata?.nickname,
            fullName: `${firstName} ${lastName}`,
            phone: authData.user.user_metadata?.phone,
            createdAt: authData.user.created_at,
          };
        }
        console.error('Error fetching user data:', userError);
        return null;
      }

      // Check if userData has valid ID
      if (!userData.id) {
        console.log('getCurrentUser: Database record has no ID, using auth data as fallback');
        const firstName = authData.user.user_metadata?.first_name || 'Usuario';
        const lastName = authData.user.user_metadata?.last_name || 'Temporal';

        return {
          id: authData.user.id,
          email: authData.user.email || '',
          firstName: firstName,
          lastName: lastName,
          nickname: authData.user.user_metadata?.nickname,
          fullName: `${firstName} ${lastName}`,
          phone: authData.user.user_metadata?.phone,
          createdAt: authData.user.created_at,
        };
      }

      // Return user data
      return {
        id: userData.id,
        email: userData.email,
        firstName: userData.first_name,
        lastName: userData.last_name,
        nickname: userData.nickname,
        fullName: userData.full_name,
        phone: userData.phone || undefined,
        profilePicture: userData.profile_picture,
        createdAt: userData.created_at,
      };
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  },

  /**
   * Update user profile
   */
  updateProfile: async (
    userId: string,
    data: { firstName: string; lastName: string; nickname?: string; email: string; phone?: string }
  ): Promise<User> => {
    console.log('AuthService.updateProfile called with:', { userId, data });
    const { firstName, lastName, nickname, email, phone } = data;
    let userData: any = null;

    try {
      // Update auth email if changed
      const { data: authData } = await supabase.auth.getUser();
      console.log('Current auth user:', authData.user);

      if (authData.user && authData.user.email !== email) {
        console.log('Email changed, updating auth email from', authData.user.email, 'to', email);
        const { error: updateError } = await supabase.auth.updateUser({
          email,
        });

        if (updateError) {
          console.error('Error updating auth email:', updateError);
          throw new Error(updateError.message);
        }
        console.log('Auth email updated successfully');
      }

      console.log('Updating user profile in database for user ID:', userId);
      // Update profile data
      const { data: updatedUserData, error: userError } = await supabase
        .from('users')
        .update({
          first_name: firstName,
          last_name: lastName,
          nickname,
          phone,
        })
        .eq('id', userId)
        .select()
        .single();

      if (userError) {
        console.error('Error updating user profile:', userError);
        throw new Error(userError.message);
      }

      userData = updatedUserData;
      console.log('User profile updated successfully:', userData);

      // Return updated user data
      return {
        id: userData.id,
        email: userData.email,
        firstName: userData.first_name,
        lastName: userData.last_name,
        nickname: userData.nickname,
        fullName: userData.full_name,
        phone: userData.phone || undefined,
        profilePicture: userData.profile_picture,
        createdAt: userData.created_at,
      };
    } catch (error) {
      console.error('Error in updateProfile:', error);
      throw error;
    }
  },

  /**
   * Reset password
   */
  resetPassword: async (email: string): Promise<void> => {
    // Ensure email is a string
    const emailStr = typeof email === 'string' ? email : String(email);

    console.log('Resetting password for email:', emailStr);

    const { error } = await supabase.auth.resetPasswordForEmail(emailStr, {
      redirectTo: 'https://main--cheerful-genie-53baa3.netlify.app/auth-redirect.html',
    });
    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Get users by IDs
   */
  getUsersByIds: async (userIds: string[]): Promise<{ data: User[] | null, error: Error | null }> => {
    try {
      if (!userIds || userIds.length === 0) {
        return { data: [], error: null };
      }

      console.log('Fetching users with IDs:', userIds);

      // First try to get users from the users table
      try {
        // Make sure we're using a clean array of unique IDs
        const uniqueUserIds = [...new Set(userIds)];

        // Add a cache-busting parameter to ensure we get fresh data
        const timestamp = Date.now();
        const { data, error } = await supabase
          .from('users')
          .select('*')
          .in('id', uniqueUserIds)
          .order('id', { ascending: true })  // Add consistent ordering
          .limit(100);  // Add a reasonable limit

        if (error) {
          // If there's an error with the users table, we'll try the auth API as fallback
          console.warn('Error fetching from users table, will try auth API:', error);
          throw error;
        }

        // Map the data to User objects
        const users: User[] = data.map(user => ({
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          nickname: user.nickname,
          fullName: user.full_name || `${user.first_name} ${user.last_name}`,
          phone: user.phone,
          profilePicture: user.profile_picture,
          createdAt: user.created_at,
        }));

        // Check if we got all the users we requested
        const fetchedUserIds = new Set(users.map(u => u.id));
        const missingUserIds = uniqueUserIds.filter(id => !fetchedUserIds.has(id));

        // If we're missing some users, create placeholders for them
        if (missingUserIds.length > 0) {
          console.log(`Missing ${missingUserIds.length} users, creating placeholders`);

          // Create simple placeholder users
          const placeholderUsers: User[] = missingUserIds.map(id => {
            console.log(`Creating placeholder for missing user ID: ${id}`);

            // For P1 (<EMAIL>)
            if (id === '7dfbd535-6711-4d28-af34-bc1ccafa696c') {
              return {
                id,
                email: '<EMAIL>',
                firstName: 'P1_Nombre',
                lastName: 'P1_Apellido',
                fullName: 'P1_Nombre P1_Apellido',
                createdAt: new Date().toISOString(),
              };
            }

            // For P2 (<EMAIL>)
            if (id === '023c2a63-4653-4967-bd56-951f0ef06868') {
              return {
                id,
                email: '<EMAIL>',
                firstName: 'P2_Nombre',
                lastName: 'P2_Apellido',
                fullName: 'P2_Nombre P2_Apellido',
                createdAt: new Date().toISOString(),
              };
            }

            // Default placeholder with better naming
            return {
              id,
              email: '',
              firstName: 'Usuario',
              lastName: 'Desconocido',
              fullName: 'Usuario Desconocido',  // More descriptive than just "Usuario"
              createdAt: new Date().toISOString(),
            };
          });

          // Combine the fetched users with the placeholders
          users.push(...placeholderUsers);
        }

        console.log('Successfully fetched users from users table:', users.length);
        return { data: users, error: null };
      } catch (tableError) {
        // Fallback: If we can't get from the users table, create placeholder users
        console.log('Using fallback for user data');

        // Create simple placeholder users
        const placeholderUsers: User[] = userIds.map(id => {
          console.log(`Creating fallback placeholder for user ID: ${id}`);

          // For P1 (<EMAIL>)
          if (id === '7dfbd535-6711-4d28-af34-bc1ccafa696c') {
            return {
              id,
              email: '<EMAIL>',
              firstName: 'P1_Nombre',
              lastName: 'P1_Apellido',
              fullName: 'P1_Nombre P1_Apellido',
              createdAt: new Date().toISOString(),
            };
          }

          // For P2 (<EMAIL>)
          if (id === '023c2a63-4653-4967-bd56-951f0ef06868') {
            return {
              id,
              email: '<EMAIL>',
              firstName: 'P2_Nombre',
              lastName: 'P2_Apellido',
              fullName: 'P2_Nombre P2_Apellido',
              createdAt: new Date().toISOString(),
            };
          }

          // Default placeholder with better naming
          return {
            id,
            email: '',
            firstName: 'Usuario',
            lastName: 'Desconocido',
            fullName: 'Usuario Desconocido',  // More descriptive than just "Usuario"
            createdAt: new Date().toISOString(),
          };
        });

        console.log('Created placeholder users:', placeholderUsers.length);
        return { data: placeholderUsers, error: null };
      }
    } catch (error) {
      console.error('Exception in getUsersByIds:', error);
      return { data: null, error: error instanceof Error ? error : new Error('Unknown error') };
    }
  },

  /**
   * Resend verification email
   */
  resendVerificationEmail: async (email: string): Promise<void> => {
    // Ensure email is a string
    const emailStr = typeof email === 'string' ? email : String(email);

    console.log('Resending verification email to:', emailStr);

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: emailStr,
      options: {
        emailRedirectTo: 'parentium://confirm-email',
      },
    });
    if (error) {
      throw new Error(error.message);
    }
  },
};
