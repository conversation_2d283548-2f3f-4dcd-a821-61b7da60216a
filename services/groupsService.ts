import { supabase } from '@/lib/supabase';
import { Group } from '@/types';
import { GroupMembership } from '@/store/groupsStore';
import { InstitutionsService } from './institutionsService';

export interface GroupData {
  institutionName: string;
  institutionAddress?: string;
  sala: string;
  año: number;
  nombre?: string;
  kidId?: string; // Kid ID for the creator's membership
}

export const GroupsService = {
  /**
   * Get all groups
   */
  getGroups: async (userId: string): Promise<{ allGroups: Group[], myGroups: Group[] }> => {
    // Get all groups with institution information (using LEFT JOIN to include groups without institutions)
    const { data: allGroupsData, error } = await supabase
      .from('groups')
      .select(`
        id,
        institution_id,
        institutions(
          id,
          name,
          address,
          status
        ),
        sala,
        año,
        nombre,
        created_by,
        created_at
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('GroupsService.getGroups: Error fetching groups:', error);
      throw new Error(error.message);
    }

    console.log('GroupsService.getGroups: Fetched groups data:', allGroupsData?.length || 0, 'groups');

    // Get group memberships
    const { data: memberships, error: membershipError } = await supabase
      .from('group_memberships')
      .select('group_id, user_id, kid_id');

    if (membershipError) {
      console.error('GroupsService.getGroups: Error fetching memberships:', membershipError);
      throw new Error(membershipError.message);
    }

    console.log('GroupsService.getGroups: Fetched memberships:', memberships?.length || 0, 'memberships');

    // Map the data to the Group type
    const allGroups = allGroupsData.map(group => {
      // Get members and kids for this group
      const groupMemberships = memberships ? memberships.filter(m => m.group_id === group.id) : [];
      const members = [...new Set(groupMemberships.map(m => m.user_id))];
      const kids = [...new Set(groupMemberships.filter(m => m.kid_id).map(m => m.kid_id))];

      return {
        id: group.id,
        institutionId: group.institution_id,
        institutionName: group.institutions?.name || 'Institución sin nombre',
        sala: group.sala,
        año: group.año,
        nombre: group.nombre,
        createdBy: group.created_by,
        createdAt: group.created_at,
        members,
        kids,
      };
    });

    // Filter my groups
    const myGroups = allGroups.filter(group =>
      group.members.includes(userId) || group.createdBy === userId
    );

    console.log('GroupsService.getGroups: Final results - allGroups:', allGroups.length, 'myGroups:', myGroups.length);
    console.log('GroupsService.getGroups: User ID:', userId);
    console.log('GroupsService.getGroups: My groups:', myGroups.map(g => ({ id: g.id, institutionName: g.institutionName, sala: g.sala, createdBy: g.createdBy, members: g.members })));

    return { allGroups, myGroups };
  },

  /**
   * Get a group by ID
   */
  getGroupById: async (groupId: string): Promise<Group | null> => {
    const { data, error } = await supabase
      .from('groups')
      .select(`
        id,
        institution_id,
        institutions(
          id,
          name,
          address,
          status
        ),
        sala,
        año,
        nombre,
        created_by,
        created_at
      `)
      .eq('id', groupId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Group not found
      }
      throw new Error(error.message);
    }

    // Get group memberships
    const { data: memberships, error: membershipError } = await supabase
      .from('group_memberships')
      .select('user_id, kid_id')
      .eq('group_id', groupId);

    if (membershipError) {
      throw new Error(membershipError.message);
    }

    // Map the data to the Group type
    return {
      id: data.id,
      institutionId: data.institution_id,
      institutionName: data.institutions?.name || 'Institución sin nombre',
      sala: data.sala,
      año: data.año,
      nombre: data.nombre,
      createdBy: data.created_by,
      createdAt: data.created_at,
      members: [...new Set(memberships.map(m => m.user_id))],
      kids: [...new Set(memberships.filter(m => m.kid_id).map(m => m.kid_id))],
    };
  },

  /**
   * Create a new group
   */
  createGroup: async (userId: string, groupData: GroupData): Promise<Group> => {
    const { institutionName, institutionAddress, sala, año, nombre, kidId } = groupData;

    // A kid ID is required to create a group
    if (!kidId) {
      throw new Error('You must select a kid to create a group');
    }

    // Get or create the institution
    const institution = await InstitutionsService.getOrCreateInstitution(institutionName, institutionAddress);

    // Create the group with institution_id
    const { data: group, error: groupError } = await supabase
      .from('groups')
      .insert({
        institution_id: institution.id,
        sala,
        año,
        nombre,
        created_by: userId,
      })
      .select()
      .single();

    if (groupError) {
      throw new Error(groupError.message);
    }

    // Add creator as a member with the selected kid
    const { error: membershipError } = await supabase
      .from('group_memberships')
      .insert({
        group_id: group.id,
        user_id: userId,
        kid_id: kidId,
        is_referente: true,
      });

    if (membershipError) {
      // If membership creation fails, delete the group to maintain consistency
      await supabase
        .from('groups')
        .delete()
        .eq('id', group.id);
      throw new Error(membershipError.message);
    }

    // Return the new group with minimal data
    return {
      id: group.id,
      institutionId: group.institution_id,
      institutionName: institution.name,
      sala: group.sala,
      año: group.año,
      nombre: group.nombre,
      createdBy: group.created_by,
      createdAt: group.created_at,
      members: [userId],
      kids: [kidId],
    };
  },

  /**
   * Join a group
   */
  joinGroup: async (groupId: string, userId: string, kidIds: string[]): Promise<void> => {
    // A user must join with at least one kid
    if (kidIds.length === 0) {
      throw new Error('You must select at least one kid to join the group');
    }

    // Create memberships for each kid-user pair
    const memberships = kidIds.map(kidId => ({
      group_id: groupId,
      user_id: userId,
      kid_id: kidId,
    }));

    // Insert all memberships
    const { error } = await supabase
      .from('group_memberships')
      .insert(memberships);

    if (error) {
      // Check if it's a unique constraint violation (user already joined with this kid)
      if (error.code === '23505') { // PostgreSQL unique constraint violation
        throw new Error('You have already joined this group with one or more of the selected kids');
      }
      throw new Error(error.message);
    }
  },

  /**
   * Leave a group
   */
  leaveGroup: async (groupId: string, userId: string): Promise<void> => {
    // Remove user and their kids from the group
    const { error } = await supabase
      .from('group_memberships')
      .delete()
      .eq('group_id', groupId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Search for groups
   */
  searchGroups: async (query: string): Promise<Group[]> => {
    const { data, error } = await supabase
      .from('groups')
      .select(`
        id,
        institution_id,
        institutions(
          id,
          name,
          address,
          status
        ),
        sala,
        año,
        nombre,
        created_by,
        created_at
      `)
      .or(`institutions.name.ilike.%${query}%,sala.ilike.%${query}%,nombre.ilike.%${query}%`)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Get group memberships
    const groupIds = data.map(group => group.id);
    const { data: memberships, error: membershipError } = await supabase
      .from('group_memberships')
      .select('group_id, user_id, kid_id')
      .in('group_id', groupIds);

    if (membershipError) {
      throw new Error(membershipError.message);
    }

    // Map the data to the Group type
    return data.map(group => {
      // Get members and kids for this group
      const groupMemberships = memberships ? memberships.filter(m => m.group_id === group.id) : [];
      const members = [...new Set(groupMemberships.map(m => m.user_id))];
      const kids = [...new Set(groupMemberships.filter(m => m.kid_id).map(m => m.kid_id))];

      return {
        id: group.id,
        institutionId: group.institution_id,
        institutionName: group.institutions?.name || 'Institución sin nombre',
        sala: group.sala,
        año: group.año,
        nombre: group.nombre,
        createdBy: group.created_by,
        createdAt: group.created_at,
        members,
        kids,
      };
    });
  },

  /**
   * Search for schools
   */
  searchSchools: async (query: string): Promise<string[]> => {
    const { data, error } = await supabase
      .from('institutions')
      .select('name')
      .ilike('name', `%${query}%`)
      .eq('status', 'active')
      .order('name')
      .limit(10);

    if (error) {
      throw new Error(error.message);
    }

    // Return institution names
    return data.map(institution => institution.name);
  },

  /**
   * Get memberships for a group
   */
  getGroupMemberships: async (groupId: string): Promise<GroupMembership[]> => {
    const { data, error } = await supabase
      .from('group_memberships')
      .select(`
        id,
        group_id,
        user_id,
        kid_id,
        is_referente,
        created_at
      `)
      .eq('group_id', groupId);

    if (error) {
      throw new Error(error.message);
    }

    // Map the data to the GroupMembership type
    return data.map(membership => ({
      id: membership.id,
      groupId: membership.group_id,
      userId: membership.user_id,
      kidId: membership.kid_id,
      isReferente: membership.is_referente,
      createdAt: membership.created_at,
    }));
  },

  /**
   * Check if a user is a referente for a group
   */
  isReferente: async (groupId: string, userId: string): Promise<boolean> => {
    const { data, error } = await supabase
      .from('group_memberships')
      .select('is_referente')
      .eq('group_id', groupId)
      .eq('user_id', userId)
      .eq('is_referente', true)
      .maybeSingle();

    if (error) {
      throw new Error(error.message);
    }

    return !!data;
  },
};
