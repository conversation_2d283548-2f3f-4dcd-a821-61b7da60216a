import { supabase } from '@/lib/supabase';
import { Announcement } from '@/types';

export interface AnnouncementData {
  title: string;
  message: string;
  type: string;
  date?: string;
}

export const AnnouncementsService = {
  /**
   * Get announcements for a group
   */
  getAnnouncementsByGroupId: async (groupId: string): Promise<Announcement[]> => {
    const { data, error } = await supabase
      .from('announcements')
      .select(`
        id,
        group_id,
        title,
        message,
        type,
        date,
        created_by,
        created_at
      `)
      .eq('group_id', groupId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Map the data to the Announcement type
    return data.map(announcement => ({
      id: announcement.id,
      groupId: announcement.group_id,
      title: announcement.title,
      message: announcement.message,
      type: announcement.type,
      date: announcement.date,
      createdBy: announcement.created_by,
      createdAt: announcement.created_at,
    }));
  },

  /**
   * Create a new announcement
   */
  createAnnouncement: async (
    groupId: string,
    userId: string,
    announcementData: AnnouncementData
  ): Promise<Announcement> => {
    const { title, message, type, date } = announcementData;

    // Insert new announcement
    const { data, error } = await supabase
      .from('announcements')
      .insert({
        group_id: groupId,
        title,
        message,
        type,
        date,
        created_by: userId,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the new announcement
    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      message: data.message,
      type: data.type,
      date: data.date,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Update an announcement
   */
  updateAnnouncement: async (
    announcementId: string,
    announcementData: AnnouncementData
  ): Promise<Announcement> => {
    const { title, message, type, date } = announcementData;

    // Update announcement
    const { data, error } = await supabase
      .from('announcements')
      .update({
        title,
        message,
        type,
        date,
      })
      .eq('id', announcementId)
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the updated announcement
    return {
      id: data.id,
      groupId: data.group_id,
      title: data.title,
      message: data.message,
      type: data.type,
      date: data.date,
      createdBy: data.created_by,
      createdAt: data.created_at,
    };
  },

  /**
   * Delete an announcement
   */
  deleteAnnouncement: async (announcementId: string): Promise<void> => {
    const { error } = await supabase
      .from('announcements')
      .delete()
      .eq('id', announcementId);

    if (error) {
      throw new Error(error.message);
    }
  },
};
