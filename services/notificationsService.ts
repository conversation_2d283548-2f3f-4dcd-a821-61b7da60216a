import { supabase } from '@/lib/supabase';
import { Notification } from '@/types';

export const NotificationsService = {
  /**
   * Get notifications for a user
   */
  getNotificationsByUserId: async (userId: string): Promise<Notification[]> => {
    const { data, error } = await supabase
      .from('notifications')
      .select(`
        id,
        user_id,
        type,
        message,
        group_id,
        event_id,
        announcement_id,
        colecta_id,
        poll_id,
        is_read,
        created_at
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(error.message);
    }

    // Map the data to the Notification type
    return data.map(notification => ({
      id: notification.id,
      userId: notification.user_id,
      type: notification.type,
      message: notification.message,
      groupId: notification.group_id,
      eventId: notification.event_id,
      announcementId: notification.announcement_id,
      colectaId: notification.colecta_id,
      pollId: notification.poll_id,
      status: notification.is_read ? 'READ' : 'UNREAD',
      createdAt: notification.created_at,
    }));
  },

  /**
   * Mark a notification as read
   */
  markAsRead: async (notificationId: string): Promise<void> => {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('id', notificationId);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Mark all notifications as read for a user
   */
  markAllAsRead: async (userId: string): Promise<void> => {
    const { error } = await supabase
      .from('notifications')
      .update({ is_read: true })
      .eq('user_id', userId)
      .eq('is_read', false);

    if (error) {
      throw new Error(error.message);
    }
  },

  /**
   * Create a notification
   */
  createNotification: async (
    userId: string,
    type: string,
    message: string,
    data: {
      groupId?: string;
      eventId?: string;
      announcementId?: string;
      colectaId?: string;
      pollId?: string;
    }
  ): Promise<Notification> => {
    const { groupId, eventId, announcementId, colectaId, pollId } = data;

    // Insert new notification
    const { data: notificationData, error } = await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type,
        message,
        group_id: groupId,
        event_id: eventId,
        announcement_id: announcementId,
        colecta_id: colectaId,
        poll_id: pollId,
        is_read: false,
      })
      .select()
      .single();

    if (error) {
      throw new Error(error.message);
    }

    // Return the new notification
    return {
      id: notificationData.id,
      userId: notificationData.user_id,
      type: notificationData.type,
      message: notificationData.message,
      groupId: notificationData.group_id,
      eventId: notificationData.event_id,
      announcementId: notificationData.announcement_id,
      colectaId: notificationData.colecta_id,
      pollId: notificationData.poll_id,
      status: 'UNREAD',
      createdAt: notificationData.created_at,
    };
  },

  /**
   * Delete a notification
   */
  deleteNotification: async (notificationId: string): Promise<void> => {
    const { error } = await supabase
      .from('notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      throw new Error(error.message);
    }
  },
};
