import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Linking } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Lock } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { supabase } from '@/lib/supabase';
import { useToastStore } from '@/store/toastStore';
import { useAuthStore } from '@/store/authStore';
import { EXPO_PUBLIC_SUPABASE_URL, EXPO_PUBLIC_SUPABASE_ANON_KEY } from '@env';

export default function ResetPasswordScreen() {
  const params = useLocalSearchParams();
  const { showToast } = useToastStore();
  const { checkAuth } = useAuthStore();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [accessToken, setAccessToken] = useState<string | null>(null);

  // Extract access token from URL or params
  useEffect(() => {
    const extractAccessToken = async () => {
      console.log('Extracting access token...');
      console.log('All params:', JSON.stringify(params, null, 2));

      // First check if we have it in the params
      const tokenFromParams = params.token as string;
      const accessTokenFromParams = params.access_token as string;

      console.log('Token from params:', tokenFromParams);
      console.log('Access token from params:', accessTokenFromParams);

      if (accessTokenFromParams) {
        console.log('Found access_token in params, length:', accessTokenFromParams.length);
        setAccessToken(accessTokenFromParams);
        return;
      }

      if (tokenFromParams) {
        console.log('Found token in params, length:', tokenFromParams.length);
        setAccessToken(tokenFromParams);
        return;
      }

      // If not in params, try to extract from the URL hash
      try {
        const initialUrl = await Linking.getInitialURL();
        console.log('Initial URL in reset-password:', initialUrl);

        if (initialUrl) {
          const hashIndex = initialUrl.indexOf('#');
          if (hashIndex !== -1) {
            const hash = initialUrl.substring(hashIndex + 1);
            const hashParams = new URLSearchParams(hash);
            const accessTokenFromHash = hashParams.get('access_token');

            if (accessTokenFromHash) {
              console.log('Found access_token in hash');
              setAccessToken(accessTokenFromHash);
              return;
            }
          }
        }
      } catch (error) {
        console.error('Error extracting access token from URL:', error);
      }
    };

    extractAccessToken();
  }, [params]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!password) {
      newErrors.password = 'La contraseña es requerida';
    } else if (password.length < 6) {
      newErrors.password = 'La contraseña debe tener al menos 6 caracteres';
    }

    if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Las contraseñas no coinciden';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleResetPassword = async () => {
    console.log('handleResetPassword called');

    if (!validate()) {
      console.log('Validation failed');
      return;
    }

    console.log('Validation passed, starting reset process');
    setIsLoading(true);

    try {
      console.log('Starting password reset process');
      console.log('Access token available:', !!accessToken);
      console.log('Access token length:', accessToken?.length);
      console.log('Access token value:', accessToken);

      // Always show a toast to confirm the button was pressed
      showToast('Procesando restablecimiento...', 'info');

      if (!accessToken) {
        console.error('No access token available');
        showToast('Token de restablecimiento inválido o expirado', 'error');
        setIsLoading(false);
        return;
      }

      console.log('Resetting password with access token');

      // For password reset, we need to exchange the access token for a session first
      // This is the correct way to handle password reset with Supabase

      try {
        console.log('Attempting to exchange access token for session...');

        // Use the access token to get a proper session
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        console.log('Current session check:', { sessionData, sessionError });

        // Set the session using the access token from the recovery email
        const { data: newSessionData, error: setSessionError } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: '' // Recovery tokens don't come with refresh tokens
        });

        if (setSessionError) {
          console.error('Error setting session with access token:', setSessionError);

          // Try alternative approach: use the token directly in the Authorization header
          console.log('Trying alternative approach with Authorization header...');

          const response = await fetch(`${EXPO_PUBLIC_SUPABASE_URL}/auth/v1/user`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              'apikey': EXPO_PUBLIC_SUPABASE_ANON_KEY
            },
            body: JSON.stringify({
              password: password
            })
          });

          if (!response.ok) {
            const errorData = await response.text();
            console.error('Direct API call failed:', response.status, errorData);

            // Parse error response to show specific messages
            try {
              const errorJson = JSON.parse(errorData);
              if (errorJson.error_code === 'same_password') {
                showToast('La nueva contraseña debe ser diferente a la actual', 'error');
              } else {
                showToast(`Error al restablecer la contraseña: ${errorJson.msg || 'Token inválido o expirado'}`, 'error');
              }
            } catch {
              showToast('Error al restablecer la contraseña. Token inválido o expirado.', 'error');
            }
            return;
          }

          console.log('Password updated successfully via direct API call');
        } else {
          console.log('Session set successfully, now updating password...');

          // Now that we have a valid session, update the password
          const { error: updateError } = await supabase.auth.updateUser({
            password: password
          });

          if (updateError) {
            console.error('Error updating password after setting session:', updateError);
            showToast('Error al restablecer la contraseña: ' + updateError.message, 'error');
            return;
          }

          console.log('Password updated successfully via updateUser');
        }
      } catch (error) {
        console.error('Error in password reset process:', error);
        showToast('Error en el proceso de restablecimiento', 'error');
        return;
      }

      console.log('Password updated successfully');

      // Check auth status to update the store
      await checkAuth();

      showToast('Contraseña restablecida con éxito', 'success');

      // Wait a moment before redirecting to ensure the toast is shown
      setTimeout(() => {
        router.replace('/login');
      }, 1000);

    } catch (error) {
      console.error('Error in reset password process:', error);
      showToast('Error en el proceso de restablecimiento', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.content}>
            <Text style={styles.title}>Restablecer Contraseña</Text>
            <Text style={styles.subtitle}>
              Ingresa tu nueva contraseña para restablecer tu cuenta.
            </Text>

            {/* Debug info - show in both debug and release for now */}
            <View style={styles.debugInfo}>
              <Text style={styles.debugText}>
                Token status: {accessToken ? '✅ Token disponible' : '❌ Sin token'}
              </Text>
              {accessToken && (
                <Text style={styles.debugText}>
                  Token length: {accessToken.length}
                </Text>
              )}
            </View>

            <Input
              label="Nueva contraseña"
              placeholder="Ingresa tu nueva contraseña"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (errors.password) setErrors({...errors, password: ''});
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.textSecondary} />}
              error={errors.password}
            />

            <Input
              label="Confirmar contraseña"
              placeholder="Confirma tu nueva contraseña"
              value={confirmPassword}
              onChangeText={(text) => {
                setConfirmPassword(text);
                if (errors.confirmPassword) setErrors({...errors, confirmPassword: ''});
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.textSecondary} />}
              error={errors.confirmPassword}
            />

            <Button
              title="Restablecer Contraseña"
              onPress={handleResetPassword}
              loading={isLoading}
              disabled={!password || !confirmPassword}
              style={styles.resetButton}
            />

            {/* Debug button - remove in production */}
            {__DEV__ && (
              <Button
                title="Debug: Mostrar Token Info"
                onPress={() => {
                  console.log('Debug button pressed');
                  console.log('Access token:', accessToken);
                  console.log('All params:', JSON.stringify(params, null, 2));
                  showToast(`Token: ${accessToken ? 'Disponible' : 'No disponible'}`, 'info');
                }}
                style={[styles.resetButton, { backgroundColor: '#666' }]}
              />
            )}
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  resetButton: {
    marginTop: 16,
  },
  debugInfo: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  debugText: {
    fontSize: 12,
    color: '#666',
    fontFamily: 'monospace',
  },
});
