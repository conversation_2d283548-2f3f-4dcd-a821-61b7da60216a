import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, KeyboardAvoidingView, Platform, ScrollView, Alert, Dimensions, TextInput } from 'react-native';
import { Link, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Mail, Lock } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { useToastStore } from '@/store/toastStore';
import { AuthService } from '@/services/authService';

const screenWidth = Dimensions.get('window').width;

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, isLoading, error, isAuthenticated, currentUser, clearError, checkAuth } = useAuthStore();
  const { showToast } = useToastStore();
  const [authChecked, setAuthChecked] = useState(false);
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  // Focus email input on mount
  useEffect(() => {
    console.log('LoginScreen: MOUNT');
    // Add a small delay to ensure the input is ready
    const timer = setTimeout(() => {
      emailInputRef.current?.focus();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Handle successful login
  const handleLogin = async () => {
    if (!email || !password) {
      showToast('Por favor ingresa tu correo y contraseña', 'error');
      return;
    }

    try {
      console.log('Login attempt from login screen with email:', email);

      // Convert form values to strings and trim whitespace
      const emailStr = String(email).trim();
      const passwordStr = String(password);

      // Start login process
      await login(emailStr, passwordStr);
      // Redirect logic is handled in useEffect
    } catch (err) {
      console.error('Login error in component:', err);

      // Check if the error is related to email confirmation
      if (error && error.includes('Email not confirmed')) {
        Alert.alert(
          'Correo no verificado',
          'Por favor verifica tu correo electrónico antes de iniciar sesión. Hemos enviado un enlace de verificación a tu correo.',
          [
            {
              text: 'Reenviar correo',
              onPress: async () => {
                try {
                  const emailStr = String(email).trim();
                  console.log('Resending verification email to:', emailStr);
                  await AuthService.resendVerificationEmail(emailStr);
                  showToast('Correo de verificación reenviado', 'success');
                } catch (resendErr) {
                  showToast('Error al reenviar el correo', 'error');
                }
              },
            },
            { text: 'OK' },
          ]
        );
      }
      // Check if it's a network error
      else if (error && (error.includes('conexión') || error.includes('JSON Parse error'))) {
        Alert.alert(
          'Error de conexión',
          'No se pudo conectar con el servidor. Por favor verifica tu conexión a internet e intenta nuevamente.',
          [{ text: 'OK' }]
        );
      }
    }
  };

  useEffect(() => {
    if (isAuthenticated && currentUser?.id) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, currentUser]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Image
              source={require('../../assets/qids-logo-full.png')}
              style={[styles.logo, { width: screenWidth * 0.75, height: undefined, aspectRatio: 300/202 }]}
              resizeMode="contain"
            />
          </View>

          <View style={styles.formContainer}>
            <Text style={styles.formTitle}>Iniciar Sesión</Text>

            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <Input
              label="Correo electrónico"
              placeholder="Ingresa tu correo"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (error) clearError();
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon={<Mail size={20} color={colors.textSecondary} />}
              onSubmitEditing={() => passwordInputRef.current?.focus()}
              ref={emailInputRef}
            />

            <Input
              label="Contraseña"
              placeholder="Ingresa tu contraseña"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (error) clearError();
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.textSecondary} />}
              ref={passwordInputRef}
              onSubmitEditing={handleLogin}
            />

            <Link href="/forgot-password" asChild>
              <TouchableOpacity style={styles.forgotPasswordContainer} disabled={isLoading}>
                <Text style={styles.forgotPasswordText}>¿Olvidaste tu contraseña?</Text>
              </TouchableOpacity>
            </Link>

            <Button
              title="Iniciar Sesión"
              onPress={handleLogin}
              loading={isLoading}
              disabled={!email || !password || isLoading}
              style={styles.loginButton}
            />

            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>¿No tienes una cuenta?</Text>
              <Link href="/register" asChild>
                <TouchableOpacity disabled={isLoading}>
                  <Text style={styles.registerLink}>Regístrate</Text>
                </TouchableOpacity>
              </Link>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  logo: {
    marginBottom: 16,
    alignSelf: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  formContainer: {
    width: '100%',
  },
  formTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 24,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: colors.link,
    fontSize: 14,
  },
  loginButton: {
    marginBottom: 24,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
  },
  registerText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  registerLink: {
    color: colors.link,
    fontSize: 14,
    fontWeight: '500',
  },
});