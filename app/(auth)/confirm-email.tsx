import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, Linking } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/store/authStore';
import { useToastStore } from '@/store/toastStore';

export default function ConfirmEmailScreen() {
  const { showToast } = useToastStore();
  const { checkAuth } = useAuthStore();
  const params = useLocalSearchParams();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);

  useEffect(() => {
    // Function to extract parameters from URL hash
    const extractHashParams = (hash: string) => {
      if (!hash || hash.length === 0) return {};

      // Remove the leading # if present
      const cleanHash = hash.startsWith('#') ? hash.substring(1) : hash;

      // Split the hash into key-value pairs
      const pairs = cleanHash.split('&');
      const params: Record<string, string> = {};

      pairs.forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params[key] = decodeURIComponent(value);
        }
      });

      return params;
    };

    const verifyEmail = async () => {
      try {
        // First check URL params (from expo-router)
        let token = params.token as string;
        let type = params.type as string;
        let accessToken = params.access_token as string;

        // If we don't have the necessary params, try to get them from the URL hash
        if ((!token || !type) && !accessToken) {
          // Get the initial URL that opened the app
          const initialUrl = await Linking.getInitialURL();
          console.log('Initial URL:', initialUrl);

          if (initialUrl) {
            // Extract the hash part of the URL
            const hashIndex = initialUrl.indexOf('#');
            if (hashIndex !== -1) {
              const hash = initialUrl.substring(hashIndex);
              const hashParams = extractHashParams(hash);

              // Check if we have an access_token in the hash
              if (hashParams.access_token) {
                accessToken = hashParams.access_token;
                type = 'recovery'; // Assume it's a recovery if we have an access_token
                console.log('Found access_token in hash:', accessToken);
              }
            }
          }
        }

        // Log all parameters for debugging
        console.log('Confirm Email Params:', {
          token,
          type,
          accessToken,
          tokenLength: token?.length,
          tokenType: typeof token,
          allParams: JSON.stringify(params)
        });

        // If we still don't have the necessary params, show an error
        if ((!token && !accessToken) || (!type && !accessToken)) {
          console.error('Missing token or type:', { token, type, accessToken });
          showToast('Link de confirmación inválido', 'error');
          setIsVerifying(false);
          return;
        }

        // If we have an access token, set the session directly
        if (accessToken) {
          console.log('Setting session with access token');

          // For password reset with access_token
          router.replace({
            pathname: '/reset-password',
            params: { access_token: accessToken }
          });
          return;
        }

        // Handle normal token verification
        if (type === 'signup') {
          // For signup confirmation
          console.log('Attempting to verify signup with token:', token);
          console.log('Token length:', token.length);

          // Check if this is a 6-digit OTP or a token_hash
          if (token.length === 6 && /^\d+$/.test(token)) {
            // This is a 6-digit OTP, we need the email to verify it
            console.log('Detected 6-digit OTP, but we need email for verification');
            showToast('Este tipo de verificación requiere el email. Por favor, usa el enlace completo del correo.', 'error');
            setIsVerifying(false);
            return;
          } else {
            // This should be a token_hash
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'signup',
            });

            if (error) {
              console.error('Error verifying email:', error);
              console.error('Error details:', JSON.stringify(error, null, 2));
              showToast('Error al verificar el correo electrónico', 'error');
              setIsVerifying(false);
              return;
            }

            // Email verified successfully
            setIsSuccess(true);
            showToast('Correo electrónico verificado con éxito', 'success');

            // Check auth status to update the store
            await checkAuth();
          }
        } else if (type === 'recovery') {
          // For password reset confirmation
          router.replace({
            pathname: '/reset-password',
            params: { token }
          });
          return;
        } else if (type === 'magiclink') {
          // For magic link login
          console.log('Processing magic link with token:', token);
          try {
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'magiclink',
            });

            if (error) {
              console.error('Error verifying magic link:', error);
              showToast('Error al verificar el enlace mágico', 'error');
              setIsVerifying(false);
              return;
            }

            // Magic link verified successfully
            showToast('Sesión iniciada con éxito', 'success');

            // Check auth status to update the store
            await checkAuth();

            // Redirect to tabs
            router.replace('/(tabs)');
            return;
          } catch (error) {
            console.error('Error processing magic link:', error);
            showToast('Error al procesar el enlace mágico', 'error');
            setIsVerifying(false);
          }
        } else if (type === 'email_change') {
          // For email change confirmation
          console.log('Processing email change with token:', token);
          try {
            const { error } = await supabase.auth.verifyOtp({
              token_hash: token,
              type: 'email_change',
            });

            if (error) {
              console.error('Error verifying email change:', error);
              showToast('Error al verificar el cambio de correo electrónico', 'error');
              setIsVerifying(false);
              return;
            }

            // Email change verified successfully
            setIsSuccess(true);
            showToast('Correo electrónico actualizado con éxito', 'success');

            // Check auth status to update the store
            await checkAuth();
            return;
          } catch (error) {
            console.error('Error processing email change:', error);
            showToast('Error al procesar el cambio de correo electrónico', 'error');
            setIsVerifying(false);
          }
        }
      } catch (error) {
        console.error('Error in verification process:', error);
        showToast('Error en el proceso de verificación', 'error');
      } finally {
        setIsVerifying(false);
      }
    };

    verifyEmail();
  }, [params]);

  const handleContinue = () => {
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.content}>
        {isVerifying ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Verificando tu correo electrónico...</Text>
          </View>
        ) : isSuccess ? (
          <View style={styles.successContainer}>
            <Text style={styles.successTitle}>¡Verificación Exitosa!</Text>
            <Text style={styles.successText}>
              Tu correo electrónico ha sido verificado correctamente. Ahora puedes acceder a todas las funciones de la aplicación.
            </Text>
            <Button
              title="Continuar"
              onPress={handleContinue}
              style={styles.continueButton}
            />
          </View>
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorTitle}>Error de Verificación</Text>
            <Text style={styles.errorText}>
              No pudimos verificar tu correo electrónico. El enlace puede haber expirado o ser inválido.
            </Text>
            <Button
              title="Volver al Inicio de Sesión"
              onPress={() => router.replace('/login')}
              style={styles.loginButton}
            />
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingContainer: {
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
  },
  successContainer: {
    alignItems: 'center',
  },
  successTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.success,
    marginBottom: 16,
  },
  successText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  continueButton: {
    width: '100%',
  },
  errorContainer: {
    alignItems: 'center',
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.error,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 22,
  },
  loginButton: {
    width: '100%',
  },
});
