import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, KeyboardAvoidingView, Platform, ScrollView, Alert } from 'react-native';
import { Link, router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Mail, Lock, Phone } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { useToastStore } from '@/store/toastStore';

export default function RegisterScreen() {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [nickname, setNickname] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { register, isLoading, error, isAuthenticated, currentUser, clearError } = useAuthStore();
  const { showToast } = useToastStore();

  useEffect(() => {
    // If user is already logged in, redirect to home
    if (isAuthenticated && currentUser) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, currentUser]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!firstName) newErrors.firstName = 'El nombre es requerido';
    if (!lastName) newErrors.lastName = 'El apellido es requerido';
    if (!email) newErrors.email = 'El correo es requerido';
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = 'Correo inválido';

    if (!password) newErrors.password = 'La contraseña es requerida';
    else if (password.length < 6) newErrors.password = 'La contraseña debe tener al menos 6 caracteres';

    if (password !== confirmPassword) newErrors.confirmPassword = 'Las contraseñas no coinciden';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const [registrationComplete, setRegistrationComplete] = useState(false);

  const handleRegister = async () => {
    if (!validate()) return;

    try {
      console.log('Submitting registration form with email:', email);

      // Convert all form values to strings to ensure proper formatting
      const emailStr = String(email).trim();
      const passwordStr = String(password);
      const firstNameStr = String(firstName).trim();
      const lastNameStr = String(lastName).trim();
      const nicknameStr = nickname ? String(nickname).trim() : undefined;
      const phoneStr = phoneNumber ? String(phoneNumber).trim() : undefined;

      await register(
        emailStr,
        passwordStr,
        firstNameStr,
        lastNameStr,
        nicknameStr,
        phoneStr
      );

      showToast('Registro exitoso', 'success');
      setRegistrationComplete(true);

      // Show email confirmation instructions
      Alert.alert(
        'Verificación de Correo Electrónico',
        `Hemos enviado un enlace de verificación a ${emailStr}. Por favor, revisa tu correo y haz clic en el enlace para verificar tu cuenta.`,
        [{ text: 'Entendido' }]
      );
    } catch (err) {
      console.error('Registration error in component:', err);
      // Error is already set in the store
    }
  };

  // If registration is complete, show the confirmation instructions screen
  if (registrationComplete) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar style="dark" />
        <View style={styles.confirmationContainer}>
          <Text style={styles.confirmationTitle}>¡Revisa tu Correo!</Text>
          <Text style={styles.confirmationText}>
            Hemos enviado un enlace de verificación a <Text style={styles.emailHighlight}>{email}</Text>.
            Por favor, revisa tu correo y haz clic en el enlace para verificar tu cuenta.
          </Text>
          <Text style={styles.confirmationSubtext}>
            Si no encuentras el correo, revisa tu carpeta de spam o correo no deseado.
          </Text>
          <Button
            title="Volver al Inicio de Sesión"
            onPress={() => router.replace('/login')}
            style={styles.loginButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.header}>
            <Text style={styles.title}>Crear Cuenta</Text>
            <Text style={styles.subtitle}>Únete a Tribu Alfa y conecta con tu comunidad escolar</Text>
          </View>

          <View style={styles.formContainer}>
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <Input
              label="Nombre"
              placeholder="Ingresa tu nombre"
              value={firstName}
              onChangeText={(text) => {
                setFirstName(text);
                if (errors.firstName) setErrors({...errors, firstName: ''});
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.firstName}
            />

            <Input
              label="Apellido"
              placeholder="Ingresa tu apellido"
              value={lastName}
              onChangeText={(text) => {
                setLastName(text);
                if (errors.lastName) setErrors({...errors, lastName: ''});
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.lastName}
            />

            <Input
              label="Apodo (opcional)"
              placeholder="Ingresa tu apodo"
              value={nickname}
              onChangeText={(text) => {
                setNickname(text);
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
            />

            <Input
              label="Correo electrónico"
              placeholder="Ingresa tu correo"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (errors.email) setErrors({...errors, email: ''});
                if (error) clearError();
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon={<Mail size={20} color={colors.textSecondary} />}
              error={errors.email}
            />

            <Input
              label="Contraseña"
              placeholder="Crea una contraseña"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                if (errors.password) setErrors({...errors, password: ''});
                if (error) clearError();
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.textSecondary} />}
              error={errors.password}
            />

            <Input
              label="Confirmar contraseña"
              placeholder="Confirma tu contraseña"
              value={confirmPassword}
              onChangeText={(text) => {
                setConfirmPassword(text);
                if (errors.confirmPassword) setErrors({...errors, confirmPassword: ''});
                if (error) clearError();
              }}
              secureTextEntry
              leftIcon={<Lock size={20} color={colors.textSecondary} />}
              error={errors.confirmPassword}
            />

            <Input
              label="Teléfono (opcional)"
              placeholder="Ingresa tu número de teléfono"
              value={phoneNumber}
              onChangeText={setPhoneNumber}
              keyboardType="phone-pad"
              leftIcon={<Phone size={20} color={colors.textSecondary} />}
            />

            <Button
              title="Registrarse"
              onPress={handleRegister}
              loading={isLoading}
              style={styles.registerButton}
            />

            <View style={styles.loginContainer}>
              <Text style={styles.loginText}>¿Ya tienes una cuenta?</Text>
              <Link href="/login" asChild>
                <TouchableOpacity>
                  <Text style={styles.loginLink}>Iniciar sesión</Text>
                </TouchableOpacity>
              </Link>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  formContainer: {
    width: '100%',
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  registerButton: {
    marginTop: 8,
    marginBottom: 24,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 4,
  },
  loginText: {
    color: colors.textSecondary,
    fontSize: 14,
  },
  loginLink: {
    color: colors.link,
    fontSize: 14,
    fontWeight: '500',
  },
  // Confirmation screen styles
  confirmationContainer: {
    flex: 1,
    padding: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  confirmationTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  confirmationText: {
    fontSize: 16,
    color: colors.text,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  confirmationSubtext: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 32,
  },
  emailHighlight: {
    fontWeight: 'bold',
    color: colors.primary,
  },
});