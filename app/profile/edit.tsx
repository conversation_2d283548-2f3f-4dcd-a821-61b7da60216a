import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, Mail, Phone } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { useToastStore } from '@/store/toastStore';

export default function EditProfileScreen() {
  const { currentUser, updateProfile, isLoading } = useAuthStore();
  const { showToast } = useToastStore();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [nickname, setNickname] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Track if initial values have been set
  const [initialValuesSet, setInitialValuesSet] = useState(false);

  useEffect(() => {
    // Only set initial values once
    if (currentUser && !initialValuesSet) {
      setFirstName(currentUser.firstName);
      setLastName(currentUser.lastName);
      setNickname(currentUser.nickname || '');
      setEmail(currentUser.email);
      setPhone(currentUser.phone || '');
      setInitialValuesSet(true);
    }
  }, [currentUser, initialValuesSet]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!firstName) newErrors.firstName = 'El nombre es requerido';
    if (!lastName) newErrors.lastName = 'El apellido es requerido';
    if (!email) newErrors.email = 'El correo electrónico es requerido';
    else if (!/\S+@\S+\.\S+/.test(email)) newErrors.email = 'Correo electrónico inválido';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateProfile = async () => {
    if (!validate()) return;

    try {
      console.log('Updating profile with:', {
        firstName,
        lastName,
        nickname,
        email,
        phone
      });

      await updateProfile({
        firstName,
        lastName,
        nickname: nickname || undefined,
        email,
        phone
      });

      showToast('Perfil actualizado exitosamente', 'success');
      router.back();
    } catch (error) {
      console.error('Error updating profile:', error);
      showToast('No se pudo actualizar el perfil. Intenta nuevamente.', 'error');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Editar Perfil',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Actualiza tu información personal
            </Text>

            <Input
              label="Nombre"
              placeholder="Ingresa tu nombre"
              value={firstName}
              onChangeText={(text) => {
                setFirstName(text);
                if (errors.firstName) setErrors({...errors, firstName: ''});
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.firstName}
            />

            <Input
              label="Apellido"
              placeholder="Ingresa tu apellido"
              value={lastName}
              onChangeText={(text) => {
                setLastName(text);
                if (errors.lastName) setErrors({...errors, lastName: ''});
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.lastName}
            />

            <Input
              label="Apodo (opcional)"
              placeholder="Ingresa tu apodo"
              value={nickname}
              onChangeText={setNickname}
              leftIcon={<User size={20} color={colors.textSecondary} />}
            />

            <Input
              label="Correo Electrónico"
              placeholder="Ingresa tu correo electrónico"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (errors.email) setErrors({...errors, email: ''});
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              leftIcon={<Mail size={20} color={colors.textSecondary} />}
              error={errors.email}
            />

            <Input
              label="Teléfono (opcional)"
              placeholder="Ingresa tu número de teléfono"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              leftIcon={<Phone size={20} color={colors.textSecondary} />}
            />

            <Button
              title="Guardar Cambios"
              onPress={handleUpdateProfile}
              loading={isLoading}
              style={styles.updateButton}
            />

            <Button
              title="Cancelar"
              onPress={() => router.back()}
              variant="outline"
              style={styles.cancelButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  updateButton: {
    marginTop: 24,
  },
  cancelButton: {
    marginTop: 12,
    marginBottom: 16,
  },
});