import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { DollarSign, CreditCard, Upload } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { Card } from '@/components/Card';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';

export default function PaymentScreen() {
  const { groupId, kidId } = useLocalSearchParams<{ groupId: string; kidId: string }>();
  const { groups, makePayment } = useGroupsStore();
  const { kids } = useKidsStore();
  
  const [amount, setAmount] = useState('1000');
  const [receiptUrl, setReceiptUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const group = groups.find(g => g.id === groupId);
  const kid = kids.find(k => k.id === kidId);
  
  const handlePayment = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      Alert.alert('Error', 'Por favor ingresa un monto válido');
      return;
    }
    
    setIsLoading(true);
    
    try {
      await makePayment(kidId, groupId, parseFloat(amount), receiptUrl || undefined);
      
      Alert.alert(
        'Pago Registrado',
        'Tu pago ha sido registrado y está pendiente de verificación.',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Hubo un problema al procesar el pago. Intenta nuevamente.');
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleUploadReceipt = () => {
    // In a real app, this would open an image picker
    setReceiptUrl('https://example.com/receipt.jpg');
  };
  
  if (!group || !kid) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Realizar Pago' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Información no encontrada</Text>
          <Button 
            title="Volver" 
            onPress={() => router.back()} 
            variant="outline" 
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: 'Realizar Pago',
          headerBackTitle: 'Atrás',
        }} 
      />
      
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.subtitle}>
          Realiza un pago único para acceder al contenido completo del grupo
        </Text>
        
        <Card variant="outlined" style={styles.infoCard}>
          <Text style={styles.infoTitle}>Información del Pago</Text>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Grupo:</Text>
            <Text style={styles.infoValue}>{group.colegio} - {group.sala}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Hijo:</Text>
            <Text style={styles.infoValue}>{kid.fullName}</Text>
          </View>
          
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>Tipo de Pago:</Text>
            <Text style={styles.infoValue}>Acceso al Grupo (Pago Único)</Text>
          </View>
        </Card>
        
        <View style={styles.formSection}>
          <Input
            label="Monto a Pagar (ARS)"
            placeholder="Ingresa el monto"
            value={amount}
            onChangeText={setAmount}
            keyboardType="numeric"
            leftIcon={<DollarSign size={20} color={colors.textSecondary} />}
          />
          
          <View style={styles.receiptSection}>
            <Text style={styles.receiptLabel}>Comprobante de Pago</Text>
            <Text style={styles.receiptInfo}>
              Sube una captura de pantalla o foto de tu comprobante de pago
            </Text>
            
            <Button
              title={receiptUrl ? "Cambiar Comprobante" : "Subir Comprobante"}
              onPress={handleUploadReceipt}
              variant="outline"
              icon={<Upload size={20} color={colors.primary} />}
              style={styles.uploadButton}
            />
            
            {receiptUrl && (
              <Text style={styles.receiptUploaded}>
                Comprobante subido correctamente
              </Text>
            )}
          </View>
          
          <View style={styles.paymentMethods}>
            <Text style={styles.paymentMethodsTitle}>Métodos de Pago</Text>
            <Text style={styles.paymentMethodsInfo}>
              Puedes realizar el pago mediante transferencia bancaria o Mercado Pago a la siguiente cuenta:
            </Text>
            
            <Card variant="outlined" style={styles.accountCard}>
              <View style={styles.accountInfo}>
                <CreditCard size={20} color={colors.primary} />
                <Text style={styles.accountTitle}>Cuenta para Transferencia</Text>
              </View>
              
              <Text style={styles.accountDetail}>Banco: Banco de la Nación Argentina</Text>
              <Text style={styles.accountDetail}>Titular: Asociación Escolar</Text>
              <Text style={styles.accountDetail}>CBU: 0110012930001234567890</Text>
              <Text style={styles.accountDetail}>Alias: ESCUELA.PAGO.GRUPO</Text>
            </Card>
          </View>
        </View>
        
        <Button
          title="Confirmar Pago"
          onPress={handlePayment}
          loading={isLoading}
          style={styles.confirmButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  infoCard: {
    marginBottom: 24,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    width: 100,
  },
  infoValue: {
    fontSize: 14,
    color: colors.text,
    flex: 1,
  },
  formSection: {
    marginBottom: 24,
  },
  receiptSection: {
    marginBottom: 24,
  },
  receiptLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  receiptInfo: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  uploadButton: {
    alignSelf: 'flex-start',
  },
  receiptUploaded: {
    fontSize: 14,
    color: colors.success,
    marginTop: 8,
  },
  paymentMethods: {
    marginBottom: 24,
  },
  paymentMethodsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  paymentMethodsInfo: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
    lineHeight: 20,
  },
  accountCard: {
    padding: 16,
  },
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  accountTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
  },
  accountDetail: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 4,
  },
  confirmButton: {
    marginBottom: 24,
  },
});