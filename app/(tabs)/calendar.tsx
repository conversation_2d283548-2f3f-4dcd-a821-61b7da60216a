import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { useThemeColor } from '../../components/Themed';
import { Calendar, Event, EventType } from '../../components/Calendar';
import { FilterBar } from '../../components/FilterBar';
import { supabase } from '@/lib/supabase';
import { useAuthStore } from '@/store/authStore';
import { useFocusEffect, useNavigation, usePathname, useLocalSearchParams, Stack } from 'expo-router';
import { Button } from '@/components/Button';
import { FontAwesome } from '@expo/vector-icons';
import { Calendar as CalendarIcon } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useRouter } from 'expo-router';

// All event types for default selection
const ALL_EVENT_TYPES: EventType[] = [
  'birthday',
  'reunion',
  'actividad',
  'feriado',
  'examen',
  'otro',
  'colecta',
  'votacion',
];

// Define EVENT_COLORS to be used for getEventColor
const EVENT_COLORS: Record<EventType, string> = {
  birthday: '#FF69B4',
  reunion: '#4CAF50',
  actividad: '#2196F3',
  feriado: '#FF9800',
  examen: '#F44336',
  otro: '#9C27B0',
  colecta: '#00BCD4',
  votacion: '#795548',
};

// Add getEventColor function
const getEventColor = (type: EventType): string => {
  return EVENT_COLORS[type] || '#9E9E9E'; // Default color if type not found
};

// Add a mapping function to convert database event types to calendar event types
const mapEventType = (dbType: string): EventType => {
  if (!dbType) {
    console.log('WARNING: Null or undefined event type received');
    return 'otro'; // Default fallback
  }
  
  // Convert to uppercase for consistent comparison
  const typeUpper = dbType.toUpperCase();
  const typeLower = dbType.toLowerCase();
  
  console.log(`DEBUG: Mapping event type from DB "${dbType}" (uppercase: "${typeUpper}", lowercase: "${typeLower}")`);
  
  // Map between database types and calendar component types using uppercase for standardization
  if (typeUpper === 'HOLIDAY') return 'feriado';  
  if (typeUpper === 'MEETING') return 'reunion';
  if (typeUpper === 'ACTIVITY') return 'actividad';
  if (typeUpper === 'EXAM') return 'examen';
  if (typeUpper === 'OTHER') return 'otro';
  if (typeUpper === 'COLLECTION') return 'colecta';
  if (typeUpper === 'VOTE') return 'votacion';
  
  // If it's already a valid EventType (in lowercase), return it
  if (ALL_EVENT_TYPES.includes(typeLower as EventType)) {
    return typeLower as EventType;
  }
  
  // Last attempt - check if the uppercase version matches one of our expected types
  console.log(`DEBUG: No direct match found for "${dbType}", checking normalized versions`);
  const knownTypes: Record<string, EventType> = {
    'FERIADO': 'feriado',
    'REUNION': 'reunion',
    'ACTIVIDAD': 'actividad',
    'EXAMEN': 'examen',
    'OTRO': 'otro',
    'COLECTA': 'colecta',
    'VOTACION': 'votacion',
    'BIRTHDAY': 'birthday'
  };
  
  if (knownTypes[typeUpper]) {
    console.log(`DEBUG: Found match in known types: ${typeUpper} -> ${knownTypes[typeUpper]}`);
    return knownTypes[typeUpper];
  }
  
  // Default fallback
  console.log(`DEBUG: Unrecognized event type "${dbType}", defaulting to "otro"`);
  return 'otro';
};

export default function CalendarScreen() {
  const { currentUser } = useAuthStore();
  const router = useRouter();
  if (!currentUser) {
    router.replace('/(auth)/login');
    return null;
  }
  const [kidIds, setKidIds] = useState<string[]>([]);
  const [groupIds, setGroupIds] = useState<string[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'week' | 'month'>('month');
  const [events, setEvents] = useState<Event[]>([]);
  const [calendarKey, setCalendarKey] = useState(Date.now());
  const [selectedFilters, setSelectedFilters] = useState({
    kids: [] as string[],
    groups: [] as string[],
    eventTypes: ALL_EVENT_TYPES as string[],
  });
  const [loading, setLoading] = useState(false);
  const backgroundColor = useThemeColor({ light: '#fff', dark: '#000' }, 'background');
  const initialLoadDone = useRef(false);
  const isMounted = useRef(true);
  const navigation = useNavigation();
  const pathname = usePathname();
  const lastTabPressTime = useRef(0);
  const params = useLocalSearchParams();

  // Load all available filter options and set them as selected by default
  const loadFilterOptions = async () => {
    if (!currentUser) return;

    try {
      const { data: userGroups } = await supabase
        .from('group_memberships')
        .select('group_id')
        .eq('user_id', currentUser.id);

      const allGroupIds = userGroups?.map(g => g.group_id).filter(Boolean) || [];
      console.log('Setting selectedFilters.groups:', allGroupIds);

      setSelectedFilters({
        kids: [],
        groups: allGroupIds,
        eventTypes: ALL_EVENT_TYPES as string[],
      });
      // Do NOT call fetchEvents here anymore
    } catch (error) {
      console.error('Error loading filter options:', error);
    }
  };

  // Reset and refresh data when tab is clicked or reset param is present
  const resetData = useCallback(() => {
    console.log('Calendar reset triggered');
    initialLoadDone.current = false;
    
    // Reset to current date and default view mode
    const currentDate = new Date();
    setSelectedDate(currentDate);
    setViewMode('month');
    setCalendarKey(Date.now());
    
    // Re-load options and data
    loadFilterOptions().then(() => {
      fetchEvents();
    });
  }, []);

  // Watch for reset parameter
  useEffect(() => {
    if (params._t) {
      console.log('Reset parameter detected, resetting calendar');
      resetData();
    }
  }, [params._t, resetData]);

  // Listen for tab press events
  useEffect(() => {
    if (!navigation) return;
    
    // Use @ts-ignore to bypass the type checking for the tabPress event
    // This is necessary because expo-router doesn't export the correct event types
    // @ts-ignore
    const unsubscribe = navigation.addListener('tabPress', (e) => {
      // Only proceed if we're already on the calendar tab
      if (pathname === '/calendar') {
        // Debounce to prevent multiple rapid clicks
        const now = Date.now();
        if (now - lastTabPressTime.current < 500) return;
        lastTabPressTime.current = now;
        
        // Don't prevent default navigation - we want the tab to be activated normally
        // but also perform our reset
        resetData();
      }
    });

    return unsubscribe;
  }, [navigation, pathname, resetData]);

  // Only fetch data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      isMounted.current = true;
      
      // Only fetch once when the screen is first focused
      if (currentUser && !initialLoadDone.current) {
        loadFilterOptions().then(() => {
          fetchEvents();
        });
        initialLoadDone.current = true;
      }
      
      return () => {
        // Component is unfocused
        isMounted.current = false;
      };
    }, [currentUser])
  );

  const handleFiltersChange = useCallback((filters: typeof selectedFilters) => {
    // Defensive: If groups is empty, keep previous value
    setSelectedFilters(prev => {
      const newGroups = filters.groups && filters.groups.length > 0 ? filters.groups : prev.groups;
      if (!filters.groups || filters.groups.length === 0) {
        console.warn('handleFiltersChange: Prevented clearing groups, keeping previous groups:', prev.groups);
      }
      return {
        ...prev,
        ...filters,
        groups: newGroups,
      };
    });
  }, []);

  const handleDateSelect = useCallback((date: Date) => {
    if (!isMounted.current) return;
    setSelectedDate(date);
    // Do NOT fetch or filter events here!
  }, []);

  const handleViewModeChange = useCallback((mode: 'week' | 'month') => {
    if (!isMounted.current) return;
    setViewMode(mode);
  }, []);

  const fetchEvents = async () => {
    if (!currentUser) return;
    if (!selectedFilters.groups || selectedFilters.groups.length === 0) {
      console.warn('Not fetching events: selectedFilters.groups is empty');
      setEvents([]);
      setLoading(false);
      return;
    }
    setLoading(true);

    try {
      console.log('DEBUG: selectedFilters.groups', selectedFilters.groups);
      if (!selectedFilters.groups || selectedFilters.groups.length === 0) {
        console.warn('WARNING: No group IDs in selectedFilters.groups!');
      }
      if (selectedFilters.groups.some(g => !g)) {
        console.warn('WARNING: Undefined group ID in selectedFilters.groups:', selectedFilters.groups);
      }
      // Fetch events from selected groups
      const { data: eventResults, error: eventError } = await supabase
        .from('events')
        .select('*')
        .in('group_id', selectedFilters.groups);

      console.log('DEBUG: eventResults', eventResults);
      console.log('DEBUG: eventError', eventError);
      if (!eventResults || eventResults.length === 0) {
        console.warn('WARNING: No events returned from Supabase for these group IDs:', selectedFilters.groups);
      }

      // Fetch kids from selected groups and build (kid, group) pairs
      const { data: groupKids, error: kidsError } = await supabase
        .from('group_memberships')
        .select('kid_id, group_id')
        .in('group_id', selectedFilters.groups)
        .not('kid_id', 'is', null);

      if (kidsError) throw kidsError;

      // Build a list of all (kidId, groupId) pairs
      const kidGroupPairs: Array<{ kidId: string, groupId: string }> = (groupKids || []).map(gk => ({
        kidId: gk.kid_id,
        groupId: gk.group_id
      }));

      // Get unique kid IDs
      const kidIds = [...new Set(kidGroupPairs.map(pair => pair.kidId))];

      // Fetch kid details
      const { data: kidsData, error: kidsDataError } = await supabase
        .from('kids')
        .select('id, full_name, birth_date')
        .in('id', kidIds);

      if (kidsDataError) throw kidsDataError;

      // Create birthday events for each (kid, group) pair
      const birthdayEvents = kidGroupPairs.flatMap(({ kidId, groupId }) => {
        const kid = kidsData.find(k => k.id === kidId);
        if (!kid) return [];
        // Parse the birth date and create a date string in YYYY-MM-DD format
        const birthDate = new Date(kid.birth_date);
        const currentYear = new Date().getFullYear();
        const birthdayDate = new Date(currentYear, birthDate.getMonth(), birthDate.getDate());
        const formattedDate = birthdayDate.toISOString().split('T')[0];
        return [{
          id: `birthday-${kid.id}-${groupId}`,
          title: `🎂 ${kid.full_name}`,
          date: formattedDate,
          type: 'birthday' as EventType,
          kidId: kid.id,
          groupId,
          isFullDay: true
        }];
      });

      // Combine regular events with birthday events
      const allEvents = [
        ...(eventResults?.map(event => {
          const mappedType = mapEventType(event.type);
          console.log(`DEBUG: Event "${event.title}" type "${event.type}" mapped to "${mappedType}"`);
          return {
            ...event,
            id: event.id,
            title: event.title,
            date: event.date.split('T')[0], // Ensure date is in YYYY-MM-DD format
            type: mappedType, // Use mapping function for event type
            groupId: event.group_id,
            isFullDay: true
          };
        }) || []),
        ...birthdayEvents
      ];

      console.log('DEBUG: allEvents', allEvents);
      setEvents(allEvents);
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const markedDates = useMemo(() => {
    const marked: { [key: string]: any } = {};
    
    console.log("Building marked dates from events:", events.length);
    if (events.length > 0) {
      console.log("Event types:", events.map(e => `${e.title}: ${e.type}`).join(", "));
      console.log("Event filter types:", selectedFilters.eventTypes);
    }
    
    events.forEach(event => {
      try {
        if (!event.date) {
          console.log(`Skipping event with no date: ${event.title}`);
          return;
        }
        
        // Normalize event type for comparison
        const eventType = typeof event.type === 'string' ? event.type.toLowerCase() : event.type;
        
        // For birthdays, always show them if they're in the selected groups
        const isBirthday = eventType === 'birthday';
        
        // Group filter: only apply if event has a groupId and filter is not empty
        const groupFilter = event.groupId
          ? (selectedFilters.groups.length === 0 || selectedFilters.groups.includes(event.groupId))
          : true;
        
        // Type filter: as before
        const typeFilter = selectedFilters.eventTypes.length === 0 || 
                       selectedFilters.eventTypes.map(t => t.toLowerCase()).includes(eventType);
        
        console.log(`Marking date check for ${event.title}: groupFilter=${groupFilter}, typeFilter=${typeFilter}`);
        console.log(`  -> event date: ${event.date}, type: ${event.type}, normalized type: ${eventType}, groupId: ${event.groupId}`);
        
        if (groupFilter && typeFilter) {
          // Mark the date
          console.log(`MARKING DATE for: ${event.title}, date: ${event.date}, type: ${eventType}`);
          
          marked[event.date] = {
            marked: true,
            dotColor: getEventColor(eventType as EventType),
          };
        } else {
          console.log(`Event filtered out from calendar dots: ${event.title}, type: ${eventType}`);
        }
      } catch (e) {
        console.error(`Error marking date for event ${event.title}:`, e);
      }
    });

    console.log("Final marked dates:", Object.keys(marked));
    return marked;
  }, [events, selectedFilters]);

  // Add a debug button to check raw database events
  useEffect(() => {
    const checkRawEvents = async () => {
      try {
        console.log("DEBUG: Fetching all events directly from DB for inspection");
        const { data, error } = await supabase
          .from('events')
          .select('id, title, type, date, group_id')
          .order('date', { ascending: false })
          .limit(20);
          
        if (error) {
          console.error("ERROR fetching events:", error);
        } else {
          console.log("DEBUG: Raw events from DB:", data);
          data.forEach(event => {
            console.log(`- Event ID ${event.id}: "${event.title}", type=${event.type}, date=${event.date}`);
          });
        }
      } catch (err) {
        console.error("Error in raw events check:", err);
      }
    };
    
    // Run this check once when the component mounts
    checkRawEvents();
  }, []);

  // useEffect to fetch events only when selectedFilters.groups is set and not empty
  useEffect(() => {
    if (selectedFilters.groups && selectedFilters.groups.length > 0) {
      console.log('Triggering fetchEvents with groups:', selectedFilters.groups);
      fetchEvents();
    } else {
      console.warn('Not fetching events: selectedFilters.groups is empty or not set');
      setEvents([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFilters.groups && selectedFilters.groups.join(",")]);

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <Stack.Screen 
        options={{
          title: 'Calendario',
          headerLeft: () => (
            <View style={{ marginLeft: 16 }}>
              <FontAwesome name="calendar" size={24} color={colors.text} />
            </View>
          ),
        }} 
      />
      <FilterBar
        onFiltersChange={handleFiltersChange}
        selectedFilters={selectedFilters}
        showEventTypeFilters={false}
        showKidsFilter={false}
      />
      <Calendar
        key={calendarKey}
        selectedDate={selectedDate}
        onDateSelect={handleDateSelect}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        filters={selectedFilters}
        events={events}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  viewToggle: {
    padding: 10,
    backgroundColor: '#00adf5',
    borderRadius: 5,
  },
  viewToggleText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
  },
  applyButtonContainer: {
    marginVertical: 10,
  },
  applyButton: {
    marginHorizontal: 5,
  }
}); 