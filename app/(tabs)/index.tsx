import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl, FlatList, Alert, Image } from 'react-native';
import { router } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Users, UserRound, Bell, School, Calendar as CalendarIcon, Filter, RefreshCw } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAnnouncementsStore } from '@/store/announcementsStore';
import { useColectasStore } from '@/store/colectasStore';
import { usePollsStore } from '@/store/pollsStore';
import { useEventsStore } from '@/store/eventsStore';
import { useNotificationsStore } from '@/store/notificationsStore';
import { useToastStore } from '@/store/toastStore';
import { clearAllCaches } from '@/utils/cacheUtils';
import { GroupCard } from '@/components/GroupCard';
import { KidCard } from '@/components/KidCard';
import { Card } from '@/components/Card';
import { EmptyState } from '@/components/EmptyState';
import { Button } from '@/components/Button';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { CalendarView } from '@/components/CalendarView';
import { NotificationItem } from '@/components/NotificationItem';
import { Badge } from '@/components/Badge';

export default function HomeScreen() {
  const { currentUser, isAuthenticated, checkAuth, logout } = useAuthStore();

  // Show loading state while authentication is being verified
  if (!currentUser) {
    router.replace('/(auth)/login');
    return (
      <View style={styles.loadingContainer}>
        {/* Plain white screen while redirecting */}
      </View>
    );
  }

  // Only initialize store hooks after authentication is verified
  const { myGroups, memberships, getGroups, isLoading: groupsLoading } = useGroupsStore();
  const { kids, getKids, isLoading: kidsLoading } = useKidsStore();
  const { announcements, getAnnouncementsByGroupId } = useAnnouncementsStore();
  const { colectas, getColectasByGroupId } = useColectasStore();
  const { polls, getPollsByGroupId } = usePollsStore();
  const { events, getEventsByGroupId } = useEventsStore();
  const { notifications, getNotifications, markAsRead, markAllAsRead } = useNotificationsStore();
  const { showToast } = useToastStore();

  // Track if initial load has been completed
  const [initialLoadCompleted, setInitialLoadCompleted] = useState(false);

  // Check authentication once when component mounts
  useEffect(() => {
    let isMounted = true;

    const verifyAuth = async () => {
      console.log('HomeScreen: Verifying authentication');

      try {
        // Always check auth on mount to ensure we have the latest state
        await checkAuth();

        // If component was unmounted during auth check, don't proceed
        if (!isMounted) return;

        // If not authenticated after checking, redirect to login
        if (!isAuthenticated || !currentUser || !currentUser.id) {
          console.log('HomeScreen: Not authenticated after check, redirecting to login');
          // Use replace to avoid navigation stack issues
          router.replace('/(auth)/login');
          return;
        }

        console.log('HomeScreen: Authentication verified', {
          userId: currentUser?.id,
          email: currentUser?.email
        });

        // Now that we're authenticated, load data with force refresh
        await loadData(true);
        setInitialLoadCompleted(true);
      } catch (error) {
        console.error('HomeScreen: Auth verification error:', error);
        // On error, force logout and redirect
        if (isMounted) {
          try {
            // First navigate to login to prevent white screen
            router.replace('/(auth)/login');
            // Then perform logout
            await logout();
          } catch (logoutError) {
            console.error('HomeScreen: Error during logout:', logoutError);
          }
        }
      }
    };

    verifyAuth();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, []);

  // Refresh data when screen comes into focus (but only after initial load)
  useFocusEffect(
    useCallback(() => {
      // Only refresh on focus if we've completed the initial load
      // and we're not already in the middle of loading
      if (initialLoadCompleted && currentUser && isAuthenticated && !refreshing) {
        console.log('HomeScreen: Screen focused, refreshing data');
        loadData(false); // Don't force refresh on focus, use cache if recent
      }
    }, [currentUser, isAuthenticated, initialLoadCompleted, refreshing])
  );

  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState<'home' | 'notifications' | 'calendar'>('home');
  const [selectedGroups, setSelectedGroups] = useState<string[]>([]);
  const [showGroupFilter, setShowGroupFilter] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Add a timestamp to track when data was last loaded
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  // Minimum time between loads in milliseconds (5 seconds)
  const MIN_LOAD_INTERVAL = 5000;
  // Track if calendar data has been loaded
  const [calendarDataLoaded, setCalendarDataLoaded] = useState(false);
  // Track if data is currently being loaded to prevent concurrent calls
  const [isLoadingData, setIsLoadingData] = useState(false);

  // Load calendar data when calendar tab is accessed
  useEffect(() => {
    if (activeTab === 'calendar' && !calendarDataLoaded && myGroups && myGroups.length > 0) {
      console.log('HomeScreen: Calendar tab accessed, loading calendar data...');
      loadCalendarData().then(() => {
        setCalendarDataLoaded(true);
      });
    }
  }, [activeTab, myGroups, calendarDataLoaded]);

  const loadData = async (forceRefresh = false) => {
    // Prevent concurrent calls
    if (isLoadingData) {
      console.log('HomeScreen: Data load already in progress, skipping');
      return;
    }

    // Check if we've loaded data recently (within the last 5 seconds)
    const now = Date.now();
    if (!forceRefresh && lastLoadTime > 0 && now - lastLoadTime < MIN_LOAD_INTERVAL) {
      console.log('HomeScreen: Skipping data load, too soon since last load');
      return;
    }

    console.log(`HomeScreen: Loading data... (forceRefresh: ${forceRefresh})`);
    setIsLoadingData(true);

    try {
      // Update last load time immediately to prevent multiple concurrent loads
      setLastLoadTime(now);

      // Fetch only essential data for home screen
      const fetchPromises = [
        getGroups(forceRefresh),
        getKids(forceRefresh),
        getNotifications(forceRefresh)
      ];

      // Wait for all initial data to load
      await Promise.all(fetchPromises);

      // Mark data as loaded
      setDataLoaded(true);
      setInitialLoading(false);
      console.log('HomeScreen: Essential data loaded successfully');
    } catch (error) {
      console.error('HomeScreen: Error loading data:', error);
      setInitialLoading(false); // Stop loading even on error
    } finally {
      setIsLoadingData(false);
    }
  };

  // Load calendar data only when calendar tab is accessed
  const loadCalendarData = async (forceRefresh = false) => {
    console.log('HomeScreen: Loading calendar data...');
    try {
      // After loading groups, load data for each group for calendar
      if (myGroups && myGroups.length > 0) {
        const groupDataPromises = [];

        // Fetch data for each group
        myGroups.forEach(group => {
          groupDataPromises.push(getAnnouncementsByGroupId(group.id));
          groupDataPromises.push(getColectasByGroupId(group.id));
          groupDataPromises.push(getPollsByGroupId(group.id));
          groupDataPromises.push(getEventsByGroupId(group.id));
        });

        // Wait for all group data to load
        if (groupDataPromises.length > 0) {
          await Promise.all(groupDataPromises);
        }
      }
      console.log('HomeScreen: Calendar data loaded successfully');
    } catch (error) {
      console.error('HomeScreen: Error loading calendar data:', error);
    }
  };

  const onRefresh = async () => {
    console.log('HomeScreen: Manual refresh triggered');
    setRefreshing(true);
    // Force a refresh of all data
    await loadData(true);
    // If we're on calendar tab, also refresh calendar data
    if (activeTab === 'calendar') {
      await loadCalendarData(true);
    }
    setRefreshing(false);
  };

  const handleForceRefresh = async () => {
    try {
      // Show confirmation dialog
      Alert.alert(
        "Forzar actualización",
        "¿Estás seguro que quieres limpiar todo el caché y actualizar los datos?",
        [
          { text: "Cancelar", style: "cancel" },
          {
            text: "Actualizar",
            onPress: async () => {
              setRefreshing(true);
              // Clear all caches
              await clearAllCaches();
              // Reset calendar data loaded flag
              setCalendarDataLoaded(false);
              // Fetch fresh data
              await loadData(true);
              // If we're on calendar tab, also refresh calendar data
              if (activeTab === 'calendar') {
                await loadCalendarData(true);
                setCalendarDataLoaded(true);
              }
              setRefreshing(false);
              showToast({
                message: "Todos los datos actualizados correctamente",
                type: "success"
              });
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error forcing refresh:', error);
      showToast({
        message: "Error al actualizar los datos",
        type: "error"
      });
      setRefreshing(false);
    }
  };

  // Helper function to get member count for a group
  const getMemberCount = (groupId: string) => {
    const group = myGroups ? myGroups.find(g => g.id === groupId) : null;
    return group && group.members ? group.members.length : 0;
  };

  // Helper function to get kid count for a group
  const getKidCount = (groupId: string) => {
    // Get the group from the store
    const group = myGroups ? myGroups.find(g => g.id === groupId) : null;

    // If the group has a kids array, use its length
    if (group && group.kids) {
      return group.kids.length;
    }

    // Fallback: Get all memberships for this group
    const groupMemberships = memberships ? memberships.filter(m => m.groupId === groupId) : [];

    // Count unique kids with valid kidId
    const uniqueKidIds = new Set();
    groupMemberships.forEach(membership => {
      if (membership.kidId) {
        uniqueKidIds.add(membership.kidId);
      }
    });

    return uniqueKidIds.size;
  };

  // Filter notifications by selected groups
  const filteredNotifications = notifications.filter(notification => {
    if (selectedGroups.length === 0) return true;
    return selectedGroups.includes(notification.groupId || '');
  });

  // Toggle group selection for filtering
  const toggleGroupSelection = (groupId: string) => {
    if (selectedGroups.includes(groupId)) {
      setSelectedGroups(selectedGroups.filter(id => id !== groupId));
    } else {
      setSelectedGroups([...selectedGroups, groupId]);
    }
  };

  // Get unread notifications count
  const unreadCount = notifications.filter(n => n.status === 'UNREAD').length;

  // Render the home tab content
  const renderHomeContent = () => (
    <ScrollView
      contentContainerStyle={styles.scrollContent}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Hola, {currentUser?.fullName.split(' ')[0] || 'Usuario'}</Text>
          <Text style={styles.subtitle}>Bienvenido</Text>
        </View>
      </View>

      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Mis Grupos</Text>
        </View>

        {groupsLoading && !myGroups?.length ? (
          <Text style={styles.loadingText}>Cargando grupos...</Text>
        ) : myGroups && myGroups.length > 0 ? (
          <View style={styles.verticalList}>
            {myGroups.map((item) => (
              <GroupCard
                key={item.id}
                institutionName={item.institutionName || 'Institución'}
                sala={item.sala}
                año={item.año}
                nombre={item.nombre}
                memberCount={getMemberCount(item.id)}
                kidCount={getKidCount(item.id)}
                onPress={() => router.push(`/group/${item.id}`)}
                style={styles.groupCard}
              />
            ))}
          </View>
        ) : (
          <EmptyState
            icon={<Users size={40} color={colors.textSecondary} />}
            title="No tienes grupos"
            message="Crea o únete a un grupo para conectar con otros padres y madres"
            actionButton={
              <Button
                title="Crear Grupo"
                onPress={() => router.push('/group/create')}
                icon={<Plus size={18} color="#fff" />}
              />
            }
          />
        )}
      </View>
    </ScrollView>
  );

  // Render the notifications tab content
  const renderNotificationsContent = () => (
    <View style={styles.tabContent}>
      <View style={styles.tabHeader}>
        <Text style={styles.tabTitle}>Notificaciones</Text>
        <View style={styles.tabActions}>
          {unreadCount > 0 && (
            <TouchableOpacity
              style={styles.markAllButton}
              onPress={() => markAllAsRead()}
            >
              <Text style={styles.markAllText}>Marcar todas como leídas</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowGroupFilter(!showGroupFilter)}
          >
            <Filter size={20} color={selectedGroups.length > 0 ? colors.primary : colors.text} />
          </TouchableOpacity>
        </View>
      </View>

      {showGroupFilter && myGroups && myGroups.length > 0 && (
        <View style={styles.filterContainer}>
          <Text style={styles.filterTitle}>Filtrar por grupo:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterList}>
            {myGroups.map(group => (
              <TouchableOpacity
                key={group.id}
                style={[
                  styles.filterChip,
                  selectedGroups.includes(group.id) && styles.filterChipSelected
                ]}
                onPress={() => toggleGroupSelection(group.id)}
              >
                <Text
                  style={[
                    styles.filterChipText,
                    selectedGroups.includes(group.id) && styles.filterChipTextSelected
                  ]}
                >
                  {group.nombre || `${group.sala} - ${group.año}`}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}

      <FlatList
        data={filteredNotifications.sort((a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        )}
        keyExtractor={item => item.id}
        renderItem={({ item }) => (
          <NotificationItem
            message={item.message}
            type={item.type}
            createdAt={item.createdAt}
            isRead={item.status === 'READ'}
            groupName={myGroups?.find(g => g.id === item.groupId)?.nombre || ''}
            onPress={() => markAsRead(item.id)}
          />
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <EmptyState
            title="No tienes notificaciones"
            message="Las notificaciones aparecerán aquí"
            icon={<Bell size={40} color={colors.textSecondary} />}
          />
        }
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      />
    </View>
  );

  // Render the calendar tab content
  const renderCalendarContent = () => {
    // Filter announcements, colectas, polls and events by selected groups
    const filteredAnnouncements = announcements ? announcements.filter(a =>
      selectedGroups.length === 0 || selectedGroups.includes(a.groupId)
    ) : [];

    const filteredColectas = colectas ? colectas.filter(c =>
      selectedGroups.length === 0 || selectedGroups.includes(c.groupId)
    ) : [];

    const filteredEvents = events ? events.filter(e =>
      selectedGroups.length === 0 || selectedGroups.includes(e.groupId)
    ) : [];

    return (
      <View style={styles.tabContent}>
        <View style={styles.tabHeader}>
          <Text style={styles.tabTitle}>Calendario</Text>
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setShowGroupFilter(!showGroupFilter)}
          >
            <Filter size={20} color={selectedGroups.length > 0 ? colors.primary : colors.text} />
          </TouchableOpacity>
        </View>

        {showGroupFilter && myGroups && myGroups.length > 0 && (
          <View style={styles.filterContainer}>
            <Text style={styles.filterTitle}>Filtrar por grupo:</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterList}>
              {myGroups.map(group => (
                <TouchableOpacity
                  key={group.id}
                  style={[
                    styles.filterChip,
                    selectedGroups.includes(group.id) && styles.filterChipSelected
                  ]}
                  onPress={() => toggleGroupSelection(group.id)}
                >
                  <Text
                    style={[
                      styles.filterChipText,
                      selectedGroups.includes(group.id) && styles.filterChipTextSelected
                    ]}
                  >
                    {group.nombre || `${group.sala} - ${group.año}`}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        <ScrollView
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
        >
          <CalendarView
            groupId="all"
            announcements={filteredAnnouncements}
            colectas={filteredColectas}
            kids={kids || []}
            groups={myGroups || []}
            events={filteredEvents}
          />
        </ScrollView>
      </View>
    );
  };

  // Render the appropriate content based on the active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'notifications':
        return renderNotificationsContent();
      case 'calendar':
        return renderCalendarContent();
      default:
        return renderHomeContent();
    }
  };

  // Show loading screen while initial data is loading
  if (initialLoading) {
    return (
      <View style={styles.loadingContainer}>
        {/* Plain white screen while loading initial data */}
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <AnimatedTransition>
        {activeTab !== 'home' && (
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setActiveTab('home')}
          >
            <Text style={styles.backButtonText}>← Volver al inicio</Text>
          </TouchableOpacity>
        )}
        {renderContent()}
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 4,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: colors.link,
  },
  verticalList: {
    width: '100%',
  },
  groupCard: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
  // Tab styles
  tabContent: {
    flex: 1,
  },
  tabHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.text,
  },
  tabActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  markAllButton: {
    padding: 8,
  },
  markAllText: {
    fontSize: 14,
    color: colors.primary,
  },
  filterButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: colors.cardBackground,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  filterTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  filterList: {
    flexDirection: 'row',
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.cardBackground,
    marginRight: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  filterChipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterChipText: {
    fontSize: 14,
    color: colors.text,
  },
  filterChipTextSelected: {
    color: colors.background,
  },
  listContent: {
    flexGrow: 1,
  },
  backButton: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButtonText: {
    fontSize: 16,
    color: colors.link,
  },
});