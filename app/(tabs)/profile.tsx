import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  User,
  Settings,
  Edit,
  LogOut,
  Mail,
  Phone,
  ChevronRight,
  HelpCircle
} from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Avatar } from '@/components/Avatar';
import { colors } from '@/constants/colors';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { useKidsStore } from '@/store/kidsStore';
import { KidCard } from '@/components/KidCard';
import { useGroupsStore } from '@/store/groupsStore';
import { KidsService } from '@/services/kidsService';
import { AuthService } from '@/services/authService';

export default function ProfileScreen() {
  const { currentUser, logout } = useAuthStore();
  if (!currentUser) {
    router.replace('/(auth)/login');
    return null;
  }
  const { kids, getKids, isLoading } = useKidsStore();
  const { allGroups } = useGroupsStore();
  const [kidParents, setKidParents] = useState<Record<string, User[]>>({});

  useEffect(() => {
    const fetchAllParents = async () => {
      if (!kids || kids.length === 0) return;
      const kidToParents: Record<string, User[]> = {};
      for (const kid of kids) {
        try {
          const parentIds = await KidsService.getParentsForKid(kid.id);
          if (parentIds.length > 0) {
            const { data: users, error } = await AuthService.getUsersByIds(parentIds);
            if (!error && users) {
              kidToParents[kid.id] = users;
            } else {
              kidToParents[kid.id] = [];
            }
          } else {
            kidToParents[kid.id] = [];
          }
        } catch (e) {
          kidToParents[kid.id] = [];
        }
      }
      setKidParents(kidToParents);
    };
    fetchAllParents();
  }, [kids]);

  const handleLogout = () => {
    Alert.alert(
      'Cerrar sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cerrar sesión',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/(auth)/login');
            } catch (error) {
              console.error('Logout error:', error);
              // Even if logout fails, redirect to login
              router.replace('/(auth)/login');
            }
          }
        }
      ]
    );
  };

  // Helper to get full group objects for a kid
  const getGroupsForKid = (kid) => {
    if (!kid.groups || !Array.isArray(kid.groups)) return [];
    return allGroups.filter(group => kid.groups.includes(group.id));
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Mi Perfil',
        }}
      />

      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.profileHeader}>
            <Avatar
              initials={currentUser?.fullName.split(' ').map(n => n[0]).join('').substring(0, 2) || 'U'}
              size={80}
              backgroundColor={colors.primary}
            />

            <TouchableOpacity
              style={styles.editButton}
              onPress={() => router.push('/profile/edit')}
            >
              <Edit size={20} color={colors.primary} />
            </TouchableOpacity>
          </View>

          <Text style={styles.userName}>{currentUser?.fullName || 'Usuario'}</Text>
          {currentUser?.nickname && <Text style={styles.userNickname}>({currentUser.nickname})</Text>}

          <Card variant="outlined" style={styles.infoCard}>
            <View style={styles.infoItem}>
              <Mail size={20} color={colors.textSecondary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Correo Electrónico</Text>
                <Text style={styles.infoValue}>{currentUser?.email || 'No disponible'}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <Phone size={20} color={colors.textSecondary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Teléfono</Text>
                <Text style={styles.infoValue}>{currentUser?.phone || 'No disponible'}</Text>
              </View>
            </View>
          </Card>

          <View style={styles.actionsSection}>
            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => router.push('/settings')}
            >
              <View style={styles.actionInfo}>
                <Settings size={20} color={colors.text} />
                <Text style={styles.actionText}>Configuración</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.actionItem}
              onPress={() => router.push('/help')}
            >
              <View style={styles.actionInfo}>
                <HelpCircle size={20} color={colors.text} />
                <Text style={styles.actionText}>Ayuda</Text>
              </View>
              <ChevronRight size={20} color={colors.textSecondary} />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionItem, styles.logoutItem]}
              onPress={handleLogout}
            >
              <View style={styles.actionInfo}>
                <LogOut size={20} color={colors.error} />
                <Text style={styles.logoutText}>Cerrar Sesión</Text>
              </View>
            </TouchableOpacity>
          </View>

          <View style={{ marginTop: 32 }}>
            <Text style={{ fontSize: 18, fontWeight: '600', color: colors.text, marginBottom: 12 }}>Mis Qids</Text>
            {isLoading ? (
              <Text style={{ color: colors.textSecondary }}>Cargando hijos...</Text>
            ) : kids && kids.length > 0 ? (
              kids.map((kid) => (
                <KidCard
                  key={kid.id}
                  fullName={kid.fullName}
                  nickname={kid.nickname}
                  dni={kid.dni}
                  birthDate={kid.birthDate}
                  groupCount={kid.groups ? kid.groups.length : 0}
                  parents={kidParents[kid.id] || []}
                  groups={getGroupsForKid(kid)}
                  onPress={() => router.push(`/kids/${kid.id}`)}
                  style={{ marginBottom: 12 }}
                />
              ))
            ) : (
              <Text style={{ color: colors.textSecondary }}>No tienes Qids registrados.</Text>
            )}
            <Button
              title="Agregar Qid"
              onPress={() => router.push('/kids/add')}
              style={{ marginTop: 12 }}
            />
            <Button
              title="Vincular con Qid existente"
              onPress={() => router.push('/kids/link')}
              variant="outline"
              style={{ marginTop: 8 }}
            />
          </View>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 16,
    position: 'relative',
  },
  editButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    padding: 8,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  userName: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  userNickname: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
  },
  infoCard: {
    marginBottom: 24,
    padding: 16,
  },
  infoItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: colors.text,
  },
  actionsSection: {
    backgroundColor: colors.cardBackground,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.border,
    overflow: 'hidden',
  },
  actionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  actionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  actionText: {
    fontSize: 16,
    color: colors.text,
  },
  logoutItem: {
    borderBottomWidth: 0,
  },
  logoutText: {
    fontSize: 16,
    color: colors.error,
  },
});