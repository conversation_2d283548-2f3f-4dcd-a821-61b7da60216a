import React, { useEffect, useState, useCallback } from 'react';
import { View, Text, StyleSheet, FlatList, RefreshControl, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useFocusEffect } from '@react-navigation/native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { PlusCircle, Search, Filter } from 'lucide-react-native';
import { GroupSearchItem } from '@/components/GroupSearchItem';
import { EmptyState } from '@/components/EmptyState';
import { Loading } from '@/components/Loading';
import { Button } from '@/components/Button';
import { Input } from '@/components/Input';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function GroupsScreen() {
  const { allGroups, isLoading, searchGroups } = useGroupsStore();
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filterYear, setFilterYear] = useState<number | null>(null);

  // Add a timestamp to track when data was last loaded
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  // Minimum time between loads in milliseconds (5 seconds)
  const MIN_LOAD_INTERVAL = 5000;

  // Use useEffect to load data only once when the component mounts
  useEffect(() => {
    console.log('GroupsScreen: Initial data load');
    loadGroupsData('', false); // Don't force refresh on initial load
  }, []); // Empty dependency array means this runs once on mount

  // Function to load groups data with debounce protection
  const loadGroupsData = async (query = '', forceRefresh = false) => {
    // Check if we've loaded data recently (within the last 5 seconds)
    const now = Date.now();
    if (!forceRefresh && lastLoadTime > 0 && now - lastLoadTime < MIN_LOAD_INTERVAL) {
      console.log('GroupsScreen: Skipping data load, too soon since last load');
      return;
    }

    // Update last load time immediately to prevent multiple concurrent loads
    setLastLoadTime(now);

    // Call the actual searchGroups function
    await searchGroups(query, forceRefresh);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadGroupsData('', true); // Pass empty query and forceRefresh=true
    setRefreshing(false);
  };

  const filteredGroups = allGroups.filter(group => {
    const searchLower = searchQuery.toLowerCase();
    const matchesSearch = (
      (group.institutionName && group.institutionName.toLowerCase().includes(searchLower)) ||
      group.sala.toLowerCase().includes(searchLower) ||
      (group.nombre && group.nombre.toLowerCase().includes(searchLower))
    );

    const matchesYear = filterYear ? group.año === filterYear : true;

    return matchesSearch && matchesYear;
  });

  const renderEmptyComponent = () => {
    if (isLoading) {
      return <Loading message="Buscando grupos..." />;
    }

    if (searchQuery && filteredGroups.length === 0) {
      return (
        <EmptyState
          title="No se encontraron resultados"
          message="Intenta con otra búsqueda o crea un nuevo grupo"
          icon={<Search size={40} color={colors.textSecondary} />}
          actionLabel="Crear Grupo"
          onAction={() => router.push('/group/create')}
        />
      );
    }

    return (
      <EmptyState
        title="No hay grupos disponibles"
        message="Crea un nuevo grupo o busca grupos existentes"
        icon={<PlusCircle size={40} color={colors.primary} />}
        actionLabel="Crear Grupo"
        onAction={() => router.push('/group/create')}
      />
    );
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  const clearFilters = () => {
    setFilterYear(null);
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <AnimatedTransition>
        <View style={styles.header}>
          <Text style={styles.title}>Grupos Escolares</Text>
          <Text style={styles.subtitle}>Busca y únete a grupos existentes o crea uno nuevo</Text>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchRow}>
            <Input
              placeholder="Buscar grupo..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              leftIcon={<Search size={20} color={colors.textSecondary} />}
              style={styles.searchInput}
            />
            <TouchableOpacity
              style={styles.filterButton}
              onPress={toggleFilters}
            >
              <Filter size={24} color={filterYear ? colors.primary : colors.textSecondary} />
            </TouchableOpacity>
          </View>

          {showFilters && (
            <View style={styles.filtersContainer}>
              <Text style={styles.filterTitle}>Filtros</Text>

              <View style={styles.yearFilters}>
                <Text style={styles.filterLabel}>Año:</Text>
                <View style={styles.yearButtons}>
                  {[2023, 2024, 2025].map(year => (
                    <TouchableOpacity
                      key={year}
                      style={[
                        styles.yearButton,
                        filterYear === year && styles.yearButtonActive
                      ]}
                      onPress={() => setFilterYear(year === filterYear ? null : year)}
                    >
                      <Text
                        style={[
                          styles.yearButtonText,
                          filterYear === year && styles.yearButtonTextActive
                        ]}
                      >
                        {year}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <Button
                title="Limpiar Filtros"
                onPress={clearFilters}
                variant="text"
                size="small"
                style={styles.clearButton}
              />
            </View>
          )}
        </View>

        <FlatList
          data={filteredGroups}
          keyExtractor={item => item.id}
          renderItem={({ item }) => (
            <GroupSearchItem
              group={item}
              onPress={() => router.push(`/group/join?groupId=${item.id}`)}
            />
          )}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={renderEmptyComponent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }
        />

        <View style={styles.footer}>
          <Button
            title="Crear Nuevo Grupo"
            onPress={() => router.push('/group/create')}
            icon={<PlusCircle size={20} color={colors.background} />}
            style={styles.createButton}
          />
        </View>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  searchContainer: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  searchInput: {
    flex: 1,
    marginBottom: 0,
  },
  filterButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.secondary,
  },
  filtersContainer: {
    backgroundColor: colors.secondary,
    padding: 16,
    borderRadius: 8,
    marginTop: 8,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  yearFilters: {
    marginBottom: 12,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  yearButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  yearButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.border,
  },
  yearButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  yearButtonText: {
    fontSize: 14,
    color: colors.text,
  },
  yearButtonTextActive: {
    color: colors.background,
    fontWeight: '500',
  },
  clearButton: {
    alignSelf: 'flex-end',
  },
  listContent: {
    padding: 16,
    paddingTop: 8,
    flexGrow: 1,
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  createButton: {
    width: '100%',
  },
});