import React, { useCallback, useEffect } from 'react';
import { Tabs, useRouter, router } from 'expo-router';
import { Home, Users, User } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { Image, TouchableOpacity } from 'react-native';
import { useThemeColor } from '../../components/Themed';
import { FontAwesome } from '@expo/vector-icons';
import { useAuthStore } from '@/store/authStore';

export default function TabsLayout() {
  const backgroundColor = useThemeColor({ light: '#fff', dark: '#000' }, 'background');
  const textColor = useThemeColor({ light: '#000', dark: '#fff' }, 'text');
  const navRouter = useRouter();
  const { isAuthenticated, currentUser, checkAuth } = useAuthStore();

  // Verify authentication on mount
  useEffect(() => {
    const verifyAuth = async () => {
      try {
        // Check authentication on mount
        await checkAuth();

        // If not authenticated, redirect to login
        if (!isAuthenticated || !currentUser || !currentUser.id) {
          console.log('TabsLayout: Not authenticated, redirecting to login');
          router.replace('/(auth)/login');
        }
      } catch (error) {
        console.error('TabsLayout: Authentication error:', error);
        router.replace('/(auth)/login');
      }
    };

    verifyAuth();
  }, []);

  // Track last calendar tab press time to debounce
  const lastCalendarPress = React.useRef(0);

  // Handler for calendar tab press to force navigation and refresh
  const handleCalendarPress = useCallback(() => {
    const now = Date.now();
    // Don't do anything if pressed in the last 500ms
    if (now - lastCalendarPress.current < 500) return;
    lastCalendarPress.current = now;

    // Force navigation to calendar tab with a timestamp to ensure reset
    navRouter.push({
      pathname: '/calendar',
      params: { _t: now } // Add timestamp to force reset
    });
  }, [navRouter]);

  // If not authenticated, don't render tabs
  if (!isAuthenticated || !currentUser) {
    return null;
  }

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#00adf5',
        tabBarInactiveTintColor: textColor,
        tabBarStyle: { backgroundColor },
        headerStyle: { backgroundColor },
        headerTintColor: textColor,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          headerTitle: () => (
            <Image
              source={require('../../assets/qids-logo-full.png')}
              style={{ width: 90, height: 60, alignSelf: 'center' }}
              resizeMode="contain"
              defaultSource={require('../../assets/qids-logo-full.png')}
            />
          ),
          headerTitleAlign: 'center',
          tabBarIcon: ({ color }) => <FontAwesome name="home" size={24} color={color} />,
          headerLeft: () => null,
          title: '', // Empty title to prevent "index" from showing
          headerShown: true, // Ensure header is shown
        }}
      />
      <Tabs.Screen
        name="groups"
        options={{
          title: 'Grupos',
          tabBarIcon: ({ color }) => <FontAwesome name="users" size={24} color={color} />,
        }}
      />
      <Tabs.Screen
        name="calendar"
        options={{
          title: 'Calendario',
          tabBarIcon: ({ color }) => (
            <TouchableOpacity onPress={handleCalendarPress}>
              <FontAwesome name="calendar" size={24} color={color} />
            </TouchableOpacity>
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Perfil',
          tabBarIcon: ({ color }) => <FontAwesome name="user" size={24} color={color} />,
        }}
      />
    </Tabs>
  );
}