import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, TouchableOpacity } from 'react-native';
import { router, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Building, BookOpen, Calendar, Users, Search, X, Baby } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { useToastStore } from '@/store/toastStore';
import { KidSelectionItem } from '@/components/KidSelectionItem';

export default function CreateGroupScreen() {
  const [institutionName, setInstitutionName] = useState('');
  const [institutionAddress, setInstitutionAddress] = useState('');
  const [sala, setSala] = useState('');
  const [año, setAño] = useState(new Date().getFullYear().toString());
  const [nombre, setNombre] = useState('');
  const [selectedKidId, setSelectedKidId] = useState<string>('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [showSchoolSearch, setShowSchoolSearch] = useState(false);

  const {
    createGroup,
    isCreatingGroup,
    error,
    clearError,
    searchSchools,
    schools,
    schoolsLoading,
    allGroups
  } = useGroupsStore();

  const { kids, getKids } = useKidsStore();
  const { showToast } = useToastStore();

  // Load kids when component mounts
  useEffect(() => {
    getKids();
  }, [getKids]);

  // Debounced search
  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchQuery.length > 2) {
        searchSchools(searchQuery);
      }
    }, 300);

    return () => clearTimeout(handler);
  }, [searchQuery, searchSchools]);

  // Get unique schools from existing groups
  const existingSchools = useCallback(() => {
    if (!allGroups || allGroups.length === 0) return [];

    // Get unique school names
    const uniqueSchools = [...new Set(allGroups.map(g => g.institutionName).filter(Boolean))];
    return uniqueSchools.filter(school =>
      school.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [allGroups, searchQuery]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!institutionName) newErrors.institutionName = 'El nombre del colegio es requerido';
    if (!sala) newErrors.sala = 'La sala es requerida';
    if (!año) newErrors.año = 'El año es requerido';
    else if (!/^\d{4}$/.test(año)) newErrors.año = 'El año debe tener 4 dígitos';
    if (!selectedKidId) newErrors.kid = 'Debes seleccionar un hijo para crear el grupo';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCreateGroup = async () => {
    if (!validate()) return;

    try {
      const newGroup = await createGroup(institutionName, sala, parseInt(año), selectedKidId, nombre || undefined, institutionAddress || undefined);

      // Get the name of the selected kid for the confirmation message
      const selectedKid = kids?.find(kid => kid.id === selectedKidId);
      const kidName = selectedKid ? selectedKid.fullName : '';

      showToast(`Grupo "${institutionName} - ${sala}" creado exitosamente con ${kidName}`, 'success');

      // Navigate to the group detail page
      router.replace(`/group/${newGroup.id}`);
    } catch (err) {
      if (err instanceof Error) {
        showToast(err.message, 'error');
      } else {
        showToast('No se pudo crear el grupo. Intenta nuevamente.', 'error');
      }
    }
  };

  const toggleKidSelection = (kidId: string) => {
    setSelectedKidId(prevId => prevId === kidId ? '' : kidId);
    if (errors.kid) setErrors({...errors, kid: ''});
  };

  const handleSelectSchool = (schoolName: string) => {
    setInstitutionName(schoolName);
    setShowSchoolSearch(false);
    setSearchQuery('');
  };

  // Combine API results with existing schools
  const combinedSchools = useCallback(() => {
    const existing = existingSchools();
    const apiResults = schools || [];

    // Combine and remove duplicates (case insensitive)
    const combined = [...existing];

    apiResults.forEach(school => {
      if (!combined.some(s => s.toLowerCase() === school.toLowerCase())) {
        combined.push(school);
      }
    });

    return combined;
  }, [existingSchools, schools]);

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Crear Grupo',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            keyboardShouldPersistTaps="handled"
          >
            <Text style={styles.subtitle}>
              Crea un nuevo grupo escolar para conectar a padres y madres
            </Text>

            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {!showSchoolSearch ? (
              <TouchableOpacity
                style={styles.schoolInputContainer}
                onPress={() => setShowSchoolSearch(true)}
              >
                <Input
                  label="Nombre del Colegio"
                  placeholder="Buscar colegio existente..."
                  value={institutionName}
                  onChangeText={(text) => {
                    setInstitutionName(text);
                    if (errors.institutionName) setErrors({...errors, institutionName: ''});
                    if (error) clearError();
                  }}
                  leftIcon={<Building size={20} color={colors.textSecondary} />}
                  rightIcon={<Search size={20} color={colors.textSecondary} />}
                  error={errors.institutionName}
                  editable={false}
                  pointerEvents="none"
                />
              </TouchableOpacity>
            ) : (
              <View style={styles.searchContainer}>
                <View style={styles.searchHeader}>
                  <Text style={styles.searchTitle}>Buscar Colegio</Text>
                  <TouchableOpacity onPress={() => setShowSchoolSearch(false)}>
                    <X size={20} color={colors.textSecondary} />
                  </TouchableOpacity>
                </View>

                <Input
                  placeholder="Escribe para buscar..."
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  leftIcon={<Search size={20} color={colors.textSecondary} />}
                  autoFocus
                />

                <ScrollView
                  style={styles.searchResults}
                  keyboardShouldPersistTaps="handled"
                >
                  {schoolsLoading ? (
                    <Text style={styles.loadingText}>Buscando colegios...</Text>
                  ) : combinedSchools().length > 0 ? (
                    combinedSchools().map((school, index) => (
                      <TouchableOpacity
                        key={`school-${index}-${school}`}
                        style={styles.schoolItem}
                        onPress={() => handleSelectSchool(school)}
                      >
                        <Building size={16} color={colors.textSecondary} />
                        <Text style={styles.schoolName}>{school}</Text>
                      </TouchableOpacity>
                    ))
                  ) : searchQuery.length > 2 ? (
                    <View>
                      <Text style={styles.noResultsText}>No se encontraron colegios</Text>
                      <Button
                        title={`Crear "${searchQuery}"`}
                        onPress={() => handleSelectSchool(searchQuery)}
                        variant="outline"
                        size="small"
                        style={styles.createSchoolButton}
                      />
                    </View>
                  ) : (
                    <Text style={styles.hintText}>Escribe al menos 3 caracteres para buscar</Text>
                  )}
                </ScrollView>
              </View>
            )}

            <Input
              label="Sala o Grado"
              placeholder="Ej: Sala Roja, 3er Grado"
              value={sala}
              onChangeText={(text) => {
                setSala(text);
                if (errors.sala) setErrors({...errors, sala: ''});
                if (error) clearError();
              }}
              leftIcon={<BookOpen size={20} color={colors.textSecondary} />}
              error={errors.sala}
            />

            <Input
              label="Año"
              placeholder="Ej: 2024"
              value={año}
              onChangeText={(text) => {
                setAño(text);
                if (errors.año) setErrors({...errors, año: ''});
                if (error) clearError();
              }}
              keyboardType="numeric"
              leftIcon={<Calendar size={20} color={colors.textSecondary} />}
              error={errors.año}
            />

            <Input
              label="Nombre del Grupo (opcional)"
              placeholder="Ej: Grupo A, Turno Mañana"
              value={nombre}
              onChangeText={setNombre}
              leftIcon={<Users size={20} color={colors.textSecondary} />}
            />

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Selecciona un hijo</Text>
              {errors.kid && <Text style={styles.errorText}>{errors.kid}</Text>}

              {kids && kids.length > 0 ? (
                kids.map(kid => (
                  <KidSelectionItem
                    key={kid.id}
                    kid={kid}
                    isSelected={selectedKidId === kid.id}
                    onToggle={() => toggleKidSelection(kid.id)}
                  />
                ))
              ) : (
                <View style={styles.emptyKidsContainer}>
                  <Text style={styles.emptyText}>
                    No tienes hijos registrados. Agrega un hijo primero.
                  </Text>
                  <Button
                    title="Agregar Hijo"
                    onPress={() => router.push('/kids/add')}
                    variant="outline"
                    style={styles.addKidButton}
                  />
                </View>
              )}
            </View>

            <Button
              title="Crear Grupo"
              onPress={handleCreateGroup}
              loading={isCreatingGroup}
              disabled={!selectedKidId || kids?.length === 0}
              style={styles.createButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    marginBottom: 8,
  },
  schoolInputContainer: {
    marginBottom: 16,
  },
  searchContainer: {
    marginBottom: 16,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    padding: 12,
  },
  searchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  searchTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  searchResults: {
    maxHeight: 200,
    marginTop: 8,
  },
  schoolItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    gap: 12,
  },
  schoolName: {
    fontSize: 14,
    color: colors.text,
  },
  loadingText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: 16,
  },
  noResultsText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: 8,
  },
  hintText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: 16,
  },
  createSchoolButton: {
    marginTop: 8,
    alignSelf: 'center',
  },
  section: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  emptyKidsContainer: {
    padding: 16,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  addKidButton: {
    minWidth: 200,
  },
  createButton: {
    marginTop: 16,
  },
});