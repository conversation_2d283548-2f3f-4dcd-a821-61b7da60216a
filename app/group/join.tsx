import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Users, CheckCircle, AlertCircle } from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { KidSelectionItem } from '@/components/KidSelectionItem';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { useToastStore } from '@/store/toastStore';

export default function JoinGroupScreen() {
  const { groupId } = useLocalSearchParams<{ groupId: string }>();
  const { getGroupById, joinGroup, isLoading, getMembershipsByGroupId } = useGroupsStore();
  const { kids, getKids } = useKidsStore();
  const { showToast } = useToastStore();

  const [selectedKidIds, setSelectedKidIds] = useState<string[]>([]);
  const [isReferente, setIsReferente] = useState(false);
  const [alreadyJoinedKids, setAlreadyJoinedKids] = useState<string[]>([]);

  const group = getGroupById(groupId);

  // Memoize memberships to prevent unnecessary re-renders
  const groupMemberships = useCallback(() => {
    if (!groupId) return [];
    return getMembershipsByGroupId(groupId);
  }, [groupId, getMembershipsByGroupId]);

  // Load kids and find already joined kids
  useEffect(() => {
    const loadData = async () => {
      await getKids();

      // Find kids that are already in this group
      if (groupId) {
        const memberships = groupMemberships();
        const joinedKids = memberships.map(m => m.kidId);
        setAlreadyJoinedKids(joinedKids);
      }
    };

    loadData();
  }, [groupId, groupMemberships, getKids]);

  const toggleKidSelection = useCallback((kidId: string) => {
    setSelectedKidIds(prev => {
      if (prev.includes(kidId)) {
        return prev.filter(id => id !== kidId);
      } else {
        return [...prev, kidId];
      }
    });
  }, []);

  const handleJoinGroup = async () => {
    if (selectedKidIds.length === 0) {
      showToast('Debes seleccionar al menos un hijo para unirte al grupo', 'error');
      return;
    }

    try {
      await joinGroup(groupId, selectedKidIds, isReferente);

      // Get the names of the selected kids for the confirmation message
      const selectedKidNames = kids
        .filter(kid => selectedKidIds.includes(kid.id))
        .map(kid => kid.fullName);

      const kidMessage = selectedKidNames.length === 1
        ? selectedKidNames[0]
        : `${selectedKidNames.length} niños (${selectedKidNames.join(', ')})`;

      showToast(`Te has unido al grupo "${group?.institutionName} - ${group?.sala}" con ${kidMessage}`, 'success');

      // Navigate to the group detail page
      router.replace(`/group/${groupId}`);
    } catch (err) {
      if (err instanceof Error) {
        showToast(err.message, 'error');
      } else {
        showToast('No se pudo unir al grupo. Intenta nuevamente.', 'error');
      }
    }
  };

  if (!group) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Unirse al Grupo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Grupo no encontrado</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Filter out kids that are already in this group
  const availableKids = kids ? kids.filter(kid => !alreadyJoinedKids.includes(kid.id)) : [];
  const joinedKids = kids ? kids.filter(kid => alreadyJoinedKids.includes(kid.id)) : [];

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Unirse al Grupo',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Text style={styles.subtitle}>
            Selecciona los hijos que deseas agregar a este grupo
          </Text>

          <Card variant="elevated" style={styles.groupCard}>
            <Text style={styles.groupName}>{group.institutionName}</Text>
            <Text style={styles.groupDetail}>{group.sala} - {group.año}</Text>
            {group.nombre && (
              <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
            )}
          </Card>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Selecciona tus hijos</Text>

            {availableKids.length > 0 ? (
              availableKids.map(kid => (
                <KidSelectionItem
                  key={kid.id}
                  kid={kid}
                  isSelected={selectedKidIds.includes(kid.id)}
                  onToggle={() => toggleKidSelection(kid.id)}
                />
              ))
            ) : (
              <Card variant="outlined" style={styles.emptyCard}>
                {kids && kids.length > 0 && joinedKids.length > 0 ? (
                  <View style={styles.warningContainer}>
                    <AlertCircle size={20} color={colors.warning} />
                    <Text style={styles.warningText}>
                      Todos tus hijos ya están en este grupo
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.emptyText}>
                    No tienes hijos registrados. Agrega un hijo primero.
                  </Text>
                )}
                <Button
                  title="Agregar Hijo"
                  onPress={() => router.push('/kids/add')}
                  variant="outline"
                  style={styles.addKidButton}
                />
              </Card>
            )}

            {joinedKids.length > 0 && (
              <View style={styles.alreadyJoinedContainer}>
                <Text style={styles.alreadyJoinedTitle}>Hijos ya en este grupo:</Text>
                {joinedKids.map(kid => (
                  <View key={kid.id} style={styles.alreadyJoinedItem}>
                    <Text style={styles.alreadyJoinedName}>{kid.fullName}</Text>
                    <CheckCircle size={16} color={colors.success} />
                  </View>
                ))}
              </View>
            )}
          </View>

          {availableKids.length > 0 && (
            <View style={styles.referenteSection}>
              <View style={styles.referenteHeader}>
                <Text style={styles.referenteTitle}>¿Eres referente del grupo?</Text>
                <Button
                  title={isReferente ? "Sí" : "No"}
                  onPress={() => setIsReferente(!isReferente)}
                  variant={isReferente ? "primary" : "outline"}
                  size="small"
                  icon={isReferente ? <CheckCircle size={16} color={colors.background} /> : undefined}
                  style={styles.referenteButton}
                />
              </View>
              <Text style={styles.referenteInfo}>
                Los referentes pueden crear anuncios, colectas y administrar el grupo.
              </Text>
            </View>
          )}

          {availableKids.length > 0 && (
            <Button
              title="Unirse al Grupo"
              onPress={handleJoinGroup}
              loading={isLoading}
              disabled={selectedKidIds.length === 0}
              icon={<Users size={20} color={colors.background} />}
              style={styles.joinButton}
            />
          )}
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  groupCard: {
    marginBottom: 24,
    padding: 16,
  },
  groupName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  emptyCard: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  warningContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  warningText: {
    fontSize: 14,
    color: colors.warning,
    flex: 1,
  },
  addKidButton: {
    minWidth: 200,
  },
  alreadyJoinedContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: colors.secondary,
    borderRadius: 8,
  },
  alreadyJoinedTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  alreadyJoinedItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  alreadyJoinedName: {
    fontSize: 14,
    color: colors.text,
  },
  referenteSection: {
    marginBottom: 24,
    backgroundColor: colors.secondary,
    padding: 16,
    borderRadius: 8,
  },
  referenteHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  referenteTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  referenteButton: {
    minWidth: 80,
  },
  referenteInfo: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  joinButton: {
    marginBottom: 16,
  },
});