/**
 * Create Colecta Screen
 *
 * Screen for creating a new collection (colecta) in a group.
 * Allows users to set details like reason, description, deadline,
 * suggested amount, and payment information.
 *
 * Features:
 * - Form for collection details
 * - Date picker for deadline
 * - Form validation
 * - Error handling
 *
 * @screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { DollarSign, CreditCard, FileText, User, CreditCard as CreditCardIcon, Landmark } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useColectasStore } from '@/store/colectasStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { DatePicker } from '@/components/DatePicker';
import { useToastStore } from '@/store/toastStore';

export default function CreateColectaScreen() {
  // Get group ID from route params
  const { id } = useLocalSearchParams<{ id: string }>();

  // Access stores for data and actions
  const { createColecta, isLoading, error, clearError } = useColectasStore();
  const { showToast } = useToastStore();

  // Form state
  const [motivo, setMotivo] = useState('');
  const [descripcion, setDescripcion] = useState('');
  const [fechaFin, setFechaFin] = useState('');
  const [baseAmount, setBaseAmount] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [accountInfo, setAccountInfo] = useState('');
  const [titularCuenta, setTitularCuenta] = useState('');
  const [cuitCuil, setCuitCuil] = useState('');
  const [aliasCbu, setAliasCbu] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  /**
   * Validate the form data
   * @returns boolean indicating if the form is valid
   */
  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!motivo.trim()) newErrors.motivo = 'El motivo es requerido';
    if (!descripcion.trim()) newErrors.descripcion = 'La descripción es requerida';
    if (!fechaFin) newErrors.fechaFin = 'La fecha límite es requerida';
    if (!baseAmount.trim()) newErrors.baseAmount = 'El monto sugerido es requerido';
    else if (isNaN(Number(baseAmount)) || Number(baseAmount) <= 0) {
      newErrors.baseAmount = 'El monto debe ser un número positivo';
    }
    if (!targetAmount.trim()) newErrors.targetAmount = 'El total a recaudar es requerido';
    else if (isNaN(Number(targetAmount)) || Number(targetAmount) <= 0) {
      newErrors.targetAmount = 'El total debe ser un número positivo';
    }
    if (!accountInfo.trim()) newErrors.accountInfo = 'La información de pago es requerida';
    if (!titularCuenta.trim()) newErrors.titularCuenta = 'El titular de cuenta es requerido';
    if (!cuitCuil.trim()) newErrors.cuitCuil = 'El CUIT/CUIL es requerido';
    if (!aliasCbu.trim()) newErrors.aliasCbu = 'El Alias o CBU es requerido';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  /**
   * Create a new collection
   */
  const handleCreateColecta = async () => {
    if (!validate()) return;

    try {
      await createColecta(
        id,
        motivo,
        descripcion,
        fechaFin,
        Number(baseAmount),
        Number(targetAmount),
        accountInfo,
        titularCuenta,
        cuitCuil,
        aliasCbu
      );

      showToast('Colecta creada exitosamente', 'success');

      // Navigate back to the group detail page
      router.replace(`/group/${id}`);
    } catch (err) {
      showToast('No se pudo crear la colecta. Intenta nuevamente.', 'error');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Nueva Colecta',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Crea una nueva colecta para recaudar fondos en el grupo
            </Text>

            {/* Error display */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            {/* Form fields */}
            <Input
              label="Motivo"
              placeholder="Ej: Regalo para la maestra"
              value={motivo}
              onChangeText={(text) => {
                setMotivo(text);
                if (errors.motivo) setErrors({...errors, motivo: ''});
                if (error) clearError();
              }}
              leftIcon={<FileText size={20} color={colors.textSecondary} />}
              error={errors.motivo}
            />

            <Input
              label="Descripción"
              placeholder="Describe el propósito de la colecta..."
              value={descripcion}
              onChangeText={(text) => {
                setDescripcion(text);
                if (errors.descripcion) setErrors({...errors, descripcion: ''});
                if (error) clearError();
              }}
              multiline
              numberOfLines={4}
              style={styles.descriptionInput}
              error={errors.descripcion}
            />

            <DatePicker
              label="Fecha límite"
              value={fechaFin}
              onChange={(date) => {
                setFechaFin(date);
                if (errors.fechaFin) setErrors({...errors, fechaFin: ''});
              }}
              error={errors.fechaFin}
              minimumDate={new Date()}
            />

            <Input
              label="Total a recaudar"
              placeholder="Ej: 50000"
              value={targetAmount}
              onChangeText={(text) => {
                setTargetAmount(text);
                if (errors.targetAmount) setErrors({...errors, targetAmount: ''});
                if (error) clearError();
              }}
              keyboardType="numeric"
              leftIcon={<DollarSign size={20} color={colors.textSecondary} />}
              error={errors.targetAmount}
            />

            <Input
              label="Monto sugerido"
              placeholder="Ej: 1000"
              value={baseAmount}
              onChangeText={(text) => {
                setBaseAmount(text);
                if (errors.baseAmount) setErrors({...errors, baseAmount: ''});
                if (error) clearError();
              }}
              keyboardType="numeric"
              leftIcon={<DollarSign size={20} color={colors.textSecondary} />}
              error={errors.baseAmount}
            />

            <Input
              label="Información de pago"
              placeholder="Ej: Transferir a la siguiente cuenta"
              value={accountInfo}
              onChangeText={(text) => {
                setAccountInfo(text);
                if (errors.accountInfo) setErrors({...errors, accountInfo: ''});
                if (error) clearError();
              }}
              leftIcon={<CreditCard size={20} color={colors.textSecondary} />}
              error={errors.accountInfo}
            />

            <Input
              label="Titular de cuenta"
              placeholder="Ej: Juan Pérez"
              value={titularCuenta}
              onChangeText={(text) => {
                setTitularCuenta(text);
                if (errors.titularCuenta) setErrors({...errors, titularCuenta: ''});
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.titularCuenta}
            />

            <Input
              label="CUIT/CUIL"
              placeholder="Ej: 20-********-9 (para validar transferencias)"
              value={cuitCuil}
              onChangeText={(text) => {
                setCuitCuil(text);
                if (errors.cuitCuil) setErrors({...errors, cuitCuil: ''});
                if (error) clearError();
              }}
              leftIcon={<CreditCardIcon size={20} color={colors.textSecondary} />}
              error={errors.cuitCuil}
            />

            <Input
              label="Alias o CBU"
              placeholder="Ej: CASA.ARBOL.PERRO o 0000000000000000000000"
              value={aliasCbu}
              onChangeText={(text) => {
                setAliasCbu(text);
                if (errors.aliasCbu) setErrors({...errors, aliasCbu: ''});
                if (error) clearError();
              }}
              leftIcon={<Landmark size={20} color={colors.textSecondary} />}
              error={errors.aliasCbu}
            />

            <Button
              title="Crear Colecta"
              onPress={handleCreateColecta}
              loading={isLoading}
              style={styles.createButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  descriptionInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  createButton: {
    marginTop: 16,
  },
});