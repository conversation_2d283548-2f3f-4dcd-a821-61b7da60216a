/**
 * Create Announcement Screen
 * 
 * Screen for creating a new announcement in a group.
 * Allows users to set a title, message, type, and optional date.
 * 
 * Features:
 * - Form for announcement details
 * - Type selection (Announcement, Reminder, Important Date)
 * - Date picker for important dates
 * - Form validation
 * - Error handling
 * 
 * @screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Bell, Calendar, AlertTriangle } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useAnnouncementsStore } from '@/store/announcementsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { DatePicker } from '@/components/DatePicker';
import { AnnouncementType } from '@/types';
import { useToastStore } from '@/store/toastStore';

export default function CreateAnnouncementScreen() {
  // Get group ID from route params
  const { id } = useLocalSearchParams<{ id: string }>();
  
  // Access stores for data and actions
  const { createAnnouncement, isLoading, error, clearError } = useAnnouncementsStore();
  const { showToast } = useToastStore();
  
  // Form state
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [type, setType] = useState<AnnouncementType>('ANNOUNCEMENT');
  const [date, setDate] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  /**
   * Validate the form data
   * @returns boolean indicating if the form is valid
   */
  const validate = () => {
    const newErrors: Record<string, string> = {};
    
    if (!title.trim()) newErrors.title = 'El título es requerido';
    if (!message.trim()) newErrors.message = 'El mensaje es requerido';
    if (type === 'IMPORTANT_DATE' && !date) newErrors.date = 'La fecha es requerida para este tipo de anuncio';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  /**
   * Create a new announcement
   */
  const handleCreateAnnouncement = async () => {
    if (!validate()) return;
    
    try {
      await createAnnouncement(
        id,
        title,
        message,
        type,
        date || undefined
      );
      
      showToast('Anuncio creado exitosamente', 'success');
      
      // Navigate back to the group detail page
      router.replace(`/group/${id}`);
    } catch (err) {
      showToast('No se pudo crear el anuncio. Intenta nuevamente.', 'error');
    }
  };
  
  /**
   * Get style for type button based on selected state
   * @param buttonType - The announcement type to check
   * @returns Style array for the button
   */
  const getTypeButtonStyle = (buttonType: AnnouncementType) => {
    return [
      styles.typeButton,
      type === buttonType && styles.activeTypeButton
    ];
  };
  
  /**
   * Get text style for type button based on selected state
   * @param buttonType - The announcement type to check
   * @returns Style array for the button text
   */
  const getTypeTextStyle = (buttonType: AnnouncementType) => {
    return [
      styles.typeButtonText,
      type === buttonType && styles.activeTypeButtonText
    ];
  };
  
  /**
   * Get icon for type button
   * @param buttonType - The announcement type
   * @returns Icon component with appropriate color
   */
  const getTypeIcon = (buttonType: AnnouncementType) => {
    switch (buttonType) {
      case 'ANNOUNCEMENT':
        return <Bell size={16} color={type === buttonType ? colors.background : colors.primary} />;
      case 'REMINDER':
        return <Bell size={16} color={type === buttonType ? colors.background : colors.warning} />;
      case 'IMPORTANT_DATE':
        return <Calendar size={16} color={type === buttonType ? colors.background : colors.error} />;
      default:
        return <AlertTriangle size={16} color={type === buttonType ? colors.background : colors.primary} />;
    }
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: 'Nuevo Anuncio',
          headerBackTitle: 'Atrás',
        }} 
      />
      
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Crea un nuevo anuncio para compartir información con el grupo
            </Text>
            
            {/* Error display */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
            
            {/* Announcement type selection */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Tipo de anuncio</Text>
              <View style={styles.typeButtonsContainer}>
                <Button
                  title="Anuncio"
                  onPress={() => setType('ANNOUNCEMENT')}
                  variant={type === 'ANNOUNCEMENT' ? 'primary' : 'outline'}
                  size="small"
                  icon={getTypeIcon('ANNOUNCEMENT')}
                  style={styles.typeSelectButton}
                />
                <Button
                  title="Recordatorio"
                  onPress={() => setType('REMINDER')}
                  variant={type === 'REMINDER' ? 'warning' : 'outline'}
                  size="small"
                  icon={getTypeIcon('REMINDER')}
                  style={styles.typeSelectButton}
                />
                <Button
                  title="Fecha Importante"
                  onPress={() => setType('IMPORTANT_DATE')}
                  variant={type === 'IMPORTANT_DATE' ? 'error' : 'outline'}
                  size="small"
                  icon={getTypeIcon('IMPORTANT_DATE')}
                  style={styles.typeSelectButton}
                />
              </View>
            </View>
            
            {/* Form fields */}
            <Input
              label="Título"
              placeholder="Ej: Reunión de padres"
              value={title}
              onChangeText={(text) => {
                setTitle(text);
                if (errors.title) setErrors({...errors, title: ''});
                if (error) clearError();
              }}
              error={errors.title}
            />
            
            <Input
              label="Mensaje"
              placeholder="Escribe el contenido del anuncio..."
              value={message}
              onChangeText={(text) => {
                setMessage(text);
                if (errors.message) setErrors({...errors, message: ''});
                if (error) clearError();
              }}
              multiline
              numberOfLines={4}
              style={styles.messageInput}
              error={errors.message}
            />
            
            {/* Date picker for important dates */}
            {type === 'IMPORTANT_DATE' && (
              <DatePicker
                label="Fecha del evento"
                value={date}
                onChange={(selectedDate) => {
                  setDate(selectedDate);
                  if (errors.date) setErrors({...errors, date: ''});
                }}
                error={errors.date}
              />
            )}
            
            <Button
              title="Crear Anuncio"
              onPress={handleCreateAnnouncement}
              loading={isLoading}
              style={styles.createButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  typeButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  typeButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  activeTypeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    color: colors.text,
  },
  activeTypeButtonText: {
    color: colors.background,
    fontWeight: '500',
  },
  typeSelectButton: {
    flex: 1,
    minWidth: 100,
  },
  messageInput: {
    height: 100,
    textAlignVertical: 'top',
  },
  createButton: {
    marginTop: 16,
  },
});