/**
 * Create Event Screen
 *
 * Screen for creating a new event in a group.
 * Allows users to set event details like title, description, type, date, and location.
 *
 * Features:
 * - Form for event details
 * - Event type selection
 * - Date and time picker
 * - Location input
 * - Form validation
 *
 * @screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Calendar, Clock, MapPin, Send } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { DateTimePicker } from '@/components/DateTimePicker';
import { colors } from '@/constants/colors';
import { useEventsStore } from '@/store/eventsStore';
import { useGroupsStore } from '@/store/groupsStore';

// Define the event types
type EventType = 'MEETING' | 'ACTIVITY' | 'HOLIDAY' | 'EXAM' | 'OTHER';

export default function CreateEventScreen() {
  // Get group ID from route params
  const { id: groupId } = useLocalSearchParams<{ id: string }>();

  // Access stores for data and actions
  const { getGroupById } = useGroupsStore();
  const { createEvent, isLoading, error, clearError } = useEventsStore();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState<EventType>('ACTIVITY');
  const [date, setDate] = useState(new Date());
  const [location, setLocation] = useState('');

  // Get group data
  const group = getGroupById(groupId);

  // Event type options
  const eventTypes: { value: EventType, label: string }[] = [
    { value: 'MEETING', label: 'Reunión' },
    { value: 'ACTIVITY', label: 'Actividad' },
    { value: 'HOLIDAY', label: 'Feriado' },
    { value: 'EXAM', label: 'Examen' },
    { value: 'OTHER', label: 'Otro' },
  ];

  /**
   * Validate the form data
   * @returns boolean indicating if the form is valid
   */
  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Por favor ingresa un título para el evento');
      return false;
    }
    
    return true;
  };

  /**
   * Create a new event
   */
  const handleCreate = async () => {
    if (!validateForm()) return;

    try {
      await createEvent(
        groupId,
        title,
        description,
        type,
        date.toISOString(),
        undefined,
        location
      );

      Alert.alert(
        'Éxito',
        'Evento creado correctamente',
        [
          { text: 'OK', onPress: () => router.back() }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo crear el evento. Intenta nuevamente.');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Nuevo Evento',
          headerBackTitle: 'Atrás',
        }}
      />

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Group Info */}
        <View style={styles.groupInfo}>
          <Text style={styles.groupName}>
            {group ? `${group.sala} - ${group.año}` : 'Cargando...'}
          </Text>
          {group?.nombre && (
            <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
          )}
        </View>

        {/* Event Information Section */}
        <View style={styles.formSection}>
          <Text style={styles.sectionTitle}>Información del evento</Text>

          <Input
            label="Título"
            placeholder="Ej: Acto escolar - Día de la Bandera"
            value={title}
            onChangeText={setTitle}
          />

          <Input
            label="Descripción (opcional)"
            placeholder="Ej: Acto por el día de la bandera. Los niños deben venir con el uniforme completo."
            value={description}
            onChangeText={setDescription}
            multiline
            numberOfLines={3}
          />

          {/* Event Type Selection */}
          <View style={styles.formField}>
            <Text style={styles.label}>Tipo de evento</Text>
            <View style={styles.typeContainer}>
              {eventTypes.map((eventType) => (
                <TouchableOpacity
                  key={eventType.value}
                  style={[
                    styles.typeButton,
                    type === eventType.value && styles.selectedTypeButton
                  ]}
                  onPress={() => setType(eventType.value)}
                >
                  <Text style={[
                    styles.typeButtonText,
                    type === eventType.value && styles.selectedTypeButtonText
                  ]}>
                    {eventType.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Date and Time Selection */}
          <DateTimePicker
            label="Fecha y hora"
            value={date}
            onChange={setDate}
          />

          {/* Location Input */}
          <View style={styles.formField}>
            <Input
              label="Ubicación (opcional)"
              placeholder="Ej: Patio central de la escuela"
              value={location}
              onChangeText={setLocation}
              leftIcon={<MapPin size={20} color={colors.textSecondary} />}
            />
          </View>
        </View>

        {/* Create Button */}
        <Button
          title="Crear Evento"
          onPress={handleCreate}
          variant="primary"
          icon={<Send size={18} color="#fff" />}
          style={styles.createButton}
          loading={isLoading}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  groupInfo: {
    marginBottom: 24,
  },
  groupName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  formSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  formField: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  typeButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedTypeButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  typeButtonText: {
    fontSize: 14,
    color: colors.text,
  },
  selectedTypeButtonText: {
    color: colors.background,
    fontWeight: '500',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: colors.card,
  },
  dateText: {
    fontSize: 16,
    color: colors.text,
  },
  createButton: {
    marginBottom: 32,
  },
});