import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Linking } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Edit,
  Trash2,
  Share2,
  MessageCircle
} from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Badge } from '@/components/Badge';
import { colors } from '@/constants/colors';
import { useEventsStore } from '@/store/eventsStore';
import { useGroupsStore } from '@/store/groupsStore';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { EventsService } from '@/services/eventsService';

export default function EventDetailScreen() {
  const { id: groupId, eventId } = useLocalSearchParams<{ id: string, eventId: string }>();
  const { events, getEventById, deleteEvent, isLoading } = useEventsStore();
  const { getGroupById } = useGroupsStore();
  
  const [fetchedEvent, setFetchedEvent] = useState<any>(null);
  const [fetching, setFetching] = useState(false);

  let event = getEventById(eventId);
  const group = getGroupById(groupId);

  // If not found in store, fetch from DB
  useEffect(() => {
    if (!eventId || event) return;
    setFetching(true);
    EventsService.getEventById(eventId)
      .then(e => setFetchedEvent(e))
      .finally(() => setFetching(false));
  }, [eventId, event]);

  if (!event && fetchedEvent) {
    event = fetchedEvent;
  }
  
  // Check if user is creator or referente (for now, always true for demo)
  const isCreatorOrReferente = true;
  
  if (!event || !group) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Evento' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Evento no encontrado</Text>
          <Button 
            title="Volver" 
            onPress={() => router.back()} 
            variant="outline" 
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }
  
  // Format dates
  const formattedDate = format(new Date(event.date), 'EEEE d MMMM, yyyy', { locale: es });
  const formattedTime = format(new Date(event.date), 'HH:mm', { locale: es });
  
  // Get event type info
  const getEventTypeInfo = (type: string) => {
    switch (type) {
      case 'MEETING':
      case 'reunion':
        return {
          label: 'Reunión',
          color: colors.primary,
        };
      case 'ACTIVITY':
      case 'actividad':
        return {
          label: 'Actividad',
          color: colors.accent,
        };
      case 'HOLIDAY':
      case 'feriado':
        return {
          label: 'Feriado',
          color: colors.warning,
        };
      case 'EXAM':
      case 'examen':
        return {
          label: 'Examen',
          color: colors.error,
        };
      case 'OTHER':
      case 'otro':
        return {
          label: 'Otro',
          color: colors.secondary,
        };
      default:
        return {
          label: type,
          color: colors.primary,
        };
    }
  };
  
  const eventTypeInfo = getEventTypeInfo(event.type);
  
  // Handle delete event
  const handleDelete = () => {
    Alert.alert(
      'Eliminar evento',
      '¿Estás seguro de que deseas eliminar este evento? Esta acción no se puede deshacer.',
      [
        { text: 'Cancelar', style: 'cancel' },
        { 
          text: 'Eliminar', 
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteEvent(eventId);
              Alert.alert(
                'Éxito',
                'El evento ha sido eliminado',
                [
                  { text: 'OK', onPress: () => router.back() }
                ]
              );
            } catch (error) {
              Alert.alert('Error', 'No se pudo eliminar el evento. Intenta nuevamente.');
            }
          }
        },
      ]
    );
  };
  
  // Handle share event
  const handleShare = () => {
    // In a real app, this would use the Share API
    Alert.alert('Compartir', 'Función de compartir no implementada en esta demo');
  };

  // Handle WhatsApp share
  const handleWhatsAppShare = () => {
    const message = `*${event.title}*\n\n` +
      `${event.description ? `${event.description}\n\n` : ''}` +
      `📅 ${formattedDate}\n` +
      `🕒 ${formattedTime}\n` +
      `${event.location ? `📍 ${event.location}\n` : ''}`;
    
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `whatsapp://send?text=${encodedMessage}`;
    
    Linking.canOpenURL(whatsappUrl)
      .then(supported => {
        if (supported) {
          Linking.openURL(whatsappUrl);
        } else {
          Alert.alert('Error', 'No se pudo abrir WhatsApp');
        }
      })
      .catch(err => {
        Alert.alert('Error', 'No se pudo abrir WhatsApp');
      });
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: 'Detalle del Evento',
          headerBackTitle: 'Atrás',
        }} 
      />
      
      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Badge 
              label={eventTypeInfo?.label || ''}
              variant="primary"
              size="medium"
              style={{ backgroundColor: eventTypeInfo?.color || colors.primary }}
            />
            
            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>{group.sala} - {group.año}</Text>
              {group.nombre && (
                <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
              )}
            </View>
          </View>
          
          <Card variant="elevated" style={styles.eventCard}>
            <Text style={styles.eventTitle}>{event.title}</Text>
            
            {event.description && (
              <Text style={styles.eventDescription}>{event.description}</Text>
            )}
            
            <View style={styles.detailsContainer}>
              <View style={styles.detailItem}>
                <Calendar size={20} color={colors.textSecondary} />
                <Text style={styles.detailText}>{formattedDate}</Text>
              </View>
              
              <View style={styles.detailItem}>
                <Clock size={20} color={colors.textSecondary} />
                <Text style={styles.detailText}>{formattedTime}</Text>
              </View>
              
              {event.location && (
                <View style={styles.detailItem}>
                  <MapPin size={20} color={colors.textSecondary} />
                  <Text style={styles.detailText}>{event.location}</Text>
                </View>
              )}
            </View>
          </Card>
          
          <View style={styles.actionsContainer}>
            <Button
              title="Compartir"
              onPress={handleShare}
              variant="outline"
              icon={<Share2 size={18} color={colors.primary} />}
              style={styles.actionButton}
            />
            
            <Button
              title="Compartir por WhatsApp"
              onPress={handleWhatsAppShare}
              variant="outline"
              icon={<MessageCircle size={18} color={colors.primary} />}
              style={styles.actionButton}
            />
            
            {isCreatorOrReferente && (
              <>
                <Button
                  title="Editar"
                  onPress={() => router.push(`/group/${groupId}/event/edit/${eventId}`)}
                  variant="outline"
                  icon={<Edit size={18} color={colors.primary} />}
                  style={styles.actionButton}
                />
                
                <Button
                  title="Eliminar"
                  onPress={handleDelete}
                  variant="outline"
                  icon={<Trash2 size={18} color={colors.error} />}
                  style={[styles.actionButton, { borderColor: colors.error }]}
                  textStyle={{ color: colors.error }}
                  loading={isLoading}
                />
              </>
            )}
          </View>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  groupInfo: {
    marginTop: 12,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  eventCard: {
    padding: 16,
    marginBottom: 24,
  },
  eventTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  eventDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  detailsContainer: {
    gap: 12,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  detailText: {
    fontSize: 16,
    color: colors.text,
  },
  actionsContainer: {
    gap: 12,
    marginBottom: 32,
  },
  actionButton: {
    width: '100%',
  },
});