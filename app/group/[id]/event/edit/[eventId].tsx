import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { DateTimePicker } from '@/components/DateTimePicker';
import { colors } from '@/constants/colors';
import { useEventsStore } from '@/store/eventsStore';
import { EventsService } from '@/services/eventsService';

const EVENT_TYPES = [
  { value: 'MEETING', label: 'Reunión' },
  { value: 'ACTIVITY', label: 'Actividad' },
  { value: 'HOLIDAY', label: 'Feriado' },
  { value: 'EXAM', label: 'Examen' },
  { value: 'OTHER', label: 'Otro' },
];

export default function EditEventScreen() {
  const { id: groupId, eventId } = useLocalSearchParams<{ id: string, eventId: string }>();
  const { getEventById, updateEvent } = useEventsStore();
  const [event, setEvent] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [type, setType] = useState('MEETING');
  const [date, setDate] = useState(new Date());
  const [location, setLocation] = useState('');

  useEffect(() => {
    let found = getEventById(eventId);
    if (!found) {
      EventsService.getEventById(eventId).then(e => {
        setEvent(e);
        if (e) {
          setTitle(e.title || '');
          setDescription(e.description || '');
          setType(e.type || 'MEETING');
          setDate(e.date ? new Date(e.date) : new Date());
          setLocation(e.location || '');
        }
      });
    } else {
      setEvent(found);
      setTitle(found.title || '');
      setDescription(found.description || '');
      setType(found.type || 'MEETING');
      setDate(found.date ? new Date(found.date) : new Date());
      setLocation(found.location || '');
    }
  }, [eventId]);

  const handleSave = async () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Por favor ingresa un título para el evento');
      return;
    }
    setLoading(true);
    try {
      await updateEvent(eventId, title, description, type, date.toISOString(), undefined, location);
      Alert.alert('Éxito', 'Evento actualizado correctamente', [
        { text: 'OK', onPress: () => router.push(`/group/${groupId}/event/${eventId}`) }
      ]);
    } catch (error) {
      Alert.alert('Error', 'No se pudo actualizar el evento. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  if (!event) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Editar Evento' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Cargando evento...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen options={{ title: 'Editar Evento' }} />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.label}>Título</Text>
        <Input value={title} onChangeText={setTitle} placeholder="Título del evento" />

        <Text style={styles.label}>Descripción</Text>
        <Input value={description} onChangeText={setDescription} placeholder="Descripción" multiline />

        <Text style={styles.label}>Tipo</Text>
        <View style={styles.typePicker}>
          {EVENT_TYPES.map(opt => (
            <Button
              key={opt.value}
              title={opt.label}
              variant={type === opt.value ? 'primary' : 'outline'}
              onPress={() => setType(opt.value)}
              style={styles.typeButton}
            />
          ))}
        </View>

        <Text style={styles.label}>Fecha y hora</Text>
        <DateTimePicker value={date} onChange={setDate} />

        <Text style={styles.label}>Ubicación</Text>
        <Input value={location} onChangeText={setLocation} placeholder="Ubicación" />

        <Button
          title={loading ? 'Guardando...' : 'Guardar Cambios'}
          onPress={handleSave}
          loading={loading}
          style={styles.saveButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  scrollContent: {
    padding: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
    marginTop: 16,
  },
  typePicker: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 8,
  },
  typeButton: {
    marginRight: 8,
    marginBottom: 8,
  },
  saveButton: {
    marginTop: 24,
  },
}); 