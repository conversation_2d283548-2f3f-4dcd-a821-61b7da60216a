/**
 * Create Poll Screen
 *
 * Screen for creating a new poll in a group.
 * Allows users to set a title, description, end date, and multiple options.
 * Polls can be saved as drafts or published immediately.
 *
 * Features:
 * - Form for poll details (title, description, end date)
 * - Dynamic option management (add, remove, edit)
 * - Save as draft or publish immediately
 * - Form validation
 *
 * @screen
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, KeyboardAvoidingView, Platform, TextInput } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Plus, Trash2, Save, Send, Vote, CheckSquare, Circle } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { DatePicker } from '@/components/DatePicker';
import { Card } from '@/components/Card';
import { colors } from '@/constants/colors';
import { usePollsStore } from '@/store/pollsStore';
import { useGroupsStore } from '@/store/groupsStore';

export default function CreatePollScreen() {
  // Get group ID from route params
  const { id: groupId } = useLocalSearchParams<{ id: string }>();

  // Access stores for data
  const { getGroupById } = useGroupsStore();
  const { createPoll, publishPoll, isLoading, error, clearError } = usePollsStore();

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [options, setOptions] = useState<string[]>(['', '']);
  const [endDate, setEndDate] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [allowMultipleChoice, setAllowMultipleChoice] = useState(false);

  // Get group data
  const group = getGroupById(groupId);

  /**
   * Add a new empty option to the options list
   */
  const handleAddOption = () => {
    setOptions([...options, '']);
  };

  /**
   * Remove an option at the specified index
   * Prevents removing if there would be fewer than 2 options
   * @param index - The index of the option to remove
   */
  const handleRemoveOption = (index: number) => {
    if (options.length <= 2) {
      Alert.alert('Error', 'Una votación debe tener al menos 2 opciones');
      return;
    }

    const newOptions = [...options];
    newOptions.splice(index, 1);
    setOptions(newOptions);
  };

  /**
   * Update the text of an option at the specified index
   * @param text - The new text for the option
   * @param index - The index of the option to update
   */
  const handleOptionChange = (text: string, index: number) => {
    const newOptions = [...options];
    newOptions[index] = text;
    setOptions(newOptions);
  };

  /**
   * Validate the form data
   * @returns boolean indicating if the form is valid
   */
  const validateForm = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Por favor ingresa un título para la votación');
      return false;
    }

    if (options.some(option => !option.trim())) {
      Alert.alert('Error', 'Todas las opciones deben tener texto');
      return false;
    }

    if (!endDate) {
      Alert.alert('Error', 'Por favor selecciona una fecha de cierre');
      return false;
    }

    return true;
  };

  /**
   * Save the poll as a draft
   */
  const handleSaveAsDraft = async () => {
    if (!validateForm()) return;

    try {
      const poll = await createPoll(
        groupId,
        title,
        description,
        options.filter(option => option.trim()),
        endDate,
        allowMultipleChoice
      );

      Alert.alert(
        'Éxito',
        'Votación guardada como borrador',
        [
          { text: 'OK', onPress: () => router.back() }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo guardar la votación. Intenta nuevamente.');
    }
  };

  /**
   * Create and publish the poll immediately
   */
  const handlePublish = async () => {
    if (!validateForm()) return;

    try {
      const poll = await createPoll(
        groupId,
        title,
        description,
        options.filter(option => option.trim()),
        endDate,
        allowMultipleChoice
      );

      await publishPoll(poll.id);

      Alert.alert(
        'Éxito',
        'Votación publicada correctamente',
        [
          { text: 'OK', onPress: () => router.back() }
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo publicar la votación. Intenta nuevamente.');
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Nueva Votación',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Group Info Card */}
          <Card variant="elevated" style={styles.groupInfoCard}>
            <View style={styles.groupInfoContent}>
              <Vote size={24} color={colors.primary} />
              <View style={styles.groupTextContainer}>
                <Text style={styles.groupName}>
                  {group ? `${group.sala} - ${group.año}` : 'Cargando...'}
                </Text>
                {group?.nombre && (
                  <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
                )}
              </View>
            </View>
          </Card>

          {/* Poll Information Section */}
          <Card variant="elevated" style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Información de la votación</Text>
              <Text style={styles.sectionDescription}>
                Define los detalles básicos de tu votación
              </Text>
            </View>

            <View style={styles.formContent}>
              <Input
                label="Título"
                placeholder="Ej: Elección de fecha para reunión de padres"
                value={title}
                onChangeText={setTitle}
                maxLength={100}
                required
                style={styles.inputField}
              />

              <Input
                label="Descripción (opcional)"
                placeholder="Ej: Por favor, voten por la fecha que prefieran para la próxima reunión de padres."
                value={description}
                onChangeText={setDescription}
                multiline
                numberOfLines={4}
                maxLength={500}
                style={styles.inputField}
              />

              <DatePicker
                label="Fecha de cierre"
                value={endDate}
                onChange={(date) => setEndDate(date)}
                minimumDate={new Date()}
              />

              {/* Voting Type Selection */}
              <View style={styles.votingTypeContainer}>
                <Text style={styles.votingTypeLabel}>Tipo de votación</Text>

                <View style={styles.votingTypeOptions}>
                  <TouchableOpacity
                    style={[
                      styles.votingTypeOption,
                      !allowMultipleChoice && styles.votingTypeOptionSelected
                    ]}
                    onPress={() => setAllowMultipleChoice(false)}
                  >
                    <Circle
                      size={20}
                      color={!allowMultipleChoice ? colors.primary : colors.textSecondary}
                      fill={!allowMultipleChoice ? colors.primary : 'transparent'}
                    />
                    <View style={styles.votingTypeText}>
                      <Text style={[
                        styles.votingTypeTitle,
                        !allowMultipleChoice && styles.votingTypeTitleSelected
                      ]}>
                        Opción única
                      </Text>
                      <Text style={styles.votingTypeDescription}>
                        Los votantes pueden elegir solo una opción
                      </Text>
                    </View>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.votingTypeOption,
                      allowMultipleChoice && styles.votingTypeOptionSelected
                    ]}
                    onPress={() => setAllowMultipleChoice(true)}
                  >
                    <CheckSquare
                      size={20}
                      color={allowMultipleChoice ? colors.primary : colors.textSecondary}
                      fill={allowMultipleChoice ? colors.primary : 'transparent'}
                    />
                    <View style={styles.votingTypeText}>
                      <Text style={[
                        styles.votingTypeTitle,
                        allowMultipleChoice && styles.votingTypeTitleSelected
                      ]}>
                        Selección múltiple
                      </Text>
                      <Text style={styles.votingTypeDescription}>
                        Los votantes pueden elegir varias opciones
                      </Text>
                    </View>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </Card>

          {/* Poll Options Section */}
          <Card variant="elevated" style={styles.sectionCard}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Opciones de votación</Text>
              <Text style={styles.sectionDescription}>
                Agrega al menos 2 opciones para que los miembros puedan votar
              </Text>
            </View>

            <View style={styles.optionsContent}>
              {options.map((option, index) => (
                <View key={`option-${index}`} style={styles.optionRow}>
                  <View style={styles.optionIconContainer}>
                    {allowMultipleChoice ? (
                      <CheckSquare size={14} color={colors.textSecondary} />
                    ) : (
                      <Circle size={14} color={colors.textSecondary} />
                    )}
                  </View>

                  <TextInput
                    placeholder={`Opción ${index + 1}`}
                    value={option}
                    onChangeText={(text) => handleOptionChange(text, index)}
                    maxLength={100}
                    style={styles.optionTextInput}
                    placeholderTextColor={colors.placeholder}
                  />

                  {options.length > 2 && (
                    <TouchableOpacity
                      style={styles.removeButton}
                      onPress={() => handleRemoveOption(index)}
                    >
                      <Trash2 size={14} color={colors.error} />
                    </TouchableOpacity>
                  )}
                </View>
              ))}

              <Button
                title="Agregar opción"
                onPress={handleAddOption}
                variant="outline"
                size="small"
                icon={<Plus size={16} color={colors.primary} />}
                style={styles.addButton}
              />
            </View>
          </Card>

          {/* Action Buttons */}
          <View style={styles.actionsContainer}>
            <Button
              title="Guardar borrador"
              onPress={handleSaveAsDraft}
              variant="outline"
              icon={<Save size={18} color={colors.primary} />}
              style={styles.draftButton}
              loading={isLoading}
            />

            <Button
              title="Publicar votación"
              onPress={handlePublish}
              variant="primary"
              icon={<Send size={18} color="#fff" />}
              style={styles.publishButton}
              loading={isLoading}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 32,
  },
  groupInfoCard: {
    marginBottom: 20,
  },
  groupInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  groupTextContainer: {
    marginLeft: 12,
    flex: 1,
  },
  groupName: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 2,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  sectionCard: {
    marginBottom: 20,
  },
  sectionHeader: {
    padding: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: colors.text,
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    lineHeight: 20,
  },
  formContent: {
    padding: 16,
  },
  inputField: {
    marginBottom: 16,
  },
  votingTypeContainer: {
    marginTop: 20,
  },
  votingTypeLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  votingTypeOptions: {
    gap: 8,
  },
  votingTypeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    backgroundColor: colors.card,
  },
  votingTypeOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primaryLight || '#F0F7FF',
  },
  votingTypeText: {
    marginLeft: 12,
    flex: 1,
  },
  votingTypeTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 2,
  },
  votingTypeTitleSelected: {
    color: colors.primary,
  },
  votingTypeDescription: {
    fontSize: 12,
    color: colors.textSecondary,
    lineHeight: 16,
  },
  optionsContent: {
    padding: 16,
    paddingTop: 8,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: colors.background,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 44,
  },
  optionIconContainer: {
    width: 20,
    alignItems: 'center',
    marginRight: 12,
  },
  removeButton: {
    padding: 6,
    marginLeft: 8,
    borderRadius: 16,
    backgroundColor: colors.errorLight || '#FEE2E2',
  },
  optionTextInput: {
    flex: 1,
    fontSize: 15,
    color: colors.text,
    paddingVertical: 0,
    paddingHorizontal: 0,
    height: 28,
    lineHeight: 20,
  },
  addButton: {
    marginTop: 8,
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: colors.primary,
    backgroundColor: 'transparent',
  },
  actionsContainer: {
    flexDirection: 'column',
    gap: 12,
    marginTop: 8,
    paddingHorizontal: 4,
  },
  draftButton: {
    height: 52,
    borderWidth: 2,
    borderColor: colors.textSecondary,
  },
  publishButton: {
    height: 52,
    backgroundColor: colors.primary,
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
});