import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Clock,
  CheckCircle,
  AlertCircle,
  VoteIcon,
  Users,
  Send,
  Edit,
  XCircle
} from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { usePollsStore } from '@/store/pollsStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useAuthStore } from '@/store/authStore';
import { formatDistanceToNow, format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function PollDetailScreen() {
  const { id: groupId, pollId } = useLocalSearchParams<{ id: string, pollId: string }>();
  const pollsStore = usePollsStore();
  const { getGroupById } = useGroupsStore();
  const { currentUser } = useAuthStore();

  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  // Use store methods directly to avoid context issues
  const poll = pollsStore?.getPollById ? pollsStore.getPollById(pollId) : null;
  const group = getGroupById ? getGroupById(groupId) : null;
  const userVote = pollsStore?.getUserVote ? pollsStore.getUserVote(pollId) : null;

  // Get other properties safely
  const isLoading = pollsStore?.isLoading || false;

  useEffect(() => {
    if (userVote) {
      setSelectedOption(userVote.optionId);
    }
  }, [userVote]);

  if (!poll || !group) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Votación' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Votación no encontrada</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  // Calculate total votes
  const totalVotes = poll.options?.reduce((sum, option) => sum + option.votes, 0) || 0;

  // Format dates
  const formattedEndDate = poll.endDate ? format(new Date(poll.endDate), 'EEEE d MMMM, yyyy - HH:mm', { locale: es }) : '';
  const timeRemaining = poll.endDate ? formatDistanceToNow(new Date(poll.endDate), { locale: es }) : '';

  // Check if user is creator or referente (for now, always true for demo)
  const isCreatorOrReferente = true;

  // Check if poll is expired
  const isPollExpired = poll.endDate ? new Date(poll.endDate) < new Date() : false;

  // Handle vote submission
  const handleVote = async () => {
    if (!selectedOption) {
      Alert.alert('Error', 'Por favor selecciona una opción para votar');
      return;
    }

    if (!pollsStore?.voteOnPoll) {
      Alert.alert('Error', 'Función de votación no disponible');
      return;
    }

    try {
      await pollsStore.voteOnPoll(pollId, selectedOption);
      Alert.alert('Éxito', 'Tu voto ha sido registrado');
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'No se pudo registrar tu voto');
    }
  };

  // Handle publish poll
  const handlePublish = async () => {
    if (!pollsStore?.publishPoll) {
      Alert.alert('Error', 'Función de publicación no disponible');
      return;
    }

    try {
      await pollsStore.publishPoll(pollId);
      Alert.alert('Éxito', 'La votación ha sido publicada');
    } catch (error) {
      Alert.alert('Error', error instanceof Error ? error.message : 'No se pudo publicar la votación');
    }
  };

  // Handle close poll
  const handleClose = async () => {
    if (!pollsStore?.closePoll) {
      Alert.alert('Error', 'Función de cierre no disponible');
      return;
    }

    Alert.alert(
      'Cerrar votación',
      '¿Estás seguro de que deseas cerrar esta votación? Esta acción no se puede deshacer.',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cerrar',
          style: 'destructive',
          onPress: async () => {
            try {
              await pollsStore.closePoll(pollId);
              Alert.alert('Éxito', 'La votación ha sido cerrada');
            } catch (error) {
              Alert.alert('Error', error instanceof Error ? error.message : 'No se pudo cerrar la votación');
            }
          }
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Detalle de Votación',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <View style={styles.statusContainer}>
              {poll.status === 'DRAFT' && (
                <>
                  <AlertCircle size={20} color={colors.pollDraft} />
                  <Text style={[styles.statusText, { color: colors.pollDraft }]}>Borrador</Text>
                </>
              )}
              {poll.status === 'ACTIVE' && (
                <>
                  <VoteIcon size={20} color={colors.pollActive} />
                  <Text style={[styles.statusText, { color: colors.pollActive }]}>Activa</Text>
                </>
              )}
              {poll.status === 'CLOSED' && (
                <>
                  <CheckCircle size={20} color={colors.pollClosed} />
                  <Text style={[styles.statusText, { color: colors.pollClosed }]}>Cerrada</Text>
                </>
              )}
            </View>

            <View style={styles.groupInfo}>
              <Text style={styles.groupName}>{group?.sala} - {group?.año}</Text>
              {group?.nombre && (
                <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
              )}
            </View>
          </View>

          <Card variant="elevated" style={styles.pollCard}>
            <Text style={styles.pollTitle}>{poll?.title}</Text>

            {poll?.description && (
              <Text style={styles.pollDescription}>{poll.description}</Text>
            )}

            <View style={styles.metaContainer}>
              <View style={styles.metaItem}>
                <Clock size={16} color={colors.textSecondary} />
                <Text style={styles.metaText}>
                  {poll?.status === 'CLOSED' ? 'Cerrada el' : 'Cierra el'} {formattedEndDate}
                </Text>
              </View>

              {poll?.status === 'ACTIVE' && !isPollExpired && (
                <View style={styles.metaItem}>
                  <AlertCircle size={16} color={colors.accent} />
                  <Text style={[styles.metaText, { color: colors.accent }]}>
                    Cierra en {timeRemaining}
                  </Text>
                </View>
              )}

              <View style={styles.metaItem}>
                <Users size={16} color={colors.textSecondary} />
                <Text style={styles.metaText}>
                  {totalVotes} {totalVotes === 1 ? 'voto' : 'votos'} hasta ahora
                </Text>
              </View>
            </View>
          </Card>

          {poll?.status === 'DRAFT' && isCreatorOrReferente ? (
            <View style={styles.draftActions}>
              <Text style={styles.draftText}>
                Esta votación está en modo borrador y no es visible para los miembros del grupo.
              </Text>

              <View style={styles.actionsContainer}>
                <Button
                  title="Editar"
                  onPress={() => router.push(`/group/${groupId}/poll/edit/${pollId}`)}
                  variant="outline"
                  icon={<Edit size={18} color={colors.primary} />}
                  style={styles.actionButton}
                />

                <Button
                  title="Publicar"
                  onPress={handlePublish}
                  variant="primary"
                  icon={<Send size={18} color="#fff" />}
                  style={styles.actionButton}
                  loading={isLoading}
                />
              </View>
            </View>
          ) : (
            <>
              <Text style={styles.optionsTitle}>Opciones</Text>

              {poll?.options?.map((option) => {
                const percentage = totalVotes > 0 ? Math.round((option.votes / totalVotes) * 100) : 0;
                const isSelected = selectedOption === option.id;
                const isUserVote = userVote && userVote.optionId === option.id;

                return (
                  <TouchableOpacity
                    key={option.id}
                    style={[
                      styles.optionItem,
                      isSelected && styles.selectedOption,
                      poll?.status !== 'ACTIVE' && styles.disabledOption
                    ]}
                    onPress={() => poll?.status === 'ACTIVE' && !userVote ? setSelectedOption(option.id) : null}
                    disabled={poll?.status !== 'ACTIVE' || !!userVote}
                  >
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionText,
                        isSelected && styles.selectedOptionText
                      ]}>
                        {option.text}
                      </Text>

                      {isUserVote && (
                        <Text style={styles.yourVoteText}>Tu voto</Text>
                      )}
                    </View>

                    <View style={styles.optionStats}>
                      <Text style={styles.voteCount}>{option.votes} {option.votes === 1 ? 'voto' : 'votos'}</Text>
                      <Text style={styles.votePercentage}>{percentage}%</Text>
                    </View>

                    <View style={styles.progressBarContainer}>
                      <View
                        style={[
                          styles.progressBar,
                          { width: `${percentage}%` },
                          isUserVote && styles.userVoteProgressBar
                        ]}
                      />
                    </View>
                  </TouchableOpacity>
                );
              })}

              {poll?.status === 'ACTIVE' && !userVote && (
                <Button
                  title="Enviar voto"
                  onPress={handleVote}
                  variant="primary"
                  icon={<Send size={18} color="#fff" />}
                  style={styles.voteButton}
                  loading={isLoading}
                  disabled={!selectedOption}
                />
              )}

              {poll?.status === 'ACTIVE' && userVote && (
                <Text style={styles.alreadyVotedText}>
                  Ya has votado en esta encuesta. Gracias por participar.
                </Text>
              )}

              {poll?.status === 'ACTIVE' && isCreatorOrReferente && (
                <Button
                  title="Cerrar votación"
                  onPress={handleClose}
                  variant="outline"
                  icon={<XCircle size={18} color={colors.error} />}
                  style={[styles.closeButton, { borderColor: colors.error }]}
                  textStyle={{ color: colors.error }}
                  loading={isLoading}
                />
              )}
            </>
          )}
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 12,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  groupInfo: {
    marginBottom: 8,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  pollCard: {
    padding: 16,
    marginBottom: 24,
  },
  pollTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 8,
  },
  pollDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  metaContainer: {
    gap: 8,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  optionsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 16,
  },
  optionItem: {
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedOption: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  },
  disabledOption: {
    opacity: 0.8,
  },
  optionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionText: {
    fontSize: 16,
    color: colors.text,
    flex: 1,
  },
  selectedOptionText: {
    fontWeight: '500',
    color: colors.primary,
  },
  yourVoteText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '600',
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  optionStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  voteCount: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  votePercentage: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: colors.border,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: colors.primary,
  },
  userVoteProgressBar: {
    backgroundColor: colors.accent,
  },
  voteButton: {
    marginTop: 24,
    marginBottom: 16,
  },
  alreadyVotedText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  closeButton: {
    marginTop: 8,
    marginBottom: 24,
  },
  draftActions: {
    marginTop: 16,
  },
  draftText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 16,
    backgroundColor: colors.cardBackground,
    padding: 16,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.pollDraft,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 16,
  },
  actionButton: {
    flex: 1,
  },
});