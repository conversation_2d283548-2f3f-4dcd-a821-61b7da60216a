import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, RefreshControl } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { UserCheck, UserX, Plus } from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { MemberItem } from '@/components/MemberItem';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function GroupMembersScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getGroupById, getMembershipsByGroupId, isReferente, toggleReferenteStatus, removeMember } = useGroupsStore();
  const { kids, getKids, getKidsByIds } = useKidsStore();
  const { users, fetchUsersByIds } = useAuthStore();

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Data state
  const [group, setGroup] = useState<any>(null);
  const [isUserReferente, setIsUserReferente] = useState(false);
  const [membersWithDetails, setMembersWithDetails] = useState<any[]>([]);

  // Add a timestamp to track when data was last loaded
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  // Minimum time between loads in milliseconds (5 seconds)
  const MIN_LOAD_INTERVAL = 5000;

  // Process memberships data and update state
  const processMembershipsData = useCallback(() => {
    // Get fresh data from stores
    const currentGroup = getGroupById(id);
    const currentMemberships = getMembershipsByGroupId(id);
    const currentIsReferente = isReferente(id);

    setGroup(currentGroup);
    setIsUserReferente(currentIsReferente);

    // Group memberships by parent (userId)
    const membershipsByParent: Record<string, any> = {};

    currentMemberships.forEach(membership => {
      if (membership.userId) {
        if (!membershipsByParent[membership.userId]) {
          // Find the parent in the users array
          const parent = users.find(u => u.id === membership.userId) || {
            id: membership.userId,
            fullName: 'Usuario',
            firstName: '',
            lastName: '',
            email: 'No disponible'
          };

          membershipsByParent[membership.userId] = {
            parent,
            kids: [],
            memberships: [],
            isReferente: false
          };
        }

        // Find the kid in the kids array or groupKids array
        const allKids = [...kids, ...useKidsStore.getState().groupKids];
        const kid = allKids.find(k => k.id === membership.kidId);

        // Add kid if not already present
        if (kid && !membershipsByParent[membership.userId].kids.some((k: any) => k.id === kid.id)) {
          membershipsByParent[membership.userId].kids.push(kid);
        }

        // Add membership
        membershipsByParent[membership.userId].memberships.push(membership);

        // Check if any membership for this parent is referente
        if (membership.isReferente) {
          membershipsByParent[membership.userId].isReferente = true;
        }
      }
    });

    // Convert to array
    const result = Object.values(membershipsByParent);
    setMembersWithDetails(result);
  }, [id, getGroupById, getMembershipsByGroupId, isReferente, kids, users, useKidsStore]);

  // Function to load data on demand (when swiping down or on initial load)
  const loadData = useCallback(async (_forceRefresh = false) => {
    try {
      // Check if we've loaded data recently (within the last 5 seconds)
      const now = Date.now();
      if (!_forceRefresh && lastLoadTime > 0 && now - lastLoadTime < MIN_LOAD_INTERVAL) {
        console.log('GroupMembersScreen: Skipping data load, too soon since last load');
        return;
      }

      // Update last load time immediately to prevent multiple concurrent loads
      setLastLoadTime(now);

      setIsLoading(true);
      // Get group memberships
      const memberships = getMembershipsByGroupId(id);

      // Extract user IDs and kid IDs from memberships
      const userIds = memberships.map(m => m.userId);
      const kidIds = memberships.filter(m => m.kidId).map(m => m.kidId as string);

      // Create an array of promises to fetch all data in parallel
      const fetchPromises = [];

      // Fetch users if we have any user IDs
      // Always force a refresh when loading the members screen
      if (userIds.length > 0) {
        // Force a refresh by temporarily clearing the lastFetchedUserIds in the auth store
        const authStore = useAuthStore.getState();
        const originalLastFetchedUserIds = authStore.lastFetchedUserIds;
        authStore.lastFetchedUserIds = [];

        // Now fetch the users (this will always fetch fresh data)
        fetchPromises.push(fetchUsersByIds(userIds));
      }

      // Fetch all kids in the group by their IDs
      if (kidIds.length > 0) {
        // Use getKidsByIds to fetch only the kids in this group
        // Always force refresh to ensure we have the latest data
        fetchPromises.push(getKidsByIds(kidIds, true));
      }

      // Wait for all data to be fetched
      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      }

      // Process the data after fetching
      processMembershipsData();

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading group members data:', error);
      setIsLoading(false);
    }
  }, [id, getMembershipsByGroupId, fetchUsersByIds, processMembershipsData, getKidsByIds, lastLoadTime]);

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData(true); // Force refresh when manually pulled
    setRefreshing(false);
  }, [loadData]);

  // Load data only when component mounts or ID changes, not continuously
  useEffect(() => {
    // Always load fresh data when the component mounts
    loadData(true);

    // Cleanup function to prevent memory leaks
    return () => {
      // Component unmounting
    };
  }, [id]); // Only depend on id, not loadData

  const handleToggleReferente = async (membershipId: string, currentStatus: boolean) => {
    try {
      setIsLoading(true);
      await toggleReferenteStatus(membershipId, !currentStatus);

      // Process data after the action to update the UI
      processMembershipsData();

      Alert.alert(
        'Estado Actualizado',
        `El usuario ahora ${!currentStatus ? 'es' : 'no es'} referente del grupo.`
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo actualizar el estado de referente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (membershipId: string, kidName: string) => {
    Alert.alert(
      'Eliminar Miembro',
      `¿Estás seguro de que deseas eliminar a ${kidName} del grupo?`,
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoading(true);
              await removeMember(membershipId);

              // Process data after the action to update the UI
              processMembershipsData();

              Alert.alert('Miembro Eliminado', 'El miembro ha sido eliminado del grupo.');
            } catch (error) {
              Alert.alert('Error', 'No se pudo eliminar al miembro del grupo.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  if (!group) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Miembros del Grupo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>Cargando grupo...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Miembros del Grupo',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        {isLoading ? (
          <View style={styles.centerContainer}>
            <Text style={styles.loadingText}>Cargando miembros...</Text>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }>
            <Card variant="elevated" style={styles.groupInfoCard}>
              <Text style={styles.groupName}>{group.institutionName}</Text>
              <Text style={styles.groupDetail}>{group.sala} - {group.año}</Text>
              {group.nombre && (
                <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
              )}
            </Card>

            <View style={styles.header}>
              <Text style={styles.title}>Miembros</Text>
              <Button
                title="Agregar"
                onPress={() => router.push(`/group/join?groupId=${id}`)}
                variant="outline"
                size="small"
                icon={<Plus size={16} color={colors.primary} />}
              />
            </View>

            <View style={styles.membersContainer}>
              {membersWithDetails.length > 0 ? (
                membersWithDetails.map((memberDetail, index) => {
                  // Get the primary membership (first one or referente one)
                  const primaryMembership = memberDetail.isReferente
                    ? memberDetail.memberships.find((m: any) => m.isReferente)
                    : memberDetail.memberships[0];

                  // Filter out undefined kids
                  const validKids = (memberDetail.kids || []).filter(Boolean);

                  return (
                    <MemberItem
                      key={`member-${memberDetail.parent?.id || primaryMembership.id}-${index}`}
                      kids={validKids}
                      parents={[memberDetail.parent]}
                      isReferente={memberDetail.isReferente}
                      canManage={isUserReferente}
                      onToggleReferente={isUserReferente ?
                        () => handleToggleReferente(primaryMembership.id, memberDetail.isReferente) :
                        undefined
                      }
                      onRemove={isUserReferente ?
                        () => handleRemoveMember(primaryMembership.id, validKids[0]?.fullName || 'este miembro') :
                        undefined
                      }
                    />
                  );
                })
              ) : (
                <Text style={styles.emptyText}>
                  No hay miembros en este grupo
                </Text>
              )}
            </View>

            {isUserReferente && (
              <View style={styles.infoSection}>
                <Text style={styles.infoTitle}>Información para Referentes</Text>
                <Text style={styles.infoText}>
                  Como referente del grupo, puedes:
                </Text>
                <View style={styles.infoList}>
                  <View style={styles.infoItem}>
                    <UserCheck size={16} color={colors.success} />
                    <Text style={styles.infoItemText}>
                      Designar a otros padres como referentes
                    </Text>
                  </View>
                  <View style={styles.infoItem}>
                    <UserX size={16} color={colors.error} />
                    <Text style={styles.infoItemText}>
                      Eliminar miembros del grupo
                    </Text>
                  </View>
                </View>
              </View>
            )}
          </ScrollView>
        )}
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  groupInfoCard: {
    marginBottom: 16,
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  membersContainer: {
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
  },
  infoSection: {
    backgroundColor: colors.card,
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
  },
  infoList: {
    gap: 8,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoItemText: {
    fontSize: 14,
    color: colors.text,
  },
});
