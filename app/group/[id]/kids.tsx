import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, ScrollView, RefreshControl, Linking } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MessageCircle } from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { KidItem } from '@/components/KidItem';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { KidsService } from '@/services/kidsService';
import { AuthService } from '@/services/authService';

export default function GroupKidsScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { getGroupById, getMembershipsByGroupId, isReferente } = useGroupsStore();
  const { kids, getKidsByIds } = useKidsStore();
  const { users, fetchUsersByIds } = useAuthStore();

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Data state
  const [group, setGroup] = useState<any>(null);
  const [isUserReferente, setIsUserReferente] = useState(false);
  const [kidsWithDetails, setKidsWithDetails] = useState<any[]>([]);

  // Add a timestamp to track when data was last loaded
  const [lastLoadTime, setLastLoadTime] = useState<number>(0);
  // Minimum time between loads in milliseconds (5 seconds)
  const MIN_LOAD_INTERVAL = 5000;

  // Process memberships data and update state
  const processMembershipsData = useCallback(async () => {
    // Get fresh data from stores
    const currentGroup = getGroupById(id);
    const currentMemberships = getMembershipsByGroupId(id);
    const currentIsReferente = isReferente(id);

    setGroup(currentGroup);
    setIsUserReferente(currentIsReferente);

    // Group memberships by kid (kidId)
    const membershipsByKid: Record<string, any> = {};

    currentMemberships.forEach(membership => {
      if (membership.kidId) {
        if (!membershipsByKid[membership.kidId]) {
          // Find the kid in the kids array or groupKids array
          const allKids = [...kids, ...useKidsStore.getState().groupKids];
          const kid = allKids.find(k => k.id === membership.kidId);

          membershipsByKid[membership.kidId] = {
            kid: kid || {
              id: membership.kidId,
              fullName: 'Niño',
              firstName: '',
              lastName: '',
              birthDate: null
            },
            parents: [],
            memberships: []
          };
        }

        // Add membership
        membershipsByKid[membership.kidId].memberships.push(membership);
      }
    });

    // Now fetch the actual parents for each kid from the kid_parents table
    const result = Object.values(membershipsByKid);

    for (const kidDetail of result) {
      try {
        // Get parent IDs from kid_parents table
        const parentIds = await KidsService.getParentsForKid(kidDetail.kid.id);

        if (parentIds.length > 0) {
          // Fetch parent user data
          const { data: parentUsers, error } = await AuthService.getUsersByIds(parentIds);
          if (!error && parentUsers) {
            kidDetail.parents = parentUsers;
          }
        }
      } catch (error) {
        console.error('Error fetching parents for kid:', kidDetail.kid.id, error);
        kidDetail.parents = [];
      }
    }

    // Sort kids alphabetically by full name
    result.sort((a: any, b: any) => {
      const aName = a.kid?.fullName || `${a.kid?.firstName || ''} ${a.kid?.lastName || ''}`.trim();
      const bName = b.kid?.fullName || `${b.kid?.firstName || ''} ${b.kid?.lastName || ''}`.trim();

      return aName.localeCompare(bName, 'es', { sensitivity: 'base' });
    });

    setKidsWithDetails(result);
  }, [id, getGroupById, getMembershipsByGroupId, isReferente, kids, useKidsStore]);

  // Function to load data on demand (when swiping down or on initial load)
  const loadData = useCallback(async (_forceRefresh = false) => {
    try {
      // Check if we've loaded data recently (within the last 5 seconds)
      const now = Date.now();
      if (!_forceRefresh && lastLoadTime > 0 && now - lastLoadTime < MIN_LOAD_INTERVAL) {
        console.log('GroupKidsScreen: Skipping data load, too soon since last load');
        return;
      }

      // Update last load time immediately to prevent multiple concurrent loads
      setLastLoadTime(now);

      setIsLoading(true);
      // Get group memberships
      const memberships = getMembershipsByGroupId(id);

      // Extract user IDs and kid IDs from memberships
      const userIds = memberships.map(m => m.userId);
      const kidIds = memberships.filter(m => m.kidId).map(m => m.kidId as string);

      // Create an array of promises to fetch all data in parallel
      const fetchPromises = [];

      // Fetch users if we have any user IDs
      if (userIds.length > 0) {
        // Force a refresh by temporarily clearing the lastFetchedUserIds in the auth store
        const authStore = useAuthStore.getState();
        const originalLastFetchedUserIds = authStore.lastFetchedUserIds;
        authStore.lastFetchedUserIds = [];

        // Now fetch the users (this will always fetch fresh data)
        fetchPromises.push(fetchUsersByIds(userIds));
      }

      // Fetch all kids in the group by their IDs
      if (kidIds.length > 0) {
        // Use getKidsByIds to fetch only the kids in this group
        // Always force refresh to ensure we have the latest data
        fetchPromises.push(getKidsByIds(kidIds, true));
      }

      // Wait for all data to be fetched
      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      }

      // Process the data after fetching
      await processMembershipsData();

      setIsLoading(false);
    } catch (error) {
      console.error('Error loading group kids data:', error);
      setIsLoading(false);
    }
  }, [id, getMembershipsByGroupId, fetchUsersByIds, processMembershipsData, getKidsByIds, lastLoadTime]);

  // Handle pull-to-refresh
  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadData(true); // Force refresh when manually pulled
    setRefreshing(false);
  }, [loadData]);

  // Handle WhatsApp share
  const handleWhatsAppShare = useCallback(() => {
    if (!group || kidsWithDetails.length === 0) {
      return;
    }

    // Format group name
    const groupName = group.nombre || `${group.sala} - ${group.año}`;

    // Create the message header
    let message = `Lista de Grupo "${groupName}" del colegio "${group.colegio}" año "${group.año}"\n\n`;

    // Add each kid to the message
    kidsWithDetails.forEach((kidDetail) => {
      const kid = kidDetail.kid;
      if (kid) {
        // Format kid name with nickname if available
        let kidName = kid.fullName || `${kid.firstName || ''} ${kid.lastName || ''}`.trim();
        if (kid.nickname) {
          kidName += ` (${kid.nickname})`;
        }

        // Format birth date
        let birthDate = 'Fecha no disponible';
        if (kid.birthDate) {
          try {
            const date = new Date(kid.birthDate);
            birthDate = date.toLocaleDateString('es-ES', {
              day: '2-digit',
              month: '2-digit',
            });
          } catch (e) {
            birthDate = 'Fecha inválida';
          }
        }

        message += `• ${kidName}, Fecha de cumpleaños: ${birthDate}\n`;
      }
    });

    // Encode the message for WhatsApp
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `whatsapp://send?text=${encodedMessage}`;

    // Try to open WhatsApp
    Linking.canOpenURL(whatsappUrl)
      .then(supported => {
        if (supported) {
          Linking.openURL(whatsappUrl);
        } else {
          // Fallback to web WhatsApp
          const webWhatsappUrl = `https://wa.me/?text=${encodedMessage}`;
          Linking.openURL(webWhatsappUrl);
        }
      })
      .catch(() => {
        // Fallback to web WhatsApp
        const webWhatsappUrl = `https://wa.me/?text=${encodedMessage}`;
        Linking.openURL(webWhatsappUrl);
      });
  }, [group, kidsWithDetails]);

  // Load data only when component mounts or ID changes, not continuously
  useEffect(() => {
    // Always load fresh data when the component mounts
    loadData(true);

    // Cleanup function to prevent memory leaks
    return () => {
      // Component unmounting
    };
  }, [id]); // Only depend on id, not loadData

  if (!group) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Qids del Grupo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>Cargando grupo...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Qids del Grupo',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        {isLoading ? (
          <View style={styles.centerContainer}>
            <Text style={styles.loadingText}>Cargando qids...</Text>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={[colors.primary]}
                tintColor={colors.primary}
              />
            }>
            <Card variant="elevated" style={styles.groupInfoCard}>
              <Text style={styles.groupName}>{group.institutionName}</Text>
              <Text style={styles.groupDetail}>{group.sala} - {group.año}</Text>
              {group.nombre && (
                <Text style={styles.groupDetail}>Grupo: {group.nombre}</Text>
              )}
            </Card>

            <View style={styles.header}>
              <Text style={styles.title}>Qids ({kidsWithDetails.length})</Text>
              {kidsWithDetails.length > 0 && (
                <Button
                  title="Enviar lista por WhatsApp"
                  onPress={handleWhatsAppShare}
                  variant="outline"
                  size="small"
                  icon={<MessageCircle size={16} color={colors.primary} />}
                />
              )}
            </View>

            <View style={styles.kidsContainer}>
              {kidsWithDetails.length > 0 ? (
                kidsWithDetails.map((kidDetail, index) => {
                  return (
                    <KidItem
                      key={`kid-${kidDetail.kid?.id || index}`}
                      kid={kidDetail.kid}
                      parents={kidDetail.parents || []}
                    />
                  );
                })
              ) : (
                <Text style={styles.emptyText}>
                  No hay qids en este grupo
                </Text>
              )}
            </View>
          </ScrollView>
        )}
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
  },
  groupInfoCard: {
    marginBottom: 16,
  },
  groupName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  kidsContainer: {
    marginBottom: 24,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 16,
  },
});
