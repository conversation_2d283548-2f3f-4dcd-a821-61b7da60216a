/**
 * Group Detail Screen
 *
 * Main screen for viewing and interacting with a school group.
 * Displays group information and provides tabs for different functionalities:
 * - Announcements
 * - Collections (Colectas)
 * - Polls
 * - Events
 *
 * Features:
 * - Group information display
 * - Member list preview
 * - Tab navigation between different group features
 * - Filtering options for collections and polls
 * - Create new items (announcements, collections, polls, events)
 * - View all items with appropriate empty states
 *
 * @screen
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Users,
  Bell,
  DollarSign,
  Plus,
  ChevronRight,
  Calendar,
  VoteIcon,
} from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Badge } from '@/components/Badge';
import { AnnouncementCard } from '@/components/AnnouncementCard';
import { ColectaCard } from '@/components/ColectaCard';
import { PollCard } from '@/components/PollCard';
import { EventCard } from '@/components/EventCard';
import { Avatar } from '@/components/Avatar';
import { colors } from '@/constants/colors';
import { useGroupsStore } from '@/store/groupsStore';
import { useKidsStore } from '@/store/kidsStore';
import { useAnnouncementsStore } from '@/store/announcementsStore';
import { useColectasStore } from '@/store/colectasStore';
import { usePollsStore } from '@/store/pollsStore';
import { useEventsStore } from '@/store/eventsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { CalendarView } from '@/components/CalendarView';
import { EmptyState } from '@/components/EmptyState';

export default function GroupDetailScreen() {
  // Get group ID from route params
  const { id } = useLocalSearchParams<{ id: string }>();

  // Access stores for data
  const { getGroupById, memberships, isLoading: groupsLoading, getGroups } = useGroupsStore();
  const { kids, getKidsByIds, groupKids } = useKidsStore();
  const { announcements, getAnnouncementsByGroupId } = useAnnouncementsStore();
  const { colectas, getColectasByGroupId, contributions } = useColectasStore();
  const pollsStore = usePollsStore();
  const { polls, votes, getPollsByGroupId } = pollsStore;
  const { events, getEventsByGroupId } = useEventsStore();
  const { currentUser, users } = useAuthStore();

  // Local state for UI
  const [activeTab, setActiveTab] = useState<'colectas' | 'polls' | 'events' | 'calendar'>('events');
  const [colectaFilter, setColectaFilter] = useState<'all' | 'pendiente' | 'pagas'>('pendiente');
  const [pollFilter, setPollFilter] = useState<'all' | 'active' | 'closed' | 'draft'>('active');
  const [refreshing, setRefreshing] = useState(false);
  const [hasReloaded, setHasReloaded] = useState(false);

  /**
   * Get the current group data
   * Memoized to prevent unnecessary re-renders
   */
  const group = useCallback(() => {
    if (!id) return null;
    return getGroupById(id);
  }, [id, getGroupById]);

  const currentGroup = group();

  /**
   * Get kids that belong to this group
   * Memoized to prevent unnecessary re-renders
   */
  const kidsInGroup = useCallback(() => {
    if (!id) return [];

    // Get all memberships for this group from props
    const groupMemberships = memberships ? memberships.filter(m => m.groupId === id) : [];

    // Get unique kid IDs from memberships
    const kidIds = new Set();
    groupMemberships.forEach(membership => {
      if (membership.kidId) {
        kidIds.add(membership.kidId);
      }
    });

    // Use groupKids from props

    // First try to find kids in the groupKids array (all kids in groups)
    // Then fall back to the kids array (only current user's kids)
    const allKids = [...(groupKids || []), ...(kids || [])];

    // Filter kids by these IDs, avoiding duplicates
    const uniqueKids: any[] = [];
    const seenIds = new Set();

    allKids.forEach((kid: any) => {
      if (kidIds.has(kid.id) && !seenIds.has(kid.id)) {
        uniqueKids.push(kid);
        seenIds.add(kid.id);
      }
    });

    return uniqueKids;
  }, [kids, id, memberships, groupKids]);

  /**
   * Check if user is a referent (admin) of the group
   * For demo purposes, always returns true
   */
  const isReferente = useCallback(() => {
    return true;
  }, []);

  // Everyone has access now
  const hasAccess = true;

  /**
   * Sort announcements by date (newest first)
   */
  const sortedAnnouncements = announcements ? [...announcements]
    .filter(a => a.groupId === id)
    .sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    }) : [];

  /**
   * Filter colectas based on selected filter
   */
  const filteredColectas = useCallback(() => {
    if (!colectas) return [];

    const groupColectas = colectas.filter(c => c.groupId === id);

    // Apply filtering logic
    let filteredResults;
    if (colectaFilter === 'all') {
      filteredResults = groupColectas;
    } else {
      // We already have currentUser from the top-level hook
      if (!currentUser) return [];

      // Get user's kids in this group using the memberships from props
      const groupMemberships = memberships ? memberships.filter(m => m.groupId === id) : [];
      const userKidsInGroup = kids.filter(kid =>
        groupMemberships.some(m => m.kidId === kid.id && m.userId === currentUser.id)
      );
      const userKidIds = userKidsInGroup.map(kid => kid.id);

      // We already have contributions from the top-level hook

      filteredResults = groupColectas.filter(colecta => {
        // Get contributions for this colecta made by the user's kids with status REGISTRADA
        const userRegisteredContributions = contributions.filter(c =>
          c.colectaId === colecta.id &&
          userKidIds.includes(c.kidId) &&
          c.status === 'REGISTRADA'
        );

        if (colectaFilter === 'pendiente') {
          // Pendientes = Colectas vigentes (ACTIVA) que no tengan realizado aporte por ninguno de los qids vinculado al usuario que este agregado al grupo
          return colecta.status === 'ACTIVA' && userRegisteredContributions.length === 0;
        }

        if (colectaFilter === 'pagas') {
          // Pagadas = Vigentes (ACTIVA) o finalizadas (FINALIZADA) para las que hayan aportes registrados por un qids vinculado al usuario que este agregado al grupo
          return (colecta.status === 'ACTIVA' || colecta.status === 'FINALIZADA') && userRegisteredContributions.length > 0;
        }

        return true;
      });
    }

    // Sort by fecha límite descendente (most recent deadline first)
    return filteredResults.sort((a, b) => {
      const dateA = new Date(a.fechaFin);
      const dateB = new Date(b.fechaFin);
      return dateB.getTime() - dateA.getTime();
    });
  }, [colectas, colectaFilter, id, kids, memberships, currentUser, contributions]);

  /**
   * Filter polls based on selected filter
   */
  const filteredPolls = useCallback(() => {
    if (!polls) return [];

    const groupPolls = polls.filter(p => p.groupId === id);

    if (pollFilter === 'all') return groupPolls;
    return groupPolls.filter(p => {
      if (pollFilter === 'active') return p.status === 'ACTIVE';
      if (pollFilter === 'closed') return p.status === 'CLOSED';
      if (pollFilter === 'draft') return p.status === 'DRAFT';
      return true;
    });
  }, [polls, pollFilter, id]);

  // Filter events (all events for now)
  const groupEvents = events ? events.filter(e => e.groupId === id) : [];

  /**
   * Load data for the group
   * @param forceRefresh Whether to force a refresh of all data
   */
  const loadGroupData = async (forceRefresh = false) => {
    if (!id) return;

    console.log(`GroupDetailScreen: ${forceRefresh ? 'Force refreshing' : 'Loading'} data for group:`, id);

    try {
      // Check if we already have data for this group
      const hasAnnouncements = !forceRefresh && announcements?.some(a => a.groupId === id);
      const hasColectas = !forceRefresh && colectas?.some(c => c.groupId === id);
      const hasPolls = !forceRefresh && polls?.some(p => p.groupId === id);
      const hasEvents = !forceRefresh && events?.some(e => e.groupId === id);

      // Only fetch data we don't already have or if forcing refresh
      const fetchPromises = [];

      // Get all memberships for this group to fetch all kids
      const groupMemberships = memberships ? memberships.filter(m => m.groupId === id) : [];

      // Get unique kid IDs from memberships
      const kidIds = [...new Set(
        groupMemberships
          .filter(m => m.kidId)
          .map(m => m.kidId as string)
      )];

      // Only fetch kids if we don't have them or if forcing refresh
      if (kidIds.length > 0 && (forceRefresh || !groupKids?.some(k => kidIds.includes(k.id)))) {
        console.log('GroupDetailScreen: Fetching kids data');
        fetchPromises.push(getKidsByIds(kidIds, forceRefresh));
      }

      // Only fetch data we don't have or if forcing refresh
      if (!hasAnnouncements) {
        console.log('GroupDetailScreen: Fetching announcements');
        fetchPromises.push(getAnnouncementsByGroupId(id));
      }
      if (!hasColectas) {
        console.log('GroupDetailScreen: Fetching colectas');
        fetchPromises.push(getColectasByGroupId(id));
      }
      if (!hasPolls) {
        console.log('GroupDetailScreen: Fetching polls');
        fetchPromises.push(getPollsByGroupId(id));
      }
      if (!hasEvents) {
        console.log('GroupDetailScreen: Fetching events');
        fetchPromises.push(getEventsByGroupId(id));
      }

      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      } else {
        console.log('GroupDetailScreen: Using cached data');
      }

      console.log('GroupDetailScreen: Data loaded successfully');
    } catch (error) {
      console.error('GroupDetailScreen: Error loading data:', error);
    }
  };

  // Handle pull-to-refresh
  const onRefresh = async () => {
    setRefreshing(true);
    await loadGroupData(true); // Force refresh
    setRefreshing(false);
  };

  // Load data only when component mounts or ID changes, not continuously
  useEffect(() => {
    if (id) {
      loadGroupData();
    }
  }, [id]); // Only depend on id to prevent continuous fetching

  // If group is not found and not loading, try to reload groups ONCE
  useEffect(() => {
    if (!groupsLoading && !currentGroup && !hasReloaded) {
      console.log('[GroupDetailScreen] Group not found, forcing reload of group list. id:', id);
      setHasReloaded(true);
      getGroups(); // Force refresh
    }
  }, [groupsLoading, currentGroup, hasReloaded, id, getGroups]);

  // If group is not found after reload
  if (!currentGroup && hasReloaded && !groupsLoading) {
    getGroups().then(allGroups => {
      console.log('[GroupDetailScreen] Group not found after reload. id:', id, 'All groups:', allGroups);
    });
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Grupo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Grupo no encontrado</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  if (!currentGroup) return null;
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Detalles del Grupo',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }>

          {/* Group Info Card */}
          <Card variant="elevated" style={styles.groupInfoCard}>
            <View style={styles.groupHeader}>
              <View>
                <Text style={styles.groupName}>{currentGroup.institutionName || 'Institución'}</Text>
                <Text style={styles.groupDetail}>{currentGroup.sala} - {currentGroup.año}</Text>
                {currentGroup.nombre && (
                  <Text style={styles.groupDetail}>Grupo: {currentGroup.nombre}</Text>
                )}
              </View>
              {isReferente() && (
                <Badge label="Referente" variant="primary" size="medium" />
              )}
            </View>
          </Card>

          {hasAccess && (
            <>
              {/* Members Section */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Miembros</Text>

                <View style={styles.membersRow}>
                  {/* Adults Card */}
                  <TouchableOpacity
                    style={styles.memberCardContainer}
                    onPress={() => router.push(`/group/${id}/adults`)}
                    activeOpacity={0.7}
                  >
                    <Card variant="outlined" style={styles.memberCard}>
                      <Text style={styles.memberCardTitle}>Adultos</Text>
                      <Text style={styles.memberCardCount}>
                        {currentGroup?.members?.length || 0}
                      </Text>
                    </Card>
                  </TouchableOpacity>

                  {/* Kids Card */}
                  <TouchableOpacity
                    style={styles.memberCardContainer}
                    onPress={() => router.push(`/group/${id}/kids`)}
                    activeOpacity={0.7}
                  >
                    <Card variant="outlined" style={styles.memberCard}>
                      <Text style={styles.memberCardTitle}>Qids</Text>
                      <Text style={styles.memberCardCount}>
                        {kidsInGroup().length}
                      </Text>
                    </Card>
                  </TouchableOpacity>
                </View>
              </View>

              {/* Tabs Navigation */}
              <View style={styles.tabsContainer}>
                <TouchableOpacity
                  style={[
                    styles.tab,
                    activeTab === 'events' && styles.activeTab
                  ]}
                  onPress={() => setActiveTab('events')}
                >
                  <Calendar
                    size={18}
                    color={activeTab === 'events' ? colors.primary : colors.textSecondary}
                  />
                  <Text
                    style={[
                      styles.tabText,
                      activeTab === 'events' && styles.activeTabText
                    ]}
                  >
                    Eventos
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.tab,
                    activeTab === 'colectas' && styles.activeTab
                  ]}
                  onPress={() => setActiveTab('colectas')}
                >
                  <DollarSign
                    size={18}
                    color={activeTab === 'colectas' ? colors.primary : colors.textSecondary}
                  />
                  <Text
                    style={[
                      styles.tabText,
                      activeTab === 'colectas' && styles.activeTabText
                    ]}
                  >
                    Colectas
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[
                    styles.tab,
                    activeTab === 'polls' && styles.activeTab
                  ]}
                  onPress={() => setActiveTab('polls')}
                >
                  <VoteIcon
                    size={18}
                    color={activeTab === 'polls' ? colors.primary : colors.textSecondary}
                  />
                  <Text
                    style={[
                      styles.tabText,
                      activeTab === 'polls' && styles.activeTabText
                    ]}
                  >
                    Votaciones
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Events Tab Content */}
              {activeTab === 'events' && (
                <View style={styles.contentSection}>
                  {isReferente() && (
                    <Button
                      title="Nuevo Evento"
                      onPress={() => router.push(`/group/${id}/event/create`)}
                      variant="outline"
                      icon={<Plus size={18} color={colors.primary} />}
                      style={styles.newButton}
                    />
                  )}

                  {groupEvents.length > 0 ? (
                    groupEvents.map(event => (
                      <EventCard
                        key={event.id}
                        event={event}
                        onPress={() => router.push(`/group/${id}/event/${event.id}`)}
                      />
                    ))
                  ) : (
                    <EmptyState
                      title="No hay eventos"
                      message="No hay eventos programados en este grupo"
                      icon={<Calendar size={40} color={colors.textSecondary} />}
                    />
                  )}
                </View>
              )}

              {/* Colectas Tab Content */}
              {activeTab === 'colectas' && (
                <View style={styles.contentSection}>
                  {isReferente() && (
                    <Button
                      title="Nueva Colecta"
                      onPress={() => router.push(`/group/${id}/colecta/create`)}
                      variant="outline"
                      icon={<Plus size={18} color={colors.primary} />}
                      style={styles.newButton}
                    />
                  )}

                  <View style={styles.filterContainer}>
                    <Text style={styles.filterLabel}>Filtrar por:</Text>
                    <View style={styles.filterButtons}>
                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          colectaFilter === 'all' && styles.activeFilterButton
                        ]}
                        onPress={() => setColectaFilter('all')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          colectaFilter === 'all' && styles.activeFilterButtonText
                        ]}>
                          Todas
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          colectaFilter === 'pendiente' && styles.activeFilterButton
                        ]}
                        onPress={() => setColectaFilter('pendiente')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          colectaFilter === 'pendiente' && styles.activeFilterButtonText
                        ]}>
                          Pendientes
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          colectaFilter === 'pagas' && styles.activeFilterButton
                        ]}
                        onPress={() => setColectaFilter('pagas')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          colectaFilter === 'pagas' && styles.activeFilterButtonText
                        ]}>
                          Pagadas
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {filteredColectas().length > 0 ? (
                    filteredColectas().map(colecta => {
                      // Calculate actual contributions count for this colecta
                      const actualContributions = contributions.filter(c => c.colectaId === colecta.id).length;

                      return (
                        <ColectaCard
                          key={colecta.id}
                          motivo={colecta.motivo}
                          descripcion={colecta.descripcion}
                          fechaFin={colecta.fechaFin}
                          baseAmount={colecta.baseAmount}
                          totalCollected={colecta.totalCollected || 0}
                          actualContributions={actualContributions}
                          status={colecta.status}
                          aliasCbu={colecta.aliasCbu}
                          onPress={() => router.push(`/group/${id}/colecta/${colecta.id}`)}
                        />
                      );
                    })
                  ) : (
                    <EmptyState
                      title="No hay colectas"
                      message={
                        colectaFilter === 'pendiente' ? 'No hay colectas pendientes' :
                        colectaFilter === 'pagas' ? 'No hay colectas pagadas' :
                        'No hay colectas en este grupo'
                      }
                      icon={<DollarSign size={40} color={colors.textSecondary} />}
                    />
                  )}
                </View>
              )}

              {/* Polls Tab Content */}
              {activeTab === 'polls' && (
                <View style={styles.contentSection}>
                  {isReferente() && (
                    <Button
                      title="Nueva Votación"
                      onPress={() => router.push(`/group/${id}/poll/create`)}
                      variant="outline"
                      icon={<Plus size={18} color={colors.primary} />}
                      style={styles.newButton}
                    />
                  )}

                  <View style={styles.filterContainer}>
                    <Text style={styles.filterLabel}>Filtrar por:</Text>
                    <View style={styles.filterButtons}>
                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          pollFilter === 'all' && styles.activeFilterButton
                        ]}
                        onPress={() => setPollFilter('all')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          pollFilter === 'all' && styles.activeFilterButtonText
                        ]}>
                          Todas
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          pollFilter === 'active' && styles.activeFilterButton
                        ]}
                        onPress={() => setPollFilter('active')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          pollFilter === 'active' && styles.activeFilterButtonText
                        ]}>
                          Activas
                        </Text>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.filterButton,
                          pollFilter === 'closed' && styles.activeFilterButton
                        ]}
                        onPress={() => setPollFilter('closed')}
                      >
                        <Text style={[
                          styles.filterButtonText,
                          pollFilter === 'closed' && styles.activeFilterButtonText
                        ]}>
                          Cerradas
                        </Text>
                      </TouchableOpacity>

                      {isReferente() && (
                        <TouchableOpacity
                          style={[
                            styles.filterButton,
                            pollFilter === 'draft' && styles.activeFilterButton
                          ]}
                          onPress={() => setPollFilter('draft')}
                        >
                          <Text style={[
                            styles.filterButtonText,
                            pollFilter === 'draft' && styles.activeFilterButtonText
                          ]}>
                            Borradores
                          </Text>
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>

                  {filteredPolls().length > 0 ? (
                    filteredPolls().map(poll => (
                      <PollCard
                        key={poll.id}
                        poll={poll}
                        hasVoted={!!(pollsStore?.getUserVote && pollsStore.getUserVote(poll.id))}
                        onPress={() => router.push(`/group/${id}/poll/${poll.id}`)}
                      />
                    ))
                  ) : (
                    <EmptyState
                      title="No hay votaciones"
                      message={pollFilter !== 'all' ? 'No hay votaciones en esta categoría' : 'No hay votaciones en este grupo'}
                      icon={<VoteIcon size={40} color={colors.textSecondary} />}
                    />
                  )}
                </View>
              )}

              {/* Calendar Tab Content */}
              {activeTab === 'calendar' && (
                <View style={styles.contentSection}>
                  <CalendarView
                    groupId={id}
                    announcements={announcements?.filter(a => a.groupId === id) || []}
                    colectas={colectas?.filter(c => c.groupId === id) || []}
                    kids={kidsInGroup()}
                    events={events?.filter(e => e.groupId === id) || []}
                  />
                </View>
              )}
            </>
          )}
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  groupInfoCard: {
    marginBottom: 24,
    padding: 16,
  },
  groupHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  groupName: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  groupDetail: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  badgeContainer: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
  },
  accessWarning: {
    marginTop: 8,
    padding: 12,
    backgroundColor: colors.secondary,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: colors.warning,
  },
  accessWarningText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 12,
  },
  payButton: {
    alignSelf: 'flex-start',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  membersRow: {
    flexDirection: 'row',
    gap: 12,
  },
  memberCardContainer: {
    flex: 1,
  },
  memberCard: {
    padding: 12,
    alignItems: 'center',
  },
  memberCardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  memberCardCount: {
    fontSize: 24,
    fontWeight: '700',
    color: colors.primary,
    textAlign: 'center',
    marginBottom: 0,
  },

  tabsContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    gap: 8,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  activeTabText: {
    color: colors.primary,
    fontWeight: '500',
  },
  contentSection: {
    marginBottom: 24,
  },
  newButton: {
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    padding: 16,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    backgroundColor: colors.card,
    borderWidth: 1,
    borderColor: colors.border,
  },
  activeFilterButton: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterButtonText: {
    fontSize: 12,
    color: colors.text,
  },
  activeFilterButtonText: {
    color: colors.background,
    fontWeight: '500',
  },

});