import React, { useEffect, useState } from 'react';
import { View, ActivityIndicator, StyleSheet, Text, Button } from 'react-native';
import { Redirect, router } from 'expo-router';
import { useAuthStore } from '@/store/authStore';
import { colors } from '@/constants/colors';

export default function SplashScreen() {
  const { isAuthenticated, currentUser, checkAuth, isLoading, logout } = useAuthStore();
  const [authChecked, setAuthChecked] = useState(false);
  const [forceLogout, setForceLogout] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [authError, setAuthError] = useState<string | null>(null);

  // Log the initial state from cache on component mount, before any effects run
  console.log('SplashScreen: INITIAL LOAD - Auth state from cache:', {
    isAuthenticated,
    hasUser: !!currentUser,
    userId: currentUser?.id,
    email: currentUser?.email,
    isLoading
  });

  // Add a timeout to show debug info if loading takes too long
  useEffect(() => {
    const timer = setTimeout(() => {
      if (isLoading) {
        console.log('SplashScreen: Loading timeout - showing debug view');
        setShowDebug(true);
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [isLoading]);

  useEffect(() => {
    console.log('SplashScreen: Initial mount, checking auth');
    const checkAuthentication = async () => {
      try {
        if (forceLogout) {
          console.log('SplashScreen: Force logout initiated');
          await logout();
          console.log('SplashScreen: Force logout complete');
          setForceLogout(false);
          // Clear any auth errors after successful logout
          setAuthError(null);
        } else {
          await checkAuth();
        }

        console.log('SplashScreen: Auth check complete', {
          isAuthenticated,
          hasUser: !!currentUser,
          userId: currentUser?.id,
          email: currentUser?.email
        });
      } catch (error) {
        console.log('SplashScreen: Auth check error', error);
        setAuthError(error instanceof Error ? error.message : 'Authentication error');
      } finally {
        setAuthChecked(true);
      }
    };

    checkAuthentication();
  }, [forceLogout]); // Run when component mounts or forceLogout changes

  // Show debug view after timeout or when there's an error
  if (showDebug || authError) {
    return (
      <View style={styles.container}>
        <Text style={styles.debugTitle}>
          {authError ? 'Authentication Error' : 'App is stuck loading'}
        </Text>
        {authError && <Text style={styles.errorText}>{authError}</Text>}
        <Text style={styles.debugText}>Auth state: {isAuthenticated ? 'Authenticated' : 'Not authenticated'}</Text>
        <Text style={styles.debugText}>User: {currentUser ? `${currentUser.email}` : 'None'}</Text>
        <Text style={styles.debugText}>Loading: {isLoading ? 'Yes' : 'No'}</Text>
        <Text style={styles.debugText}>Auth checked: {authChecked ? 'Yes' : 'No'}</Text>

        <View style={styles.buttonContainer}>
          <Button
            title="Test Network"
            onPress={async () => {
              try {
                console.log('Testing network connection...');
                const response = await fetch('https://jbvwbuzrqzprapjtdcsc.supabase.co/rest/v1/', {
                  method: 'GET',
                  headers: {
                    'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpidndidXpycXpwcmFwanRkY3NjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQxNjY0MzUsImV4cCI6MjA1OTc0MjQzNX0.7c0yORQu9rAs7yR4CJj9BvtgOUJa05AWnjF9u1aOnO8',
                    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpidndidXpycXpwcmFwanRkY3NjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQxNjY0MzUsImV4cCI6MjA1OTc0MjQzNX0.7c0yORQu9rAs7yR4CJj9BvtgOUJa05AWnjF9u1aOnO8'
                  }
                });
                console.log('Network test response status:', response.status);
                console.log('Network test successful! Status:', response.status);
              } catch (error) {
                console.error('Network test failed:', error);
              }
            }}
            color={colors.secondary}
          />
          <Button
            title="Force Logout"
            onPress={() => setForceLogout(true)}
            color={colors.error}
          />
          <Button
            title="Go to Login"
            onPress={() => router.replace('/(auth)/login')}
            color={colors.primary}
          />
        </View>
      </View>
    );
  }

  if (isLoading && !authChecked) {
    console.log('SplashScreen: Still loading auth state');
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  // Only redirect after authChecked is true
  if (!authChecked) {
    return null;
  }
  if (!isAuthenticated || !currentUser) {
    return <Redirect href="/(auth)/login" />;
  }
  return <Redirect href="/(tabs)" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  debugTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: colors.error,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 16,
    color: colors.error,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  debugText: {
    fontSize: 14,
    marginBottom: 8,
    color: colors.text,
  },
  buttonContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    width: '80%',
    marginTop: 20,
    gap: 10,
  }
});
