import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, RefreshControl } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Edit, Trash2, Users, Calendar, CreditCard, UserPlus, RefreshCw } from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { Avatar } from '@/components/Avatar';
import { GroupCard } from '@/components/GroupCard';
import { colors } from '@/constants/colors';
import { useKidsStore } from '@/store/kidsStore';
import { useGroupsStore } from '@/store/groupsStore';
import { useToastStore } from '@/store/toastStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { clearCache } from '@/utils/cacheUtils';

export default function KidDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { kids, deleteKid, getKids } = useKidsStore();
  const { groups, memberships, searchGroups, allGroups } = useGroupsStore();
  const { showToast } = useToastStore();
  const [kidGroups, setKidGroups] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Find the current kid
  const kid = kids.find(k => k.id === id);

  // Find the kid's groups
  const getKidGroups = () => {
    if (!id || !memberships || !groups) return [];
    const kidMemberships = memberships.filter(m => m.kidId === id);
    const kidGroupIds = kidMemberships.map(m => m.groupId);
    return groups.filter(g => kidGroupIds.includes(g.id));
  };

  // Get the kid's groups
  const kidGroupsData = getKidGroups();

  // Effect to load data when the component mounts or id changes
  useEffect(() => {
    let isMounted = true;
    const loadData = async (forceRefresh = false) => {
      if (!isMounted) return;

      console.log('KidDetailScreen: Loading data for kid:', id);
      setIsLoading(true);

      try {
        const loadPromises = [];

        // Only fetch fresh data if explicitly requested
        console.log('KidDetailScreen: Loading kids data...');
        loadPromises.push(getKids(forceRefresh));

        // Only fetch fresh groups data if explicitly requested
        console.log('KidDetailScreen: Loading groups data...');
        loadPromises.push(searchGroups(forceRefresh));

        // Wait for all data to load in parallel
        if (loadPromises.length > 0) {
          await Promise.all(loadPromises);
          if (!isMounted) return;
          console.log('KidDetailScreen: All data loaded successfully');
        }

        // Set kid groups only if component is still mounted
        if (isMounted) {
          const groups = getKidGroups();
          setKidGroups(groups);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('KidDetailScreen: Error loading data:', error);
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadData(false); // Load data without forcing refresh

    // Cleanup function
    return () => {
      isMounted = false;
      console.log('KidDetailScreen: Unmounting');
    };
  }, [id]); // Only re-run if id changes

  // Function to handle manual refresh
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await getKids(true); // Force refresh
      const groups = getKidGroups();
      setKidGroups(groups);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Function to handle force refresh with cache clearing
  const handleForceRefresh = async () => {
    try {
      // Show confirmation dialog
      Alert.alert(
        "Forzar actualización",
        "¿Estás seguro que quieres limpiar el caché y actualizar los datos?",
        [
          { text: "Cancelar", style: "cancel" },
          {
            text: "Actualizar",
            onPress: async () => {
              setRefreshing(true);
              // Clear the kids cache
              await clearCache('kids');
              // Fetch fresh data
              await getKids(true);
              const groups = getKidGroups();
              setKidGroups(groups);
              setRefreshing(false);
              showToast({
                message: "Datos actualizados correctamente",
                type: "success"
              });
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error forcing refresh:', error);
      showToast({
        message: "Error al actualizar los datos",
        type: "error"
      });
      setRefreshing(false);
    }
  };

  const formatBirthDate = (dateString?: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const handleDeleteKid = () => {
    Alert.alert(
      'Eliminar Hijo',
      '¿Estás seguro que deseas eliminar a este hijo? Esta acción no se puede deshacer.',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Eliminar',
          onPress: async () => {
            try {
              await deleteKid(id);
              router.replace('/(tabs)/kids');
            } catch (error) {
              Alert.alert('Error', 'No se pudo eliminar al hijo. Intenta nuevamente.');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Detalle del Hijo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.loadingText}>Cargando...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!kid) {
    return (
      <SafeAreaView style={styles.container}>
        <Stack.Screen options={{ title: 'Detalle del Hijo' }} />
        <View style={styles.centerContainer}>
          <Text style={styles.errorText}>Hijo no encontrado</Text>
          <Button
            title="Volver"
            onPress={() => router.back()}
            variant="outline"
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Detalle del Hijo',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          }>
          <View style={styles.profileHeader}>
            <Avatar
              initials={kid.fullName.split(' ').map(n => n[0]).join('').substring(0, 2)}
              size={80}
              backgroundColor={colors.primary}
            />

            <View style={styles.profileActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleForceRefresh}
              >
                <RefreshCw size={20} color={colors.primary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => router.push(`/kids/edit/${id}`)}
              >
                <Edit size={20} color={colors.primary} />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleDeleteKid}
              >
                <Trash2 size={20} color={colors.error} />
              </TouchableOpacity>
            </View>
          </View>

          <Text style={styles.kidName}>{kid.fullName}</Text>
          {kid.nickname && <Text style={styles.kidNickname}>({kid.nickname})</Text>}

          <Card variant="outlined" style={styles.infoCard}>
            <View style={styles.infoItem}>
              <Calendar size={20} color={colors.textSecondary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>Fecha de Nacimiento</Text>
                <Text style={styles.infoValue}>{formatBirthDate(kid.birthDate)}</Text>
              </View>
            </View>

            <View style={styles.infoItem}>
              <CreditCard size={20} color={colors.textSecondary} />
              <View style={styles.infoContent}>
                <Text style={styles.infoLabel}>DNI</Text>
                <Text style={styles.infoValue}>{kid.dni}</Text>
              </View>
            </View>
          </Card>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Grupos</Text>

            {kidGroups.length > 0 ? (
              kidGroups.map(group => (
                <GroupCard
                  key={group.id}
                  institutionName={group.institutionName || 'Institución'}
                  sala={group.sala}
                  año={group.año}
                  nombre={group.nombre}
                  memberCount={0}
                  kidCount={0}
                  onPress={() => router.push(`/group/${group.id}`)}
                />
              ))
            ) : (
              <Card variant="outlined" style={styles.emptyCard}>
                <Text style={styles.emptyText}>
                  No está en ningún grupo todavía
                </Text>
                <Button
                  title="Unirse a un Grupo"
                  onPress={() => router.push('/(tabs)/groups')}
                  variant="outline"
                  icon={<UserPlus size={18} color={colors.primary} />}
                  style={styles.joinButton}
                />
              </Card>
            )}
          </View>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  centerContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  errorText: {
    fontSize: 16,
    color: colors.error,
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  backButton: {
    minWidth: 120,
  },
  scrollContent: {
    padding: 16,
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  profileActions: {
    flexDirection: 'row',
    gap: 16,
  },
  actionButton: {
    padding: 8,
    backgroundColor: colors.cardBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  kidName: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  kidNickname: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 16,
  },
  infoCard: {
    marginBottom: 24,
    padding: 16,
  },
  infoItem: {
    flexDirection: 'row',
    marginBottom: 16,
    alignItems: 'center',
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  infoValue: {
    fontSize: 16,
    color: colors.text,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  emptyCard: {
    padding: 16,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  joinButton: {
    minWidth: 200,
  },
});