import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, CreditCard } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { DatePicker } from '@/components/DatePicker';
import { colors } from '@/constants/colors';
import { useKidsStore } from '@/store/kidsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function EditKidScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { kids, updateKid } = useKidsStore();

  const [dni, setDni] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [nickname, setNickname] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [localLoading, setLocalLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Track if initial values have been set
  const [initialValuesSet, setInitialValuesSet] = useState(false);

  useEffect(() => {
    // Only set initial values once
    if (id && !initialValuesSet) {
      const kid = kids.find(k => k.id === id);
      if (kid) {
        setDni(kid.dni || '');
        setFirstName(kid.firstName);
        setLastName(kid.lastName);
        setNickname(kid.nickname || '');
        setBirthDate(kid.birthDate || '');
        setInitialValuesSet(true);
      } else {
        Alert.alert('Error', 'No se encontró el niño', [
          { text: 'OK', onPress: () => router.back() }
        ]);
      }
    }
  }, [id, kids, initialValuesSet]);

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!dni) newErrors.dni = 'El DNI es requerido';
    if (!firstName) newErrors.firstName = 'El nombre es requerido';
    if (!lastName) newErrors.lastName = 'El apellido es requerido';
    if (!birthDate) newErrors.birthDate = 'La fecha de nacimiento es requerida';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateKid = async () => {
    if (!validate()) return;

    setLocalLoading(true);
    try {
      await updateKid(id, {
        dni,
        firstName,
        lastName,
        nickname: nickname || undefined,
        birthDate
      });

      Alert.alert(
        'Información Actualizada',
        'Los datos del niño han sido actualizados exitosamente.',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'No se pudo actualizar la información. Intenta nuevamente.');
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Editar Hijo',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Actualiza la información de tu hijo
            </Text>

            <Input
              label="DNI"
              placeholder="Ingresa el DNI"
              value={dni}
              onChangeText={(text) => {
                setDni(text.replace(/[^0-9]/g, ''));
                if (errors.dni) setErrors({...errors, dni: ''});
              }}
              keyboardType="numeric"
              leftIcon={<CreditCard size={20} color={colors.textSecondary} />}
              error={errors.dni}
            />

            <Input
              label="Nombre"
              placeholder="Ingresa el nombre"
              value={firstName}
              onChangeText={(text) => {
                setFirstName(text);
                if (errors.firstName) setErrors({...errors, firstName: ''});
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.firstName}
            />

            <Input
              label="Apellido"
              placeholder="Ingresa el apellido"
              value={lastName}
              onChangeText={(text) => {
                setLastName(text);
                if (errors.lastName) setErrors({...errors, lastName: ''});
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.lastName}
            />

            <Input
              label="Apodo (opcional)"
              placeholder="Ingresa el apodo"
              value={nickname}
              onChangeText={setNickname}
              leftIcon={<User size={20} color={colors.textSecondary} />}
            />

            <DatePicker
              label="Fecha de Nacimiento"
              value={birthDate}
              onChange={(date) => {
                setBirthDate(date);
                if (errors.birthDate) setErrors({...errors, birthDate: ''});
              }}
              error={errors.birthDate}
            />

            <Button
              title="Guardar Cambios"
              onPress={handleUpdateKid}
              loading={localLoading}
              style={styles.updateButton}
            />

            <Button
              title="Cancelar"
              onPress={() => router.back()}
              variant="outline"
              style={styles.cancelButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  updateButton: {
    marginTop: 24,
  },
  cancelButton: {
    marginTop: 12,
    marginBottom: 16,
  },
});