import React, { useState } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView, Alert } from 'react-native';
import { router, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { CreditCard, Link } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useKidsStore } from '@/store/kidsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { DatePicker } from '@/components/DatePicker';
import { useToastStore } from '@/store/toastStore';
import { KidsService } from '@/services/kidsService';
import { useAuthStore } from '@/store/authStore';

export default function LinkQidScreen() {
  const [dni, setDni] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [localLoading, setLocalLoading] = useState(false);

  const { showToast } = useToastStore();
  const { getKids } = useKidsStore();
  const { currentUser } = useAuthStore();

  const validate = () => {
    const newErrors: Record<string, string> = {};
    if (!dni) newErrors.dni = 'El DNI es requerido';
    else if (!/^[0-9]{7,8}$/.test(dni)) newErrors.dni = 'El DNI debe tener 7 u 8 dígitos';
    if (!birthDate) newErrors.birthDate = 'La fecha de nacimiento es requerida';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLinkQid = async () => {
    if (!validate()) return;
    setLocalLoading(true);
    try {
      // Buscar el Qid existente por DNI y fecha de nacimiento
      const foundKids = await KidsService.findKidByDniAndBirthDate(dni, birthDate);
      if (!foundKids || foundKids.length === 0) {
        showToast('No se encontró ningún Qid registrado con esos datos.', 'error');
        setLocalLoading(false);
        return;
      }
      // Mapear a camelCase
      const kidRaw = foundKids[0] as any;
      const kid = {
        id: kidRaw.id,
        parentId: kidRaw.parent_id,
        fullName: kidRaw.full_name,
        birthDate: kidRaw.birth_date,
        dni: kidRaw.dni,
      };
      // Validar si ya está vinculado a este adulto (muchos a muchos)
      const parentIds = await KidsService.getParentsForKid(kid.id);
      if (parentIds.includes(currentUser.id)) {
        showToast('Este Qid ya está vinculado a tu cuenta.', 'info');
        setLocalLoading(false);
        return;
      }
      // Confirmar con el usuario (usar los campos camelCase)
      Alert.alert(
        'Confirmar vinculación',
        `¿Desea vincular a ${kid.fullName} (DNI: ${kid.dni}, Nacimiento: ${new Date(kid.birthDate).toLocaleDateString('es-AR')}) a su cuenta?`,
        [
          { text: 'Cancelar', style: 'cancel', onPress: () => setLocalLoading(false) },
          {
            text: 'Vincular',
            style: 'default',
            onPress: async () => {
              try {
                await KidsService.linkKidToParent(kid.id, currentUser.id);
                showToast('Qid vinculado exitosamente', 'success');
                await getKids(true);
                router.replace('/(tabs)/profile');
              } catch (err: any) {
                setLocalLoading(false);
                showToast(err?.message || 'Error inesperado al vincular Qid.', 'error');
                console.error('[VincularQid] Error:', err);
              }
            },
          },
        ]
      );
    } catch (err: any) {
      setLocalLoading(false);
      showToast(err?.message || 'Error inesperado al vincular Qid.', 'error');
      console.error('[VincularQid] Error:', err);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Vincular Qid',
          headerBackTitle: 'Atrás',
        }}
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Vincula un Qid que ya está registrado en la plataforma
            </Text>
            <Input
              label="DNI"
              placeholder="Ej: 12345678"
              value={dni}
              onChangeText={(text) => {
                setDni(text.replace(/[^0-9]/g, ''));
                if (errors.dni) setErrors({ ...errors, dni: '' });
              }}
              keyboardType="numeric"
              leftIcon={<CreditCard size={20} color={colors.textSecondary} />}
              error={errors.dni}
            />
            <DatePicker
              label="Fecha de Nacimiento"
              value={birthDate}
              onChange={(date) => {
                setBirthDate(date);
                if (errors.birthDate) setErrors({ ...errors, birthDate: '' });
              }}
              error={errors.birthDate}
            />
            <Button
              title="Vincular Qid"
              onPress={handleLinkQid}
              loading={localLoading}
              icon={<Link size={18} color={colors.background} />}
              style={styles.linkButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  linkButton: {
    marginTop: 16,
  },
});