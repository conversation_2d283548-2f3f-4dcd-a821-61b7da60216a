import React, { useState } from 'react';
import { View, Text, StyleSheet, KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { router, Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { User, CreditCard, Calendar } from 'lucide-react-native';
import { Input } from '@/components/Input';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useKidsStore } from '@/store/kidsStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { DatePicker } from '@/components/DatePicker';
import { useToastStore } from '@/store/toastStore';

export default function AddKidScreen() {
  const [dni, setDni] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [nickname, setNickname] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [localLoading, setLocalLoading] = useState(false);

  const { addKid, error, clearError } = useKidsStore();
  const { showToast } = useToastStore();

  const validate = () => {
    const newErrors: Record<string, string> = {};

    if (!dni) newErrors.dni = 'El DNI es requerido';
    else if (!/^\d{7,8}$/.test(dni)) newErrors.dni = 'El DNI debe tener 7 u 8 dígitos';

    if (!firstName) newErrors.firstName = 'El nombre es requerido';
    if (!lastName) newErrors.lastName = 'El apellido es requerido';
    if (!birthDate) newErrors.birthDate = 'La fecha de nacimiento es requerida';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddKid = async () => {
    if (!validate()) return;

    setLocalLoading(true);
    try {
      const newKid = await addKid({
        dni,
        firstName,
        lastName,
        nickname: nickname || undefined,
        birthDate: birthDate ? birthDate.split('T')[0] : ''
      });

      showToast(`${firstName} ${lastName} agregado exitosamente`, 'success');

      // Wait a moment to ensure the kid is in the store before navigating
      setTimeout(() => {
        // Navigate to the kid detail page
        router.replace(`/kids/${newKid.id}`);
      }, 300);
    } catch (err) {
      if (err instanceof Error) {
        showToast(err.message, 'error');
      } else {
        showToast('No se pudo agregar al hijo. Intenta nuevamente.', 'error');
      }
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Agregar Hijo',
          headerBackTitle: 'Atrás',
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <AnimatedTransition>
          <ScrollView contentContainerStyle={styles.scrollContent}>
            <Text style={styles.subtitle}>
              Agrega a tu hijo para poder unirlo a grupos escolares
            </Text>

            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}

            <Input
              label="DNI"
              placeholder="Ej: 12345678"
              value={dni}
              onChangeText={(text) => {
                setDni(text.replace(/[^0-9]/g, ''));
                if (errors.dni) setErrors({...errors, dni: ''});
                if (error) clearError();
              }}
              keyboardType="numeric"
              maxLength={8}
              leftIcon={<CreditCard size={20} color={colors.textSecondary} />}
              error={errors.dni}
            />

            <Input
              label="Nombre"
              placeholder="Ej: Juan"
              value={firstName}
              onChangeText={(text) => {
                setFirstName(text);
                if (errors.firstName) setErrors({...errors, firstName: ''});
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.firstName}
            />

            <Input
              label="Apellido"
              placeholder="Ej: Pérez"
              value={lastName}
              onChangeText={(text) => {
                setLastName(text);
                if (errors.lastName) setErrors({...errors, lastName: ''});
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
              error={errors.lastName}
            />

            <Input
              label="Apodo (opcional)"
              placeholder="Ej: Juanito"
              value={nickname}
              onChangeText={(text) => {
                setNickname(text);
                if (error) clearError();
              }}
              leftIcon={<User size={20} color={colors.textSecondary} />}
            />

            <DatePicker
              label="Fecha de Nacimiento"
              value={birthDate}
              onChange={(date) => {
                setBirthDate(date);
                if (errors.birthDate) setErrors({...errors, birthDate: ''});
              }}
              error={errors.birthDate}
            />

            <Button
              title="Agregar Hijo"
              onPress={handleAddKid}
              loading={localLoading}
              style={styles.addButton}
            />
          </ScrollView>
        </AnimatedTransition>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    marginBottom: 24,
    lineHeight: 22,
  },
  errorContainer: {
    backgroundColor: '#FEE2E2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
  },
  dateContainer: {
    marginBottom: 16,
  },
  dateLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.text,
    marginBottom: 8,
  },
  dateButton: {
    alignSelf: 'flex-start',
  },
  addButton: {
    marginTop: 16,
  },
});