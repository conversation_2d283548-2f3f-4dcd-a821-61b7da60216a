import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { HelpCircle, Users, Bell, DollarSign, Calendar } from 'lucide-react-native';
import { Card } from '@/components/Card';
import { colors } from '@/constants/colors';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function HelpScreen() {
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: 'Ayuda',
          headerBackTitle: 'Atrás',
        }} 
      />
      
      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <HelpCircle size={40} color={colors.primary} />
            <Text style={styles.title}>Centro de Ayuda</Text>
            <Text style={styles.subtitle}>
              Encuentra respuestas a las preguntas más frecuentes
            </Text>
          </View>
          
          <Card variant="outlined" style={styles.helpCard}>
            <View style={styles.helpHeader}>
              <Users size={20} color={colors.primary} />
              <Text style={styles.helpTitle}>Grupos</Text>
            </View>
            <Text style={styles.helpText}>
              Los grupos te permiten conectar con otros padres y madres de la misma clase o escuela.
            </Text>
            <Text style={styles.helpQuestion}>¿Cómo crear un grupo?</Text>
            <Text style={styles.helpAnswer}>
              Ve a la pestaña "Grupos", toca el botón "+" y sigue las instrucciones para crear un nuevo grupo.
            </Text>
            <Text style={styles.helpQuestion}>¿Cómo unirme a un grupo existente?</Text>
            <Text style={styles.helpAnswer}>
              Busca el grupo en la pestaña "Grupos", selecciónalo y toca "Unirse al Grupo". Luego selecciona los hijos que quieres agregar.
            </Text>
          </Card>
          
          <Card variant="outlined" style={styles.helpCard}>
            <View style={styles.helpHeader}>
              <Bell size={20} color={colors.primary} />
              <Text style={styles.helpTitle}>Anuncios</Text>
            </View>
            <Text style={styles.helpText}>
              Los anuncios permiten compartir información importante con todos los miembros del grupo.
            </Text>
            <Text style={styles.helpQuestion}>¿Quién puede crear anuncios?</Text>
            <Text style={styles.helpAnswer}>
              Solo los referentes del grupo pueden crear anuncios. Si eres referente, verás la opción "Nuevo Anuncio" en la página del grupo.
            </Text>
          </Card>
          
          <Card variant="outlined" style={styles.helpCard}>
            <View style={styles.helpHeader}>
              <DollarSign size={20} color={colors.primary} />
              <Text style={styles.helpTitle}>Colectas</Text>
            </View>
            <Text style={styles.helpText}>
              Las colectas facilitan la recolección de dinero para regalos, eventos o necesidades del grupo.
            </Text>
            <Text style={styles.helpQuestion}>¿Cómo crear una colecta?</Text>
            <Text style={styles.helpAnswer}>
              Los referentes pueden crear colectas desde la pestaña "Colectas" en la página del grupo. Deberás especificar el motivo, monto y fecha límite.
            </Text>
            <Text style={styles.helpQuestion}>¿Cómo informar un pago?</Text>
            <Text style={styles.helpAnswer}>
              Abre la colecta, toca "Informar Pago" y sigue las instrucciones para registrar tu contribución.
            </Text>
          </Card>
          
          <Card variant="outlined" style={styles.helpCard}>
            <View style={styles.helpHeader}>
              <Calendar size={20} color={colors.primary} />
              <Text style={styles.helpTitle}>Calendario</Text>
            </View>
            <Text style={styles.helpText}>
              El calendario muestra eventos importantes como anuncios, fechas límite de colectas y cumpleaños.
            </Text>
            <Text style={styles.helpQuestion}>¿Qué eventos se muestran en el calendario?</Text>
            <Text style={styles.helpAnswer}>
              El calendario muestra anuncios programados, fechas límite de colectas y cumpleaños de los niños del grupo.
            </Text>
          </Card>
          
          <Text style={styles.contactText}>
            ¿Necesitas más ayuda? Contá<NAME_EMAIL>
          </Text>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  helpCard: {
    marginBottom: 16,
    padding: 16,
  },
  helpHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  helpTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
  },
  helpText: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 16,
    lineHeight: 20,
  },
  helpQuestion: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 4,
  },
  helpAnswer: {
    fontSize: 14,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  contactText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 24,
  },
});