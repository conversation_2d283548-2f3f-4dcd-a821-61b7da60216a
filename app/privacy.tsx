import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Stack } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Shield } from 'lucide-react-native';
import { colors } from '@/constants/colors';
import { AnimatedTransition } from '@/components/AnimatedTransition';

export default function PrivacyPolicyScreen() {
  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen 
        options={{ 
          title: 'Política de Privacidad',
          headerBackTitle: 'Atrás',
        }} 
      />
      
      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Shield size={40} color={colors.primary} />
            <Text style={styles.title}>Política de Privacidad</Text>
            <Text style={styles.date}>Última actualización: 1 de junio de 2024</Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>1. Información que Recopilamos</Text>
            <Text style={styles.paragraph}>
              Recopilamos información personal que usted nos proporciona directamente, como:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>• Información de registro: nombre, correo electrónico, contraseña</Text>
              <Text style={styles.bulletItem}>• Información de perfil: número de teléfono (opcional)</Text>
              <Text style={styles.bulletItem}>• Información sobre sus hijos: nombre, fecha de nacimiento, DNI</Text>
              <Text style={styles.bulletItem}>• Información de grupos escolares</Text>
              <Text style={styles.bulletItem}>• Información de pagos y colectas</Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>2. Cómo Utilizamos su Información</Text>
            <Text style={styles.paragraph}>
              Utilizamos la información recopilada para:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>• Proporcionar, mantener y mejorar nuestros servicios</Text>
              <Text style={styles.bulletItem}>• Facilitar la comunicación entre padres y madres</Text>
              <Text style={styles.bulletItem}>• Gestionar grupos, anuncios y colectas</Text>
              <Text style={styles.bulletItem}>• Enviar notificaciones relacionadas con la actividad en sus grupos</Text>
              <Text style={styles.bulletItem}>• Prevenir fraudes y garantizar la seguridad</Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>3. Compartición de Información</Text>
            <Text style={styles.paragraph}>
              Compartimos su información en las siguientes circunstancias:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>• Con otros miembros de los grupos a los que pertenece</Text>
              <Text style={styles.bulletItem}>• Con proveedores de servicios que nos ayudan a operar la aplicación</Text>
              <Text style={styles.bulletItem}>• Cuando sea requerido por ley o para proteger derechos legales</Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>4. Seguridad de la Información</Text>
            <Text style={styles.paragraph}>
              Implementamos medidas de seguridad diseñadas para proteger su información personal, 
              incluyendo encriptación de datos y protocolos de seguridad. Sin embargo, ningún sistema 
              es completamente seguro, y no podemos garantizar la seguridad absoluta de su información.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>5. Sus Derechos</Text>
            <Text style={styles.paragraph}>
              Usted tiene derecho a:
            </Text>
            <View style={styles.bulletList}>
              <Text style={styles.bulletItem}>• Acceder a su información personal</Text>
              <Text style={styles.bulletItem}>• Corregir información inexacta</Text>
              <Text style={styles.bulletItem}>• Eliminar su cuenta y datos asociados</Text>
              <Text style={styles.bulletItem}>• Oponerse al procesamiento de sus datos</Text>
              <Text style={styles.bulletItem}>• Retirar su consentimiento en cualquier momento</Text>
            </View>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>6. Cambios a esta Política</Text>
            <Text style={styles.paragraph}>
              Podemos actualizar esta política de privacidad periódicamente. Le notificaremos 
              cualquier cambio material publicando la nueva política de privacidad en esta página 
              y, cuando sea apropiado, a través de notificaciones en la aplicación.
            </Text>
          </View>
          
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>7. Contacto</Text>
            <Text style={styles.paragraph}>
              Si tiene preguntas sobre esta política de privacidad, por favor contáctenos en:
              <EMAIL>
            </Text>
          </View>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '600',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  date: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
  },
  paragraph: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 12,
    lineHeight: 20,
  },
  bulletList: {
    marginLeft: 8,
  },
  bulletItem: {
    fontSize: 14,
    color: colors.text,
    marginBottom: 8,
    lineHeight: 20,
  },
});