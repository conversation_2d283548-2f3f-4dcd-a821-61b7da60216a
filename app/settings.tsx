import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Switch, Alert } from 'react-native';
import { Stack, router } from 'expo-router';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  Bell,
  Moon,
  Globe,
  Shield,
  HelpCircle,
  FileText,
  LogOut,
  ChevronRight
} from 'lucide-react-native';
import { Card } from '@/components/Card';
import { Button } from '@/components/Button';
import { colors } from '@/constants/colors';
import { useSettingsStore } from '@/store/settingsStore';
import { useAuthStore } from '@/store/authStore';
import { AnimatedTransition } from '@/components/AnimatedTransition';
import { clearAllStorage, clearColectasStorage } from '@/utils/clearStorage';

export default function SettingsScreen() {
  const {
    darkMode,
    toggleDarkMode,
    language,
    setLanguage,
    notificationsEnabled,
    toggleNotifications
  } = useSettingsStore();

  const { logout } = useAuthStore();

  const handleLogout = () => {
    Alert.alert(
      'Cerrar sesión',
      '¿Estás seguro de que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cerrar sesión',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              router.replace('/(auth)/login');
            } catch (error) {
              console.error('Logout error:', error);
              // Even if logout fails, redirect to login
              router.replace('/(auth)/login');
            }
          }
        }
      ]
    );
  };

  const handleLanguageChange = () => {
    const newLanguage = language === 'es' ? 'en' : 'es';
    setLanguage(newLanguage);
  };

  const handleClearColectasStorage = () => {
    Alert.alert(
      'Limpiar Cache de Colectas',
      '¿Estás seguro que deseas limpiar el cache de colectas? Esto eliminará todos los datos locales de colectas y contribuciones.',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Limpiar',
          onPress: async () => {
            try {
              await clearColectasStorage();
              Alert.alert('Éxito', 'Cache de colectas limpiado correctamente');
            } catch (error) {
              Alert.alert('Error', 'No se pudo limpiar el cache de colectas');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  const handleClearAllStorage = () => {
    Alert.alert(
      'Limpiar Todo el Cache',
      '¿Estás seguro que deseas limpiar todo el cache? Esto eliminará todos los datos locales y tendrás que iniciar sesión nuevamente.',
      [
        {
          text: 'Cancelar',
          style: 'cancel',
        },
        {
          text: 'Limpiar',
          onPress: async () => {
            try {
              await clearAllStorage();
              logout();
              router.replace('/(auth)/login');
            } catch (error) {
              Alert.alert('Error', 'No se pudo limpiar el cache');
            }
          },
          style: 'destructive',
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <Stack.Screen
        options={{
          title: 'Configuración',
          headerBackTitle: 'Atrás',
        }}
      />

      <AnimatedTransition>
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <Text style={styles.sectionTitle}>Apariencia</Text>

          <Card variant="outlined" style={styles.settingCard}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Moon size={20} color={colors.textSecondary} />
                <Text style={styles.settingLabel}>Modo Oscuro</Text>
              </View>
              <Switch
                value={darkMode}
                onValueChange={toggleDarkMode}
                trackColor={{ false: colors.border, true: colors.primaryLight }}
                thumbColor={darkMode ? colors.primary : colors.background}
              />
            </View>
          </Card>

          <Text style={styles.sectionTitle}>Preferencias</Text>

          <Card variant="outlined" style={styles.settingCard}>
            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Globe size={20} color={colors.textSecondary} />
                <Text style={styles.settingLabel}>Idioma</Text>
              </View>
              <View style={styles.settingValue}>
                <Text style={styles.valueText}>
                  {language === 'es' ? 'Español' : 'English'}
                </Text>
                <ChevronRight size={16} color={colors.textSecondary} />
              </View>
            </View>

            <View style={styles.settingItem}>
              <View style={styles.settingInfo}>
                <Bell size={20} color={colors.textSecondary} />
                <Text style={styles.settingLabel}>Notificaciones</Text>
              </View>
              <Switch
                value={notificationsEnabled}
                onValueChange={toggleNotifications}
                trackColor={{ false: colors.border, true: colors.primaryLight }}
                thumbColor={notificationsEnabled ? colors.primary : colors.background}
              />
            </View>
          </Card>

          <Text style={styles.sectionTitle}>Información</Text>

          <Card variant="outlined" style={styles.settingCard}>
            <Button
              title="Ayuda"
              onPress={() => router.push('/help')}
              variant="text"
              icon={<HelpCircle size={20} color={colors.text} />}
              style={styles.textButton}
              textStyle={styles.textButtonLabel}
            />

            <Button
              title="Política de Privacidad"
              onPress={() => router.push('/privacy')}
              variant="text"
              icon={<Shield size={20} color={colors.text} />}
              style={styles.textButton}
              textStyle={styles.textButtonLabel}
            />

            <Button
              title="Términos y Condiciones"
              onPress={() => router.push('/terms')}
              variant="text"
              icon={<FileText size={20} color={colors.text} />}
              style={styles.textButton}
              textStyle={styles.textButtonLabel}
            />
          </Card>

          <Card style={{ marginTop: 24 }}>
            <Text style={styles.sectionTitle}>Desarrollo</Text>
            <Button
              title="Limpiar Cache de Colectas"
              onPress={handleClearColectasStorage}
              variant="outline"
              style={{ marginBottom: 12 }}
            />
            <Button
              title="Limpiar Todo el Cache"
              onPress={handleClearAllStorage}
              variant="outline"
              style={{ marginBottom: 12 }}
            />
          </Card>

          <Button
            title="Cerrar Sesión"
            onPress={handleLogout}
            variant="outline"
            icon={<LogOut size={20} color={colors.error} />}
            style={styles.logoutButton}
            textStyle={styles.logoutText}
          />

          <Text style={styles.versionText}>Versión 1.0.0</Text>
        </ScrollView>
      </AnimatedTransition>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollContent: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text,
    marginBottom: 12,
    marginTop: 16,
  },
  settingCard: {
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  settingLabel: {
    fontSize: 16,
    color: colors.text,
  },
  settingValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  valueText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  textButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  textButtonLabel: {
    fontSize: 16,
    color: colors.text,
    marginLeft: 12,
  },
  logoutButton: {
    marginTop: 24,
    borderColor: colors.error,
  },
  logoutText: {
    color: colors.error,
  },
  versionText: {
    fontSize: 14,
    color: colors.textSecondary,
    textAlign: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
});